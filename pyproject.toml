[tool.poetry]
name = "athlea-langgraph"
version = "0.1.0"
description = "Python LangGraph implementation for Athlea coaching"
authors = ["Athlea Team"]
readme = "README.md"
packages = [{include = "athlea_langgraph"}]

[tool.poetry.dependencies]
python = ">=3.11,<3.14"
langgraph = "^0.4.7"
langchain = "^0.3.25"
langchain-openai = "^0.3.18"
python-dotenv = "^1.1.0"
aiohttp = "^3.10.0"
pydantic = "^2.10.0"
pymongo = "^4.10.0"
motor = "^3.6.0"
azure-identity = "^1.19.0"
requests = "^2.32.0"
typing-extensions = "^4.12.0"
langgraph-checkpoint-postgres = "^2.0.21"
uvicorn = "^0.34.3"
httpx = "^0.28.1"
prometheus-client = "^0.21.1"
azure-search-documents = "^11.5.2"
azure-core = "^1.34.0"
openai = "^1.82.0"
mcp = "^1.9.1"
pyairtable = "^3.1.1"
psutil = "^7.0.0"
mem0ai = "^0.1.102"
sentence-transformers = "^4.1.0"
fastapi = "0.115.9"
chromadb = "^1.0.11"
qdrant-client = "^1.14.2"
pinecone = ">=5.0.0,<7.0.0"
websockets = "14.1"
redis = "^5.0.0"
gremlinpython = "^3.7.3"
pydantic-settings = "^2.9.1"
langgraph-cli = {extras = ["inmem"], version = "^0.3.1"}
langchain-community = "^0.3.25"
duckduckgo-search = "^8.0.3"
beautifulsoup4 = "^4.12.3"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.24.0"
black = "^24.0.0"
isort = "^5.13.0"
mypy = "^1.8.0"
pytest-cov = "^6.1.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
# Temporarily more lenient - can be made stricter gradually
disallow_untyped_defs = false
# Ignore missing imports (common with langchain/langgraph)
ignore_missing_imports = true
# Allow untyped calls
disallow_untyped_calls = false
# Show error codes for easier fixing
show_error_codes = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
    "--color=yes",
    "--durations=10"
]
markers = [
    "unit: Unit tests - fast, isolated component tests",
    "integration: Integration tests - multi-component workflow tests", 
    "e2e: End-to-end tests - complete user journey tests",
    "slow: Tests that take longer than 30 seconds",
    "external: Tests that require external services"
]

[tool.coverage.run]
source = ["athlea_langgraph"]
omit = [
    "*/tests/*",
    "*/venv/*", 
    "*/__pycache__/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError", 
    "raise NotImplementedError"
]
