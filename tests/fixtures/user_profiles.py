"""
User Profile Fixtures for Testing

This module provides various user profile scenarios for testing the coaching agents.
It includes profiles for different fitness levels, goals, and user characteristics.
"""

from datetime import date, datetime
from enum import Enum
from typing import Any, Dict, List


class FitnessLevel(Enum):
    """Fitness level enumeration."""

    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    ELITE = "elite"


class Goal(Enum):
    """Common fitness goals."""

    WEIGHT_LOSS = "weight_loss"
    MUSCLE_GAIN = "muscle_gain"
    STRENGTH = "strength"
    ENDURANCE = "endurance"
    MARATHON = "marathon"
    POWERLIFTING = "powerlifting"
    BODYBUILDING = "bodybuilding"
    GENERAL_FITNESS = "general_fitness"
    ATHLETIC_PERFORMANCE = "athletic_performance"
    INJURY_RECOVERY = "injury_recovery"


class DietaryRestriction(Enum):
    """Common dietary restrictions."""

    VEGETARIAN = "vegetarian"
    VEGAN = "vegan"
    KETO = "keto"
    PALEO = "paleo"
    GLUTEN_FREE = "gluten_free"
    DAIRY_FREE = "dairy_free"
    LOW_CARB = "low_carb"
    MEDITERRANEAN = "mediterranean"


def create_basic_user_profile(
    name: str = "Test User",
    age: int = 30,
    fitness_level: FitnessLevel = FitnessLevel.BEGINNER,
    goals: List[Goal] = None,
    dietary_restrictions: List[DietaryRestriction] = None,
    **kwargs,
) -> Dict[str, Any]:
    """
    Create a basic user profile with common fields.

    Args:
        name: User's name
        age: User's age
        fitness_level: Current fitness level
        goals: List of fitness goals
        dietary_restrictions: List of dietary restrictions
        **kwargs: Additional profile fields

    Returns:
        Dictionary containing user profile data
    """
    if goals is None:
        goals = [Goal.GENERAL_FITNESS]
    if dietary_restrictions is None:
        dietary_restrictions = []

    profile = {
        "user_id": f"test_user_{name.lower().replace(' ', '_')}",
        "name": name,
        "age": age,
        "fitness_level": fitness_level.value,
        "goals": [goal.value for goal in goals],
        "dietary_restrictions": [dr.value for dr in dietary_restrictions],
        "created_at": datetime.now().isoformat(),
        "last_updated": datetime.now().isoformat(),
        **kwargs,
    }

    return profile


# Beginner user profiles
BEGINNER_WEIGHT_LOSS = create_basic_user_profile(
    name="Sarah Johnson",
    age=28,
    fitness_level=FitnessLevel.BEGINNER,
    goals=[Goal.WEIGHT_LOSS, Goal.GENERAL_FITNESS],
    dietary_restrictions=[DietaryRestriction.VEGETARIAN],
    height="5'6\"",
    weight="180 lbs",
    target_weight="150 lbs",
    activity_level="sedentary",
    workout_frequency="2-3 times per week",
    preferences=["low-impact exercises", "home workouts"],
    injuries=[],
    medical_conditions=[],
)

BEGINNER_MUSCLE_GAIN = create_basic_user_profile(
    name="Mike Chen",
    age=22,
    fitness_level=FitnessLevel.BEGINNER,
    goals=[Goal.MUSCLE_GAIN, Goal.STRENGTH],
    height="5'10\"",
    weight="140 lbs",
    target_weight="165 lbs",
    activity_level="lightly_active",
    workout_frequency="3-4 times per week",
    preferences=["gym workouts", "compound movements"],
    injuries=[],
    medical_conditions=[],
    gym_access=True,
)

BEGINNER_GENERAL_FITNESS = create_basic_user_profile(
    name="Lisa Rodriguez",
    age=35,
    fitness_level=FitnessLevel.BEGINNER,
    goals=[Goal.GENERAL_FITNESS, Goal.ENDURANCE],
    dietary_restrictions=[DietaryRestriction.GLUTEN_FREE],
    height="5'4\"",
    weight="140 lbs",
    activity_level="sedentary",
    workout_frequency="2-3 times per week",
    preferences=["outdoor activities", "group classes"],
    injuries=["knee pain (mild)"],
    medical_conditions=[],
    time_availability="30-45 minutes",
)

# Intermediate user profiles
INTERMEDIATE_RUNNER = create_basic_user_profile(
    name="David Park",
    age=31,
    fitness_level=FitnessLevel.INTERMEDIATE,
    goals=[Goal.MARATHON, Goal.ENDURANCE],
    height="5'8\"",
    weight="155 lbs",
    activity_level="very_active",
    workout_frequency="5-6 times per week",
    preferences=["running", "cross-training"],
    injuries=[],
    medical_conditions=[],
    running_experience="2 years",
    current_weekly_mileage="25-30 miles",
    best_5k_time="22:30",
    best_10k_time="47:15",
)

INTERMEDIATE_STRENGTH = create_basic_user_profile(
    name="Jessica Thompson",
    age=27,
    fitness_level=FitnessLevel.INTERMEDIATE,
    goals=[Goal.STRENGTH, Goal.POWERLIFTING],
    dietary_restrictions=[DietaryRestriction.LOW_CARB],
    height="5'7\"",
    weight="145 lbs",
    activity_level="very_active",
    workout_frequency="4-5 times per week",
    preferences=["powerlifting", "heavy compounds"],
    injuries=[],
    medical_conditions=[],
    lifting_experience="18 months",
    current_lifts={"squat": "185 lbs", "bench": "110 lbs", "deadlift": "225 lbs"},
    gym_access=True,
)

INTERMEDIATE_BODYBUILDING = create_basic_user_profile(
    name="Alex Martinez",
    age=29,
    fitness_level=FitnessLevel.INTERMEDIATE,
    goals=[Goal.BODYBUILDING, Goal.MUSCLE_GAIN],
    dietary_restrictions=[DietaryRestriction.PALEO],
    height="5'11\"",
    weight="175 lbs",
    target_weight="185 lbs",
    activity_level="very_active",
    workout_frequency="5-6 times per week",
    preferences=["bodybuilding splits", "isolation work"],
    injuries=[],
    medical_conditions=[],
    lifting_experience="2.5 years",
    body_fat_percentage="12%",
    gym_access=True,
)

# Advanced user profiles
ADVANCED_ATHLETE = create_basic_user_profile(
    name="Taylor Wilson",
    age=26,
    fitness_level=FitnessLevel.ADVANCED,
    goals=[Goal.ATHLETIC_PERFORMANCE, Goal.STRENGTH],
    height="6'0\"",
    weight="180 lbs",
    activity_level="extremely_active",
    workout_frequency="6-7 times per week",
    preferences=["periodized training", "sport-specific"],
    injuries=[],
    medical_conditions=[],
    sport="basketball",
    competitive_level="collegiate",
    training_experience="8 years",
)

ADVANCED_POWERLIFTER = create_basic_user_profile(
    name="Marcus Johnson",
    age=33,
    fitness_level=FitnessLevel.ADVANCED,
    goals=[Goal.POWERLIFTING, Goal.STRENGTH],
    dietary_restrictions=[DietaryRestriction.KETO],
    height="5'9\"",
    weight="220 lbs",
    activity_level="very_active",
    workout_frequency="4-5 times per week",
    preferences=["powerlifting", "competition prep"],
    injuries=[],
    medical_conditions=[],
    lifting_experience="6 years",
    current_lifts={"squat": "485 lbs", "bench": "335 lbs", "deadlift": "565 lbs"},
    competition_history=["2023 State Championships - 2nd place"],
    gym_access=True,
)

# Special case profiles
INJURY_RECOVERY = create_basic_user_profile(
    name="Robert Kim",
    age=45,
    fitness_level=FitnessLevel.INTERMEDIATE,
    goals=[Goal.INJURY_RECOVERY, Goal.GENERAL_FITNESS],
    height="5'10\"",
    weight="190 lbs",
    activity_level="lightly_active",
    workout_frequency="3-4 times per week",
    preferences=["low-impact", "rehabilitation exercises"],
    injuries=["ACL reconstruction (6 months ago)", "shoulder impingement"],
    medical_conditions=[],
    physical_therapy="completed 3 months ago",
    doctor_clearance=True,
    restrictions=["no jumping", "limited overhead movements"],
)

OLDER_ADULT = create_basic_user_profile(
    name="Helen Davis",
    age=62,
    fitness_level=FitnessLevel.BEGINNER,
    goals=[Goal.GENERAL_FITNESS, Goal.STRENGTH],
    dietary_restrictions=[DietaryRestriction.MEDITERRANEAN],
    height="5'3\"",
    weight="150 lbs",
    activity_level="lightly_active",
    workout_frequency="3 times per week",
    preferences=["gentle exercises", "balance training"],
    injuries=[],
    medical_conditions=["osteoporosis", "high blood pressure (controlled)"],
    balance_concerns=True,
    medication=["blood pressure medication"],
    doctor_clearance=True,
)

PREGNANT_USER = create_basic_user_profile(
    name="Emily Chang",
    age=30,
    fitness_level=FitnessLevel.INTERMEDIATE,
    goals=[Goal.GENERAL_FITNESS],
    height="5'5\"",
    weight="135 lbs",
    activity_level="moderately_active",
    workout_frequency="3-4 times per week",
    preferences=["prenatal safe exercises", "walking"],
    injuries=[],
    medical_conditions=[],
    pregnancy_stage="second trimester (20 weeks)",
    doctor_clearance=True,
    pre_pregnancy_fitness="very active",
    restrictions=["no supine exercises after 20 weeks", "avoid contact sports"],
)

VEGAN_ATHLETE = create_basic_user_profile(
    name="Jordan Lee",
    age=24,
    fitness_level=FitnessLevel.ADVANCED,
    goals=[Goal.ATHLETIC_PERFORMANCE, Goal.ENDURANCE],
    dietary_restrictions=[DietaryRestriction.VEGAN],
    height="5'7\"",
    weight="125 lbs",
    activity_level="extremely_active",
    workout_frequency="6-7 times per week",
    preferences=["endurance training", "plant-based nutrition"],
    injuries=[],
    medical_conditions=[],
    sport="triathlon",
    competitive_level="amateur competitive",
    training_experience="5 years",
    nutrition_focus="plant-based performance",
)

# Test profile collections
ALL_PROFILES = [
    BEGINNER_WEIGHT_LOSS,
    BEGINNER_MUSCLE_GAIN,
    BEGINNER_GENERAL_FITNESS,
    INTERMEDIATE_RUNNER,
    INTERMEDIATE_STRENGTH,
    INTERMEDIATE_BODYBUILDING,
    ADVANCED_ATHLETE,
    ADVANCED_POWERLIFTER,
    INJURY_RECOVERY,
    OLDER_ADULT,
    PREGNANT_USER,
    VEGAN_ATHLETE,
]

BEGINNER_PROFILES = [
    BEGINNER_WEIGHT_LOSS,
    BEGINNER_MUSCLE_GAIN,
    BEGINNER_GENERAL_FITNESS,
]

INTERMEDIATE_PROFILES = [
    INTERMEDIATE_RUNNER,
    INTERMEDIATE_STRENGTH,
    INTERMEDIATE_BODYBUILDING,
]

ADVANCED_PROFILES = [ADVANCED_ATHLETE, ADVANCED_POWERLIFTER]

SPECIAL_CASE_PROFILES = [INJURY_RECOVERY, OLDER_ADULT, PREGNANT_USER, VEGAN_ATHLETE]

STRENGTH_FOCUSED_PROFILES = [
    BEGINNER_MUSCLE_GAIN,
    INTERMEDIATE_STRENGTH,
    INTERMEDIATE_BODYBUILDING,
    ADVANCED_POWERLIFTER,
]

NUTRITION_FOCUSED_PROFILES = [
    BEGINNER_WEIGHT_LOSS,
    INTERMEDIATE_BODYBUILDING,
    VEGAN_ATHLETE,
]

CARDIO_FOCUSED_PROFILES = [INTERMEDIATE_RUNNER, ADVANCED_ATHLETE, VEGAN_ATHLETE]


def get_profile_by_name(name: str) -> Dict[str, Any]:
    """Get a profile by name."""
    for profile in ALL_PROFILES:
        if profile["name"] == name:
            return profile
    raise ValueError(f"Profile with name '{name}' not found")


def get_profiles_by_fitness_level(level: FitnessLevel) -> List[Dict[str, Any]]:
    """Get all profiles for a specific fitness level."""
    return [p for p in ALL_PROFILES if p["fitness_level"] == level.value]


def get_profiles_by_goal(goal: Goal) -> List[Dict[str, Any]]:
    """Get all profiles that include a specific goal."""
    return [p for p in ALL_PROFILES if goal.value in p["goals"]]


def get_profiles_with_dietary_restrictions() -> List[Dict[str, Any]]:
    """Get all profiles that have dietary restrictions."""
    return [p for p in ALL_PROFILES if p["dietary_restrictions"]]


def get_profiles_with_injuries() -> List[Dict[str, Any]]:
    """Get all profiles that have injuries."""
    return [p for p in ALL_PROFILES if p.get("injuries")]


def get_profiles_with_medical_conditions() -> List[Dict[str, Any]]:
    """Get all profiles that have medical conditions."""
    return [p for p in ALL_PROFILES if p.get("medical_conditions")]
