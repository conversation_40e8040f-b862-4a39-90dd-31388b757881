"""
Tests for Azure Search Retriever Tool

Comprehensive test suite covering keyword search, vector search, hybrid search,
error handling, circuit breaker functionality, and integration scenarios.
"""

import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from athlea_langgraph.schemas.azure_search_retriever_schemas import (
    AzureSearchRetrieverInput,
    AzureSearchRetrieverOutput,
    SearchResultItem,
)
from athlea_langgraph.tools.external.azure_search_retriever import (
    AzureSearchRetrieverTool,
    CircuitBreaker,
)


class TestAzureSearchRetrieverTool:
    """Test suite for AzureSearchRetrieverTool."""

    @pytest.fixture
    def tool(self):
        """Create an AzureSearchRetrieverTool instance for testing."""
        return AzureSearchRetrieverTool(
            azure_search_service_name="test-service",
            azure_search_api_key="test-key",
            default_index_name="test-index",
            timeout=5,
            max_retries=2,
            base_delay=0.1,
        )

    @pytest.fixture
    def keyword_search_input(self):
        """Sample keyword search input."""
        return {
            "query": "benefits of strength training",
            "top_k": 5,
            "vector_search": False,
        }

    @pytest.fixture
    def vector_search_input(self):
        """Sample vector search input."""
        return {
            "query": "cardiovascular health and running",
            "top_k": 3,
            "vector_search": True,
            "hybrid_search": False,
        }

    @pytest.fixture
    def hybrid_search_input(self):
        """Sample hybrid search input."""
        return {
            "query": "HIIT for fat loss",
            "top_k": 5,
            "vector_search": True,
            "hybrid_search": True,
        }

    @pytest.fixture
    def mock_search_results(self):
        """Mock search results from Azure Search."""
        return [
            {
                "id": "doc1",
                "content": "Strength training provides numerous benefits including increased muscle mass, improved bone density, and enhanced metabolic rate.",
                "title": "Benefits of Strength Training",
                "source": "fitness-guide.pdf",
                "url": "https://example.com/fitness-guide.pdf",
                "@search.score": 0.95,
                "@search.highlights": {
                    "content": [
                        "<em>Strength training</em> provides numerous <em>benefits</em>"
                    ]
                },
                "chunk_id": "chunk_001",
                "created_at": "2024-01-01T00:00:00Z",
            },
            {
                "id": "doc2",
                "content": "Regular resistance training can help prevent age-related muscle loss and improve functional strength for daily activities.",
                "title": "Resistance Training for Health",
                "source": "health-research.pdf",
                "url": "https://example.com/health-research.pdf",
                "@search.score": 0.87,
                "@search.highlights": {
                    "content": ["Regular resistance <em>training</em> can help prevent"]
                },
                "chunk_id": "chunk_002",
                "created_at": "2024-01-02T00:00:00Z",
            },
        ]

    @pytest.mark.asyncio
    async def test_tool_initialization(self):
        """Test tool initialization with various parameters."""
        tool = AzureSearchRetrieverTool(
            azure_search_service_name="test-service",
            azure_search_api_key="test-key",
            default_index_name="custom-index",
            timeout=10,
            max_retries=5,
            base_delay=2.0,
        )

        assert tool.service_name == "test-service"
        assert tool.search_api_key == "test-key"
        assert tool.default_index == "custom-index"
        assert tool.timeout == 10
        assert tool.max_retries == 5
        assert tool.base_delay == 2.0
        assert isinstance(tool.circuit_breaker, CircuitBreaker)
        assert tool.is_initialized is True

    @pytest.mark.asyncio
    async def test_tool_initialization_missing_credentials(self):
        """Test tool initialization with missing credentials."""
        tool = AzureSearchRetrieverTool(
            azure_search_service_name=None,
            azure_search_api_key=None,
        )

        assert tool.is_initialized is False

    @pytest.mark.asyncio
    async def test_keyword_search_success(
        self, tool, keyword_search_input, mock_search_results
    ):
        """Test successful keyword search."""
        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock the search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(keyword_search_input)

            assert result.success is True
            assert result.query == "benefits of strength training"
            assert result.result_count == 2
            assert len(result.results) == 2
            assert result.results[0].title == "Benefits of Strength Training"
            assert result.results[0].score == 0.95
            assert result.error_type is None

    @pytest.mark.asyncio
    async def test_vector_search_success(
        self, tool, vector_search_input, mock_search_results
    ):
        """Test successful vector search with embedding generation."""
        with (
            patch.object(tool, "_get_search_client") as mock_get_client,
            patch(
                "athlea_langgraph.tools.external.azure_search_retriever.get_azure_openai_embedding"
            ) as mock_embedding,
        ):

            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock embedding generation
            mock_embedding.return_value = [0.1] * 1536  # Mock 1536-dim embedding

            # Mock the search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(vector_search_input)

            assert result.success is True
            assert result.query == "cardiovascular health and running"
            assert result.result_count == 2
            assert len(result.results) == 2
            mock_embedding.assert_called_once()

    @pytest.mark.asyncio
    async def test_hybrid_search_success(
        self, tool, hybrid_search_input, mock_search_results
    ):
        """Test successful hybrid search."""
        with (
            patch.object(tool, "_get_search_client") as mock_get_client,
            patch(
                "athlea_langgraph.tools.external.azure_search_retriever.get_azure_openai_embedding"
            ) as mock_embedding,
        ):

            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock embedding generation
            mock_embedding.return_value = [0.1] * 1536

            # Mock the search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(hybrid_search_input)

            assert result.success is True
            assert result.query == "HIIT for fat loss"
            assert result.result_count == 2
            assert len(result.results) == 2
            mock_embedding.assert_called_once()

    @pytest.mark.asyncio
    async def test_input_validation_errors(self, tool):
        """Test input validation error handling."""
        # Missing required query field
        invalid_input = {
            "top_k": 5,
        }

        result = await tool.invoke(invalid_input)

        assert result.success is False
        assert result.error_type == "validation_error"
        assert "validation failed" in result.message.lower()

    @pytest.mark.asyncio
    async def test_invalid_top_k_validation(self, tool):
        """Test validation of invalid top_k values."""
        # top_k too high
        invalid_input = {
            "query": "test query",
            "top_k": 150,  # Above max of 100
        }

        result = await tool.invoke(invalid_input)

        assert result.success is False
        assert result.error_type == "validation_error"

    @pytest.mark.asyncio
    async def test_tool_not_initialized(self):
        """Test behavior when tool is not properly initialized."""
        tool = AzureSearchRetrieverTool(
            azure_search_service_name=None,
            azure_search_api_key=None,
        )

        input_data = {
            "query": "test query",
            "top_k": 5,
        }

        result = await tool.invoke(input_data)

        assert result.success is False
        assert result.error_type == "configuration_error"
        assert "not properly initialized" in result.message

    @pytest.mark.asyncio
    async def test_circuit_breaker_open(self, tool, keyword_search_input):
        """Test behavior when circuit breaker is open."""
        # Force circuit breaker to open state
        tool.circuit_breaker.failure_count = 10  # Above threshold
        tool.circuit_breaker.last_failure_time = asyncio.get_event_loop().time()

        result = await tool.invoke(keyword_search_input)

        assert result.success is False
        assert result.error_type == "circuit_breaker_open"
        assert "temporarily unavailable" in result.message

    @pytest.mark.asyncio
    async def test_search_timeout(self, tool, keyword_search_input):
        """Test search timeout handling."""
        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock timeout
            mock_client.search.side_effect = asyncio.TimeoutError()

            result = await tool.invoke(keyword_search_input)

            assert result.success is False
            assert result.error_type == "timeout_error"
            assert "timed out" in result.message

    @pytest.mark.asyncio
    async def test_search_api_error(self, tool, keyword_search_input):
        """Test Azure Search API error handling."""
        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock API error
            mock_client.search.side_effect = Exception("Azure Search API error")

            result = await tool.invoke(keyword_search_input)

            assert result.success is False
            assert result.error_type == "search_api_error"
            assert "Azure Search API error" in result.message

    @pytest.mark.asyncio
    async def test_embedding_failure_pure_vector(self, tool, vector_search_input):
        """Test embedding failure in pure vector search mode."""
        with patch(
            "athlea_langgraph.tools.external.azure_search_retriever.get_azure_openai_embedding"
        ) as mock_embedding:
            # Mock embedding failure
            mock_embedding.side_effect = Exception("Embedding generation failed")

            result = await tool.invoke(vector_search_input)

            assert result.success is False
            assert result.error_type == "search_api_error"
            assert "failed" in result.message.lower()

    @pytest.mark.asyncio
    async def test_embedding_failure_hybrid_fallback(
        self, tool, hybrid_search_input, mock_search_results
    ):
        """Test embedding failure in hybrid search with fallback to keyword."""
        with (
            patch.object(tool, "_get_search_client") as mock_get_client,
            patch(
                "athlea_langgraph.tools.external.azure_search_retriever.get_azure_openai_embedding"
            ) as mock_embedding,
        ):

            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock embedding failure
            mock_embedding.side_effect = Exception("Embedding generation failed")

            # Mock successful keyword search fallback
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(hybrid_search_input)

            # Should succeed with keyword fallback
            assert result.success is True
            assert result.result_count == 2

    @pytest.mark.asyncio
    async def test_execution_time_tracking(
        self, tool, keyword_search_input, mock_search_results
    ):
        """Test that execution time is properly tracked."""
        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock the search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(keyword_search_input)

            assert result.success is True
            assert result.execution_time_ms > 0
            assert isinstance(result.execution_time_ms, int)

    @pytest.mark.asyncio
    async def test_request_id_generation(
        self, tool, keyword_search_input, mock_search_results
    ):
        """Test that unique request IDs are generated."""
        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock the search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result1 = await tool.invoke(keyword_search_input)
            result2 = await tool.invoke(keyword_search_input)

            assert result1.success is True
            assert result2.success is True
            assert result1.request_id != result2.request_id
            assert len(result1.request_id) == 8  # UUID[:8]

    @pytest.mark.asyncio
    async def test_search_result_item_mapping(
        self, tool, keyword_search_input, mock_search_results
    ):
        """Test proper mapping of search results to SearchResultItem objects."""
        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock the search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(keyword_search_input)

            assert result.success is True
            assert len(result.results) == 2

            first_result = result.results[0]
            assert isinstance(first_result, SearchResultItem)
            assert (
                first_result.content
                == "Strength training provides numerous benefits including increased muscle mass, improved bone density, and enhanced metabolic rate."
            )
            assert first_result.title == "Benefits of Strength Training"
            assert first_result.source == "fitness-guide.pdf"
            assert first_result.url == "https://example.com/fitness-guide.pdf"
            assert first_result.score == 0.95
            assert first_result.chunk_id == "chunk_001"
            assert first_result.highlights is not None

    @pytest.mark.asyncio
    async def test_custom_index_name(
        self, tool, keyword_search_input, mock_search_results
    ):
        """Test using custom index name in search request."""
        custom_input = {**keyword_search_input, "index_name": "custom-index"}

        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock the search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter(mock_search_results))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(custom_input)

            assert result.success is True
            mock_get_client.assert_called_with("custom-index")

    @pytest.mark.asyncio
    async def test_close_clients(self, tool):
        """Test proper cleanup of search clients."""
        # Create some mock clients
        mock_client1 = AsyncMock()
        mock_client2 = AsyncMock()
        tool._search_clients = {
            "index1": mock_client1,
            "index2": mock_client2,
        }

        await tool.close()

        mock_client1.close.assert_called_once()
        mock_client2.close.assert_called_once()
        assert len(tool._search_clients) == 0


class TestAzureSearchRetrieverEdgeCases:
    """Test edge cases and boundary conditions."""

    @pytest.mark.asyncio
    async def test_empty_search_results(self):
        """Test handling of empty search results."""
        tool = AzureSearchRetrieverTool(
            azure_search_service_name="test-service",
            azure_search_api_key="test-key",
        )

        input_data = {
            "query": "nonexistent query",
            "top_k": 5,
        }

        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock empty search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter([]))  # Empty results
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success is True
            assert result.result_count == 0
            assert len(result.results) == 0

    @pytest.mark.asyncio
    async def test_large_top_k_boundary(self):
        """Test behavior with maximum allowed top_k value."""
        tool = AzureSearchRetrieverTool(
            azure_search_service_name="test-service",
            azure_search_api_key="test-key",
        )

        input_data = {
            "query": "test query",
            "top_k": 100,  # Maximum allowed
        }

        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter([]))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success is True

    @pytest.mark.asyncio
    async def test_special_characters_in_query(self):
        """Test handling of special characters in search query."""
        tool = AzureSearchRetrieverTool(
            azure_search_service_name="test-service",
            azure_search_api_key="test-key",
        )

        input_data = {
            "query": "test query with special chars: @#$%^&*()",
            "top_k": 5,
        }

        with patch.object(tool, "_get_search_client") as mock_get_client:
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client

            # Mock search response
            mock_response = AsyncMock()
            mock_page = AsyncMock()
            mock_page.__aiter__ = AsyncMock(return_value=iter([]))
            mock_response.by_page.return_value.__aiter__ = AsyncMock(
                return_value=iter([mock_page])
            )
            mock_client.search.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success is True
            assert result.query == "test query with special chars: @#$%^&*()"
