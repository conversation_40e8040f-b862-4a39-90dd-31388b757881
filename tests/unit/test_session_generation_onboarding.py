"""
Test for Session Generation portion of the Onboarding Graph

This test focuses specifically on testing the session generation nodes
within the onboarding graph workflow.
"""

import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta

from athlea_langgraph.states.onboarding_state import create_initial_onboarding_state
from athlea_langgraph.agents.session_generation_nodes import (
    session_generation_node,
    coach_coordination_node,
    user_control_node,
    session_adaptation_node,
    session_flow_router,
)
from athlea_langgraph.states.onboarding_state import (
    PlanDetails,
    SidebarStateData,
    UserGoals,
)


class TestSessionGenerationOnboarding:
    """Test the session generation portion of the onboarding graph."""

    @pytest.fixture
    def mock_onboarding_state(self):
        """Create a mock onboarding state with a generated plan."""
        state = create_initial_onboarding_state("test_user_123")

        # Add a generated plan (required for session generation)
        state["generated_plan"] = PlanDetails(
            name="Test Fitness Plan",
            description="A comprehensive test fitness plan",
            duration="8 weeks",
            level="intermediate",
            plan_type="strength_endurance",
            disciplines=["running", "strength"],
            rationale="Test plan for strength and endurance development",
            phases=[],
            example_sessions=[],
        )

        # Add sidebar data
        state["sidebar_data"] = SidebarStateData(
            goals=UserGoals(exists=True, list=["build strength", "improve endurance"]),
            summary_items=[],
            selected_sports=["running", "strength"],
            current_stage="plan_generated",
        )

        return state

    @pytest.fixture
    def mock_weekly_plan(self):
        """Create a mock weekly plan response."""
        return {
            "week_number": 1,
            "total_sessions": 4,
            "weekly_focus": "Base Building",
            "weekly_volume": {
                "strength": 120,  # minutes
                "running": 180,  # minutes
                "recovery": 60,  # minutes
            },
            "sessions": {
                "day_1": {
                    "type": "strength",
                    "duration": 60,
                    "exercises": ["squats", "bench press", "rows"],
                },
                "day_2": {
                    "type": "running",
                    "duration": 45,
                    "workout": "easy run",
                },
                "day_3": {
                    "type": "strength",
                    "duration": 60,
                    "exercises": ["deadlifts", "pull-ups", "push-ups"],
                },
                "day_4": {
                    "type": "running",
                    "duration": 30,
                    "workout": "tempo run",
                },
            },
        }

    @pytest.mark.asyncio
    async def test_session_generation_node_success(
        self, mock_onboarding_state, mock_weekly_plan
    ):
        """Test successful session generation."""
        with patch(
            "athlea_langgraph.agents.session_generation_nodes.SessionGenerationEngine"
        ) as mock_engine_class:
            # Mock the session generation engine
            mock_engine = MagicMock()
            mock_engine_class.return_value = mock_engine
            # Mock the async method properly
            from unittest.mock import AsyncMock

            mock_engine.generate_basic_week = AsyncMock(return_value=mock_weekly_plan)

            # Mock the fitness profile creation
            with patch(
                "athlea_langgraph.agents.session_generation_nodes.create_fitness_profile_from_onboarding"
            ) as mock_profile_creation:
                mock_profile_creation.return_value = {
                    "user_id": "test_user_123",
                    "experience": "intermediate",
                }

                # Execute the session generation node
                result_state = await session_generation_node(mock_onboarding_state)

                # Verify the session generation was called
                mock_engine.generate_basic_week.assert_called_once_with(
                    "test_user_123", week_number=1
                )

                # Check that the state was updated correctly
                assert "generated_weekly_plans" in result_state
                assert 1 in result_state["generated_weekly_plans"]
                assert result_state["generated_weekly_plans"][1] == mock_weekly_plan
                assert result_state["current_week"] == 1
                assert result_state["current_day"] == 1
                assert result_state["onboarding_stage"] == "sessions_generated"

                # Check that a success message was added
                messages = result_state["messages"]
                assert len(messages) > 0
                last_message = messages[-1]
                assert (
                    "Great! I've generated your first week" in last_message["content"]
                )
                assert "4 sessions" in last_message["content"]

                # Check that completion flags are set
                assert result_state["has_enough_info"] is True
                assert result_state["needs_input"] is False
                assert result_state["requires_input"] is False

    @pytest.mark.asyncio
    async def test_session_generation_node_no_plan(self):
        """Test session generation when no plan is available."""
        state = create_initial_onboarding_state("test_user_123")
        # Don't add a generated_plan to simulate missing plan

        result_state = await session_generation_node(state)

        # Verify error handling
        assert "error" in result_state
        assert "No training plan found" in result_state["error"]

    @pytest.mark.asyncio
    async def test_coach_coordination_node(
        self, mock_onboarding_state, mock_weekly_plan
    ):
        """Test coach coordination node."""
        # Setup state with generated weekly plan
        mock_onboarding_state["generated_weekly_plans"] = {1: mock_weekly_plan}
        mock_onboarding_state["current_week"] = 1

        result_state = await coach_coordination_node(mock_onboarding_state)

        # Check that coordination notes were added
        assert "coach_coordination" in result_state
        assert 1 in result_state["coach_coordination"]
        coordination_notes = result_state["coach_coordination"][1]

        assert "strength_coach" in coordination_notes
        assert "running_coach" in coordination_notes
        assert "coordination_summary" in coordination_notes
        assert (
            "120 minutes of strength training" in coordination_notes["strength_coach"]
        )
        assert "180 minutes of running" in coordination_notes["running_coach"]

    @pytest.mark.asyncio
    async def test_user_control_node_pause(self, mock_onboarding_state):
        """Test user control node with pause command."""
        mock_onboarding_state["user_input"] = "I need to pause my training"

        result_state = await user_control_node(mock_onboarding_state)

        # Check that training was paused
        assert "user_control_state" in result_state
        user_control = result_state["user_control_state"]
        assert user_control["is_paused"] is True
        assert (
            user_control["pause_reason"] == "i need to pause my training"
        )  # lowercase as converted by the implementation
        assert "paused_at" in user_control

        # Check that input is required
        assert result_state["needs_input"] is True
        assert result_state["input_prompt"] is not None
        assert "paused" in result_state["input_prompt"]

        # Check that a response message was added
        messages = result_state["messages"]
        assert len(messages) > 0
        last_message = messages[-1]
        assert "paused" in last_message["content"]

    @pytest.mark.asyncio
    async def test_user_control_node_resume(self, mock_onboarding_state):
        """Test user control node with resume command."""
        # Setup paused state
        mock_onboarding_state["user_control_state"] = {
            "is_paused": True,
            "pause_reason": "user requested",
        }
        mock_onboarding_state["user_input"] = "resume training"

        result_state = await user_control_node(mock_onboarding_state)

        # Check that training was resumed
        user_control = result_state["user_control_state"]
        assert user_control["is_paused"] is False
        assert user_control["pause_reason"] is None

        # Check that input is no longer required
        assert result_state["needs_input"] is False
        assert result_state["input_prompt"] is None

        # Check that a resume message was added
        messages = result_state["messages"]
        assert len(messages) > 0
        last_message = messages[-1]
        assert "resumed" in last_message["content"]

    def test_session_flow_router_paused(self, mock_onboarding_state):
        """Test session flow router when training is paused."""
        mock_onboarding_state["user_control_state"] = {"is_paused": True}

        result = session_flow_router(mock_onboarding_state)

        assert result == "user_control"

    def test_session_flow_router_adaptation_needed(self, mock_onboarding_state):
        """Test session flow router when adaptation is needed."""
        mock_onboarding_state["user_control_state"] = {"is_paused": False}
        mock_onboarding_state["feedback_history"] = [
            {"session_id": 1, "feedback": "too easy"},
            {"session_id": 2, "feedback": "good"},
            {"session_id": 3, "feedback": "too hard"},
        ]

        result = session_flow_router(mock_onboarding_state)

        assert result == "session_adaptation"

    def test_session_flow_router_complete(self, mock_onboarding_state):
        """Test session flow router when flow should complete."""
        mock_onboarding_state["user_control_state"] = {"is_paused": False}
        mock_onboarding_state["feedback_history"] = []

        result = session_flow_router(mock_onboarding_state)

        assert result == "complete"

    @pytest.mark.asyncio
    async def test_full_session_generation_flow(
        self, mock_onboarding_state, mock_weekly_plan
    ):
        """Test the full session generation flow from start to finish."""
        with patch(
            "athlea_langgraph.agents.session_generation_nodes.SessionGenerationEngine"
        ) as mock_engine_class:
            # Mock the session generation engine
            mock_engine = MagicMock()
            mock_engine_class.return_value = mock_engine
            # Mock the async method properly
            from unittest.mock import AsyncMock

            mock_engine.generate_basic_week = AsyncMock(return_value=mock_weekly_plan)

            # Mock the fitness profile creation
            with patch(
                "athlea_langgraph.agents.session_generation_nodes.create_fitness_profile_from_onboarding"
            ) as mock_profile_creation:
                mock_profile_creation.return_value = {
                    "user_id": "test_user_123",
                    "experience": "intermediate",
                }

                # 1. Generate sessions
                state = await session_generation_node(mock_onboarding_state)
                assert state["onboarding_stage"] == "sessions_generated"

                # 2. Coordinate coaches
                state = await coach_coordination_node(state)
                assert "coach_coordination" in state

                # 3. Check routing
                route = session_flow_router(state)
                assert (
                    route == "complete"
                )  # Should complete if no pausing or adaptation needed

                print("✅ Full session generation flow completed successfully")
                print(
                    f"   - Generated {state['generated_weekly_plans'][1]['total_sessions']} sessions"
                )
                print(
                    f"   - Weekly focus: {state['generated_weekly_plans'][1]['weekly_focus']}"
                )
                print(
                    f"   - Total volume: {sum(state['generated_weekly_plans'][1]['weekly_volume'].values())} minutes"
                )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
