#!/usr/bin/env python3
"""
Test Specific Strength Training Requests

Test with very specific strength training requests to see if routing works.
"""

import asyncio
import logging

from langchain_core.messages import HumanMessage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
)


async def test_specific_requests():
    """Test with very specific requests."""
    print("🧪 Testing Specific Strength Training Requests")
    print("=" * 60)

    config = {
        "user_id": "test_user",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 2,
        "enable_human_feedback": False,
    }

    graph = await create_comprehensive_coaching_graph(config)

    test_cases = [
        "I need help with strength training",
        "Can you help me with my deadlift form?",
        "I want to build muscle mass",
        "Create a strength training program for me",
        "I need running advice for a 5K",
        "Help me with my nutrition plan",
        "I want cycling route recommendations",
    ]

    for i, test_input in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: '{test_input}'")

        initial_state = {
            "messages": [HumanMessage(content=test_input)],
            "user_query": test_input,
            "execution_steps": [],
            "active_coaches": [],
            "coach_responses": {},
            "tool_calls_made": [],
        }

        try:
            # Use astream to see the routing decision
            step_count = 0
            routing_decision = None

            async for step in graph.astream(initial_state):
                step_count += 1
                for node_name, state_update in step.items():
                    if node_name == "router" and isinstance(state_update, dict):
                        routing_decision = state_update.get("routing_decision")
                        break
                    elif node_name == "planning" and isinstance(state_update, dict):
                        plan = state_update.get("plan", [])
                        print(f"   Plan: {plan}")

                if routing_decision:
                    break

                if step_count > 5:  # Safety break
                    break

            print(f"   Routing decision: {routing_decision}")

        except Exception as e:
            print(f"   ❌ Error: {e}")


async def test_with_react_coach():
    """Test direct ReAct coach execution."""
    print("\n🤖 Testing Direct ReAct Coach Execution")
    print("=" * 60)

    config = {
        "user_id": "test_user",
        "enable_memory": False,
        "use_react_agents": True,
        "max_iterations": 2,
        "enable_human_feedback": False,
    }

    graph = await create_comprehensive_coaching_graph(config)

    # Create a state that should go directly to strength coach
    initial_state = {
        "messages": [HumanMessage(content="I need strength training help")],
        "user_query": "I need strength training help",
        "execution_steps": [],
        "active_coaches": [],
        "coach_responses": {},
        "tool_calls_made": [],
        "plan": ["strength_coach"],  # Force the plan
        "current_step": 0,
    }

    try:
        print("Testing with forced strength_coach plan...")
        result = await graph.ainvoke(initial_state)

        print(f"Execution steps: {result.get('execution_steps', [])}")
        print(f"Active coaches: {result.get('active_coaches', [])}")
        print(f"Coach responses: {list(result.get('coach_responses', {}).keys())}")

        # Check if strength coach was actually executed
        if "strength_coach" in result.get("active_coaches", []):
            print("✅ Strength coach was executed!")
            strength_response = result.get("coach_responses", {}).get(
                "strength_coach", ""
            )
            if strength_response:
                print(f"Response preview: {strength_response[:200]}...")
        else:
            print("❌ Strength coach was not executed")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


async def main():
    """Run tests."""
    await test_specific_requests()
    await test_with_react_coach()


if __name__ == "__main__":
    asyncio.run(main())
