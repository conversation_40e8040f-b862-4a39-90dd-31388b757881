#!/usr/bin/env python3
"""
Test Individual Coach Graphs

This test verifies that the individual coach graphs work correctly
and can be instantiated without errors.
"""

import asyncio
import logging
import pytest
from typing import Dict, Any

from langchain_core.messages import HumanMessage

from athlea_langgraph.graphs.individual_graph import (
    create_individual_coach_graph,
    create_strength_coach_graph,
    create_cardio_coach_graph,
    create_cycling_coach_graph,
    create_nutrition_coach_graph,
    create_recovery_coach_graph,
    create_mental_coach_graph,
    AVAILABLE_COACHES,
)

logger = logging.getLogger(__name__)


class TestIndividualCoachGraphs:
    """Test individual coach graph creation and functionality."""

    @pytest.mark.asyncio
    async def test_available_coaches_list(self):
        """Test that all expected coaches are available."""
        expected_coaches = [
            "strength_coach",
            "cardio_coach",
            "cycling_coach",
            "nutrition_coach",
            "recovery_coach",
            "mental_coach",
        ]

        assert AVAILABLE_COACHES == expected_coaches
        print(f"✅ Available coaches: {AVAILABLE_COACHES}")

    @pytest.mark.asyncio
    async def test_create_individual_coach_graph(self):
        """Test creating an individual coach graph."""
        coach_name = "strength_coach"

        try:
            graph = await create_individual_coach_graph(coach_name)

            assert graph is not None
            assert hasattr(graph, "nodes")
            assert hasattr(graph, "ainvoke")

            print(f"✅ Created {coach_name} graph successfully")
            print(f"   Nodes: {list(graph.nodes.keys())}")

        except Exception as e:
            pytest.fail(f"Failed to create {coach_name} graph: {e}")

    @pytest.mark.asyncio
    async def test_invalid_coach_name(self):
        """Test that invalid coach names raise appropriate errors."""
        invalid_coach = "invalid_coach"

        with pytest.raises(ValueError) as exc_info:
            await create_individual_coach_graph(invalid_coach)

        assert "not available" in str(exc_info.value)
        print(f"✅ Correctly rejected invalid coach: {invalid_coach}")

    @pytest.mark.asyncio
    async def test_all_factory_functions(self):
        """Test that all factory functions work correctly."""
        factory_functions = [
            ("strength_coach", create_strength_coach_graph),
            ("cardio_coach", create_cardio_coach_graph),
            ("cycling_coach", create_cycling_coach_graph),
            ("nutrition_coach", create_nutrition_coach_graph),
            ("recovery_coach", create_recovery_coach_graph),
            ("mental_coach", create_mental_coach_graph),
        ]

        for coach_name, factory_func in factory_functions:
            try:
                graph = await factory_func()

                assert graph is not None
                assert hasattr(graph, "nodes")
                assert hasattr(graph, "ainvoke")

                print(f"✅ Factory function for {coach_name} works")

            except Exception as e:
                pytest.fail(f"Factory function failed for {coach_name}: {e}")

    @pytest.mark.asyncio
    async def test_basic_graph_structure(self):
        """Test that the graph has the expected structure."""
        graph = await create_strength_coach_graph()

        expected_nodes = [
            "reasoning",
            "knowledge_assessment",
            "graphrag_retrieval",
            "strength_coach",
        ]

        for node in expected_nodes:
            assert node in graph.nodes, f"Missing node: {node}"

        print(f"✅ Graph structure correct: {list(graph.nodes.keys())}")

    @pytest.mark.asyncio
    async def test_simple_invocation(self):
        """Test a simple invocation of an individual coach graph."""
        try:
            graph = await create_strength_coach_graph()

            test_input = {
                "messages": [
                    HumanMessage(content="Hello, I need help with my workout")
                ],
                "user_query": "Hello, I need help with my workout",
                "user_profile": {"user_id": "test_user"},
                "thread_id": "test_thread",
            }

            # Just test that it doesn't crash - we won't run the full flow in CI
            assert graph is not None
            print("✅ Graph invocation test setup successful")

        except Exception as e:
            pytest.fail(f"Simple invocation test failed: {e}")

    @pytest.mark.asyncio
    async def test_graph_configuration(self):
        """Test that graphs can be configured properly."""
        config = {
            "user_id": "test_user",
            "thread_id": "test_thread",
            "enable_memory": False,  # Disable for testing
            "use_react_agents": True,
            "max_iterations": 3,
        }

        try:
            graph = await create_nutrition_coach_graph(config)

            assert graph is not None
            print("✅ Graph configuration test passed")

        except Exception as e:
            pytest.fail(f"Graph configuration test failed: {e}")


async def main():
    """Run tests manually if not using pytest."""
    print("🧪 Testing Individual Coach Graphs")
    print("=" * 50)

    test_instance = TestIndividualCoachGraphs()

    try:
        await test_instance.test_available_coaches_list()
        await test_instance.test_create_individual_coach_graph()
        await test_instance.test_invalid_coach_name()
        await test_instance.test_all_factory_functions()
        await test_instance.test_basic_graph_structure()
        await test_instance.test_simple_invocation()
        await test_instance.test_graph_configuration()

        print("\n" + "=" * 50)
        print("✅ All tests passed!")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
