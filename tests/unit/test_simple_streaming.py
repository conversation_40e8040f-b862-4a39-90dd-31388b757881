"""
Simple test script to verify streaming functionality with fallback responses.
"""

import asyncio
import logging
import os
from datetime import datetime

from athlea_langgraph.api.streaming import SimpleCoachingStreamer, StreamingEventType
from athlea_langgraph.graphs.archived.coaching_graph_with_memory import (
    MemoryEnhancedCoachingGraph,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockCoachingGraph:
    """Mock coaching graph for testing fallback responses."""

    def __init__(self, mongodb_uri: str, user_id: str):
        self.mongodb_uri = mongodb_uri
        self.user_id = user_id


async def test_fallback_streaming():
    """Test the fallback streaming functionality."""
    print("🧪 Testing Fallback Streaming")
    print("=" * 40)

    # Create a mock coaching graph
    mock_graph = MockCoachingGraph("mongodb://test", "test_user")

    # Create the simple streamer
    streamer = SimpleCoachingStreamer(mock_graph)

    # Test queries
    test_queries = [
        "I want to start strength training",
        "How can I improve my nutrition?",
        "What's the best cardio workout?",
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"\n🎯 Test {i}: {query}")
        print("-" * 30)

        collected_content = []
        event_count = 0

        try:
            # Test the fallback response method directly
            agent_type = streamer._determine_agent_from_message(query)
            fallback_response = await streamer._fallback_coach_response(query)

            print(f"🤖 Agent: {agent_type}")
            print(f"📝 Response: {fallback_response[:100]}...")

            # Test SSE formatting
            sse_event = streamer.format_sse_event(
                StreamingEventType.TOKEN,
                {"agent": agent_type, "content": "Hello world"},
            )
            print(f"📡 SSE Format: {sse_event.strip()}")

            print(f"✅ Test {i} completed successfully")

        except Exception as e:
            print(f"❌ Test {i} failed: {e}")

    print(f"\n🎉 Fallback streaming tests completed!")


async def test_real_streaming():
    """Test streaming with the real coaching graph if MongoDB is available."""
    print("\n🔥 Testing Real Streaming (if MongoDB available)")
    print("=" * 50)

    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("⚠️ MONGODB_URI not set, skipping real streaming test")
        return

    try:
        # Create real coaching graph
        coaching_graph = MemoryEnhancedCoachingGraph(mongodb_uri, "test_user_streaming")

        # Create streamer
        streamer = SimpleCoachingStreamer(coaching_graph)

        # Test a simple query
        query = "I want to build muscle"
        print(f"🎯 Testing: {query}")

        collected_events = []
        async for event in streamer.stream_coaching_response(
            user_message=query,
            thread_id=f"test_{datetime.now().strftime('%H%M%S')}",
            user_profile={},
        ):
            collected_events.append(event)
            print(f"📨 Event: {event.strip()}")

            # Stop after a reasonable number of events
            if len(collected_events) >= 20:
                break

        print(f"✅ Real streaming test completed with {len(collected_events)} events")

    except Exception as e:
        print(f"❌ Real streaming test failed: {e}")
        import traceback

        traceback.print_exc()


async def main():
    """Run all streaming tests."""
    print("🚀 Simple Streaming Test Suite")
    print("=" * 50)

    # Test fallback responses first
    await test_fallback_streaming()

    # Test real streaming if available
    await test_real_streaming()

    print(f"\n🏁 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
