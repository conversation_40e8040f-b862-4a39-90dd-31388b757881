"""
Comprehensive test suite for streaming memory-enhanced coaching system.

Tests SSE format, agent identification, memory integration, and API endpoints.
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List

import httpx
import pytest

from athlea_langgraph.api.streaming import (
    CoachingGraphStreamer,
    SimpleCoachingStreamer,
    StreamingEventType,
    create_coaching_streamer,
    stream_coaching_response,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StreamingTestSuite:
    """Comprehensive test suite for streaming functionality."""

    def __init__(self):
        self.mongodb_uri = os.getenv("MONGODB_URI")
        self.test_user_id = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        if not self.mongodb_uri:
            raise ValueError("MONGODB_URI environment variable is required for testing")

    async def test_sse_format_validation(self):
        """Test that SSE events are properly formatted."""
        print("\n🧪 Testing SSE Format Validation")
        print("-" * 40)

        try:
            # Create a streamer
            streamer = await create_coaching_streamer(
                mongodb_uri=self.mongodb_uri, user_id=self.test_user_id
            )

            # Test the SSE format method
            test_data = {"agent": "strength", "content": "Hello world"}
            sse_event = streamer.format_sse_event(StreamingEventType.TOKEN, test_data)

            # Validate format
            expected_format = (
                f"event: {StreamingEventType.TOKEN}\ndata: {json.dumps(test_data)}\n\n"
            )
            assert sse_event == expected_format, f"SSE format mismatch: {sse_event}"

            print("✅ SSE format validation passed")
            return True

        except Exception as e:
            print(f"❌ SSE format validation failed: {e}")
            return False

    async def test_agent_identification(self):
        """Test that agents are properly identified in streaming."""
        print("\n🎯 Testing Agent Identification")
        print("-" * 40)

        try:
            streamer = await create_coaching_streamer(
                mongodb_uri=self.mongodb_uri, user_id=self.test_user_id
            )

            # Test agent mappings - check if it's the simple streamer
            if isinstance(streamer, SimpleCoachingStreamer):
                # Test agent mappings directly from the mapping dict
                test_mappings = {
                    "reasoning": "reasoning",
                    "head_coach": "Athlea",
                    "strength_coach": "strength",
                    "nutrition_coach": "nutrition",
                }

                for node_name, expected_agent in test_mappings.items():
                    mapped_agent = streamer.agent_mappings.get(node_name)
                    assert (
                        mapped_agent == expected_agent
                    ), f"Agent mapping failed for {node_name}: expected {expected_agent}, got {mapped_agent}"
            else:
                # Test the _get_agent_name method for the full streamer
                test_mappings = {
                    "reasoning": "reasoning",
                    "head_coach": "Athlea",
                    "strength_coach": "strength",
                    "nutrition_coach": "nutrition",
                }

                for node_name, expected_agent in test_mappings.items():
                    mapped_agent = streamer._get_agent_name(node_name)
                    assert (
                        mapped_agent == expected_agent
                    ), f"Agent mapping failed for {node_name}: expected {expected_agent}, got {mapped_agent}"

            print("✅ Agent identification test passed")
            return True

        except Exception as e:
            print(f"❌ Agent identification test failed: {e}")
            return False

    async def test_streaming_flow(self):
        """Test the complete streaming flow."""
        print("\n🌊 Testing Streaming Flow")
        print("-" * 40)

        try:
            thread_id = f"test_thread_{datetime.now().strftime('%H%M%S')}"
            collected_events = []

            # Stream a simple coaching question
            async for event in stream_coaching_response(
                user_message="I want to start strength training",
                thread_id=thread_id,
                user_id=self.test_user_id,
                mongodb_uri=self.mongodb_uri,
            ):
                # Parse the SSE event
                if event.strip():
                    lines = event.strip().split("\n")
                    event_type = None
                    data = None

                    for line in lines:
                        if line.startswith("event:"):
                            event_type = line.split(":", 1)[1].strip()
                        elif line.startswith("data:"):
                            data_str = line.split(":", 1)[1].strip()
                            try:
                                data = json.loads(data_str)
                            except json.JSONDecodeError:
                                data = data_str

                    if event_type and data:
                        collected_events.append({"type": event_type, "data": data})
                        print(f"📨 Event: {event_type} | Data: {data}")

                # Stop after collecting some events for testing
                if len(collected_events) >= 10:
                    break

            # Validate we received expected event types
            event_types = {event["type"] for event in collected_events}
            expected_types = {StreamingEventType.AGENT_START, StreamingEventType.TOKEN}

            # For the simple streamer, we should see at least one event type
            if not event_types:
                print("⚠️ Warning: No events received")
            elif not expected_types.intersection(event_types):
                print(
                    f"⚠️ Warning: Expected event types {expected_types} not found in {event_types}"
                )

            print(
                f"✅ Streaming flow test completed with {len(collected_events)} events"
            )
            return True

        except Exception as e:
            print(f"❌ Streaming flow test failed: {e}")
            return False

    async def test_memory_integration(self):
        """Test memory integration in streaming."""
        print("\n🧠 Testing Memory Integration")
        print("-" * 40)

        try:
            # Create coaching graph with memory
            coaching_graph_streamer = await create_coaching_streamer(
                mongodb_uri=self.mongodb_uri, user_id=self.test_user_id
            )

            # Save some test user data
            test_profile = {
                "name": "Test User",
                "goals": ["strength training", "weight loss"],
                "experience": "beginner",
                "preferences": {"workout_time": "morning", "duration": "45 minutes"},
            }

            await coaching_graph_streamer.coaching_graph.update_user_profile(
                test_profile
            )

            # Test memory retrieval
            memories = await coaching_graph_streamer.coaching_graph.load_user_memories(
                limit=1
            )

            if memories:
                print(f"✅ Memory integration working: {len(memories)} memories found")
                return True
            else:
                print("⚠️ No memories found, but test completed without errors")
                return True

        except Exception as e:
            print(f"❌ Memory integration test failed: {e}")
            return False

    async def test_api_endpoints(self):
        """Test FastAPI endpoints (if running)."""
        print("\n🌐 Testing API Endpoints")
        print("-" * 40)

        try:
            # Try to connect to the API
            async with httpx.AsyncClient() as client:
                # Test health endpoint
                try:
                    response = await client.get("http://localhost:8000/api/health")
                    if response.status_code == 200:
                        print("✅ Health endpoint accessible")

                        # Test coaches endpoint
                        coaches_response = await client.get(
                            "http://localhost:8000/api/coaches"
                        )
                        if coaches_response.status_code == 200:
                            coaches_data = coaches_response.json()
                            print(
                                f"✅ Coaches endpoint returned {len(coaches_data.get('coaches', []))} coaches"
                            )

                        return True
                    else:
                        print(
                            f"⚠️ Health endpoint returned status {response.status_code}"
                        )
                        return False

                except httpx.ConnectError:
                    print(
                        "⚠️ API server not running (expected if testing without server)"
                    )
                    return True  # Not a failure, just not running

        except Exception as e:
            print(f"❌ API endpoint test failed: {e}")
            return False

    async def test_error_handling(self):
        """Test error handling in streaming."""
        print("\n🚨 Testing Error Handling")
        print("-" * 40)

        try:
            # Test with invalid MongoDB URI
            try:
                async for event in stream_coaching_response(
                    user_message="test",
                    thread_id="test",
                    user_id="test",
                    mongodb_uri="invalid://uri",
                ):
                    # Should get an error event
                    if StreamingEventType.ERROR in event:
                        print("✅ Error handling working - caught invalid MongoDB URI")
                        break
                else:
                    print("⚠️ Expected error event not received")

            except Exception:
                print("✅ Error handling working - exception caught appropriately")

            return True

        except Exception as e:
            print(f"❌ Error handling test failed: {e}")
            return False

    async def run_all_tests(self):
        """Run all streaming tests."""
        print("🚀 Starting Streaming Test Suite")
        print("=" * 50)

        tests = [
            ("SSE Format Validation", self.test_sse_format_validation),
            ("Agent Identification", self.test_agent_identification),
            ("Streaming Flow", self.test_streaming_flow),
            ("Memory Integration", self.test_memory_integration),
            ("API Endpoints", self.test_api_endpoints),
            ("Error Handling", self.test_error_handling),
        ]

        results = []

        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                logger.error(f"Test {test_name} failed with exception: {e}")
                results.append((test_name, False))

        # Summary
        print("\n📊 Test Results Summary")
        print("=" * 50)

        passed = 0
        total = len(results)

        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            if success:
                passed += 1

        print(f"\nTotal: {passed}/{total} tests passed")

        if passed == total:
            print("🎉 All tests passed! Streaming system is working correctly.")
        else:
            print("⚠️ Some tests failed. Check the logs above for details.")

        return passed == total


async def run_quick_streaming_demo():
    """Run a quick demonstration of streaming."""
    print("\n🎬 Quick Streaming Demo")
    print("=" * 30)

    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("❌ MONGODB_URI not set, skipping demo")
        return

    user_id = f"demo_user_{datetime.now().strftime('%H%M%S')}"
    thread_id = f"demo_thread_{datetime.now().strftime('%H%M%S')}"

    print(f"User: I want to improve my nutrition")
    print(f"Coach: ", end="", flush=True)

    try:
        async for event in stream_coaching_response(
            user_message="I want to improve my nutrition",
            thread_id=thread_id,
            user_id=user_id,
            mongodb_uri=mongodb_uri,
        ):
            if StreamingEventType.TOKEN in event:
                # Extract token content
                lines = event.strip().split("\n")
                for line in lines:
                    if line.startswith("data:"):
                        data_str = line.split(":", 1)[1].strip()
                        try:
                            data = json.loads(data_str)
                            if "content" in data:
                                print(data["content"], end="", flush=True)
                        except json.JSONDecodeError:
                            pass
            elif StreamingEventType.COMPLETE in event:
                break

        print("\n\n✅ Demo completed successfully!")

    except Exception as e:
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":

    async def main():
        # Run the test suite
        test_suite = StreamingTestSuite()
        await test_suite.run_all_tests()

        # Run quick demo
        await run_quick_streaming_demo()

    asyncio.run(main())
