#!/usr/bin/env python3
"""
Test Runner Script - Multiple ways to run tests with Poetry

This script demonstrates different test execution strategies based on
the multi-agent best practices guide using Poetry for dependency management.
"""

import os
import subprocess
import sys
import time
from pathlib import Path

# Change to project root directory
project_root = Path(__file__).parent.parent.parent
os.chdir(project_root)


def run_command(cmd: str, description: str):
    """Run a command and display results"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"Command: {cmd}")
    print("=" * 60)

    start_time = time.time()
    result = subprocess.run(cmd.split(), capture_output=True, text=True)
    duration = time.time() - start_time

    if result.returncode == 0:
        print(f"✅ SUCCESS ({duration:.2f}s)")
        if result.stdout:
            print("STDOUT:", result.stdout[-500:])  # Last 500 chars
    else:
        print(f"❌ FAILED ({duration:.2f}s)")
        if result.stderr:
            print("STDERR:", result.stderr[-500:])

    return result.returncode == 0


def main():
    """Run different test suites using Poetry"""

    print("🚀 ATHLEA LANGGRAPH TEST RUNNER (Poetry)")
    print("Based on Multi-Agent Best Practices Guide")
    print(f"Working directory: {os.getcwd()}")

    # Ensure Poetry dependencies are installed
    print("\n📦 Installing dependencies with Poetry...")
    run_command("poetry install", "POETRY DEPENDENCY INSTALLATION")

    # 1. Quick Unit Tests (fast feedback)
    success = run_command(
        "poetry run pytest tests/unit/ -v --tb=short",
        "UNIT TESTS - Fast, isolated component tests",
    )

    if not success:
        print("\n⚠️  Unit tests failed! Fix these before running integration tests.")
        return 1

    # 2. Tool Tests (domain-specific tool validation)
    tool_success = run_command(
        "poetry run pytest tests/tools/ -v --tb=short",
        "TOOL TESTS - Domain-specific tool validation",
    )

    # 3. Integration Tests (slower but comprehensive)
    success = run_command(
        "poetry run pytest tests/integration/ -v --tb=short",
        "INTEGRATION TESTS - Multi-component workflow tests",
    )

    if not success:
        print("\n⚠️  Integration tests failed!")
        return 1

    # 4. End-to-End Tests (complete user workflows)
    e2e_success = run_command(
        "poetry run pytest tests/e2e/ -v --tb=short",
        "END-TO-END TESTS - Complete user workflow validation",
    )

    # 5. All Tests Together (excluding e2e for speed)
    success = run_command(
        "poetry run pytest tests/unit/ tests/tools/ tests/integration/ -v --tb=short",
        "CORE TEST SUITE - Unit + Tools + Integration",
    )

    # 6. Generate Coverage Report (if pytest-cov is available)
    coverage_success = run_command(
        "poetry run pytest tests/unit/ tests/tools/ tests/integration/ --cov=athlea_langgraph --cov-report=html --cov-report=term",
        "TEST COVERAGE ANALYSIS",
    )

    if not coverage_success:
        print("📊 Coverage analysis failed - you may need to add pytest-cov:")
        print("    poetry add --group dev pytest-cov")

    print(f"\n{'='*60}")
    print("🎯 TEST RUNNER COMPLETE")
    print(f"✅ Unit Tests: {'PASSED' if success else 'FAILED'}")
    print(f"✅ Tool Tests: {'PASSED' if tool_success else 'FAILED'}")
    print(f"✅ Integration Tests: {'PASSED' if success else 'FAILED'}")
    print(f"✅ E2E Tests: {'PASSED' if e2e_success else 'FAILED'}")
    if coverage_success:
        print("Check htmlcov/index.html for detailed coverage report")
    print("=" * 60)

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
