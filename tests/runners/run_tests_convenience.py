#!/usr/bin/env python3
"""
Convenience Test Runner

This script provides easy access to the organized test runners from the project root.
All test runners are now properly organized in tests/runners/

Usage Examples:
    python run_tests_convenience.py main           # Run main test suite
    python run_tests_convenience.py modular        # Run modular agent tests
    python run_tests_convenience.py tools          # Run tool organization tests
    python run_tests_convenience.py phase3 fast    # Run phase 3 fast tests
    python run_tests_convenience.py phase3 all     # Run all phase 3 tests
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    if len(sys.argv) < 2:
        print(__doc__)
        print("\nAvailable commands:")
        print(
            "  main           - Run comprehensive test suite (tests/runners/run_tests.py)"
        )
        print(
            "  modular        - Run modular agent tests (tests/runners/run_modular_agent_tests.py)"
        )
        print(
            "  tools          - Run tool organization tests (tests/runners/run_tool_organization_tests.py)"
        )
        print(
            "  phase3 <mode>  - Run phase 3 tests with specific mode (tests/runners/run_phase3_tests.py)"
        )
        print(
            "\nFor phase3 modes: agents, tools, integration, synthetic, fast, coverage, health, performance, all"
        )
        sys.exit(1)

    command = sys.argv[1]

    if command == "main":
        runner_script = "tests/runners/run_tests.py"
        cmd = [sys.executable, runner_script]

    elif command == "modular":
        runner_script = "tests/runners/run_modular_agent_tests.py"
        cmd = [sys.executable, runner_script]

    elif command == "tools":
        runner_script = "tests/runners/run_tool_organization_tests.py"
        cmd = [sys.executable, runner_script]

    elif command == "phase3":
        if len(sys.argv) < 3:
            print("Phase 3 requires a mode. Available modes:")
            print(
                "agents, tools, integration, synthetic, fast, coverage, health, performance, all"
            )
            sys.exit(1)
        mode = sys.argv[2]
        runner_script = "tests/runners/run_phase3_tests.py"
        cmd = [sys.executable, runner_script, mode]

    else:
        print(f"Unknown command: {command}")
        print("Run without arguments to see available commands")
        sys.exit(1)

    print(f"🚀 Running: {' '.join(cmd)}")
    print(f"📁 Working directory: {os.getcwd()}")
    print("=" * 60)

    # Execute the test runner
    result = subprocess.run(cmd)
    sys.exit(result.returncode)


if __name__ == "__main__":
    main()
