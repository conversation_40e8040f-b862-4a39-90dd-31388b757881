/**
 * Simplified Onboarding Test
 * Tests just the basic functionality without complex validation
 */

const FRONTEND_URL = "http://localhost:3000";

async function testBasicOnboardingFlow() {
  console.log("🧪 SIMPLE ONBOARDING TEST");
  console.log("Testing basic onboarding flow with sidebar updates");

  const userId = `simple-test-${Date.now()}`;
  const threadId = `thread-${Date.now()}`;

  const testSteps = [
    {
      message: "Hi, I want to start my fitness journey",
      expectedUpdates: ["greeting stage", "sport suggestions"],
    },
    {
      message: "Running",
      expectedUpdates: ["selected sport", "no more suggestions"],
    },
    {
      message: "I want to run a 5k race and lose weight",
      expectedUpdates: ["goals captured"],
    },
  ];

  console.log(`User ID: ${userId}`);
  console.log(`Thread ID: ${threadId}\n`);

  for (let i = 0; i < testSteps.length; i++) {
    const { message, expectedUpdates } = testSteps[i];
    console.log(`📨 Step ${i + 1}: "${message}"`);
    console.log(`🎯 Expected: ${expectedUpdates.join(", ")}`);

    try {
      const response = await fetch(`${FRONTEND_URL}/api/onboarding-python`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          threadId,
          userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body");
      }

      const decoder = new TextDecoder();
      let sidebarUpdates = 0;
      let lastSidebarData = null;
      let responseTokens = "";
      let eventCount = 0;

      const startTime = Date.now();
      const maxTime = 15000; // 15 seconds max per step

      while (Date.now() - startTime < maxTime) {
        const { done, value } = await reader.read();

        if (done) {
          console.log("   ✅ Stream completed");
          break;
        }

        const chunk = decoder.decode(value, { stream: true });

        if (chunk.trim()) {
          eventCount++;

          // Collect response tokens
          if (chunk.includes('"type": "token"')) {
            const contentMatch = chunk.match(/"content": "([^"]*?)"/);
            if (contentMatch) {
              responseTokens += contentMatch[1];
            }
          }

          // Look for sidebar updates
          if (chunk.includes("sidebar_update")) {
            try {
              const dataMatches = chunk.matchAll(/data: ({.*?})\n/g);
              for (const match of dataMatches) {
                const data = JSON.parse(match[1]);
                if (data.type === "sidebar_update" && data.sidebarData) {
                  sidebarUpdates++;
                  lastSidebarData = data.sidebarData;

                  console.log(`   🔄 Sidebar Update #${sidebarUpdates}:`);
                  console.log(`      Stage: ${data.sidebarData.currentStage}`);
                  console.log(
                    `      Goals exist: ${data.sidebarData.goals?.exists || false}`,
                  );
                  console.log(
                    `      Goals count: ${data.sidebarData.goals?.list?.length || 0}`,
                  );
                  console.log(
                    `      Summary items: ${data.sidebarData.summaryItems?.length || 0}`,
                  );
                  console.log(
                    `      Sport suggestions: ${data.sidebarData.sportSuggestions ? data.sidebarData.sportSuggestions.length : 0}`,
                  );
                  console.log(
                    `      Selected sports: ${data.sidebarData.selectedSports?.length || 0}`,
                  );
                }
              }
            } catch (e) {
              console.error("   ❌ Error parsing sidebar data:", e.message);
            }
          }

          // Stop on completion
          if (chunk.includes("agent_end") || chunk.includes("completion")) {
            console.log("   🏁 Agent completed");
            break;
          }
        }
      }

      console.log(
        `   📊 Results: ${sidebarUpdates} sidebar updates, ${eventCount} total events`,
      );
      console.log(
        `   💬 Response preview: "${responseTokens.substring(0, 80)}..."`,
      );

      if (sidebarUpdates > 0) {
        console.log(`   ✅ Step ${i + 1} PASSED - Received sidebar updates`);
      } else {
        console.log(`   ❌ Step ${i + 1} FAILED - No sidebar updates received`);
      }
    } catch (error) {
      console.error(`   ❌ Step ${i + 1} ERROR:`, error.message);
    }

    console.log(""); // Empty line between steps

    // Small delay between steps
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }
}

// Command line interface
if (process.argv.length > 2) {
  const command = process.argv[2];
  if (command === "run") {
    testBasicOnboardingFlow();
  } else {
    console.log("Usage: node test-simple-onboarding.js run");
  }
} else {
  testBasicOnboardingFlow();
}
