/**
 * Onboarding Data Flow Debugger
 *
 * Real-time debugging tool to monitor and visualize the onboarding data flow
 * from Python backend through the proxy to the frontend
 */

import { v4 as uuidv4 } from "uuid";

const FRONTEND_URL = "http://localhost:3000";
const PYTHON_BACKEND_URL = "http://localhost:8000";

class OnboardingDataDebugger {
  constructor() {
    this.userId = `debug-${Date.now()}`;
    this.threadId = `debug-thread-${Date.now()}`;
    this.dataHistory = [];
    this.eventCount = 0;
    this.startTime = Date.now();
  }

  logWithTimestamp(message, data = null) {
    const elapsed = Date.now() - this.startTime;
    const timestamp = `[${elapsed}ms]`;
    console.log(`${timestamp} ${message}`);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }

  captureDataSnapshot(source, eventType, data) {
    const snapshot = {
      timestamp: Date.now() - this.startTime,
      source,
      eventType,
      data: JSON.parse(JSON.stringify(data)), // Deep copy
      eventNumber: this.eventCount++,
    };
    this.dataHistory.push(snapshot);
    return snapshot;
  }

  validateDataStructure(data, source) {
    const issues = [];

    // Check for required fields in sidebar data
    if (data.sidebarData) {
      const sidebar = data.sidebarData;

      if (typeof sidebar.currentStage !== "string") {
        issues.push(
          `${source}: currentStage should be string, got ${typeof sidebar.currentStage}`,
        );
      }

      if (!sidebar.goals || typeof sidebar.goals.exists !== "boolean") {
        issues.push(`${source}: goals.exists should be boolean`);
      }

      if (!Array.isArray(sidebar.goals?.list)) {
        issues.push(`${source}: goals.list should be array`);
      }

      if (sidebar.summaryItems && !Array.isArray(sidebar.summaryItems)) {
        issues.push(`${source}: summaryItems should be array`);
      }

      if (sidebar.selectedSports && !Array.isArray(sidebar.selectedSports)) {
        issues.push(`${source}: selectedSports should be array`);
      }

      // Check for snake_case vs camelCase issues
      const pythonFields = [
        "current_stage",
        "summary_items",
        "generated_plan",
        "sport_suggestions",
        "selected_sport",
        "selected_sports",
      ];
      const foundPythonFields = pythonFields.filter(
        (field) => field in sidebar,
      );
      if (foundPythonFields.length > 0) {
        issues.push(
          `${source}: Found Python snake_case fields: ${foundPythonFields.join(", ")}`,
        );
      }
    }

    return issues;
  }

  async testDirectPythonBackend(message) {
    this.logWithTimestamp("🐍 Testing direct Python backend...");

    try {
      const response = await fetch(`${PYTHON_BACKEND_URL}/api/onboarding`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          user_id: this.userId + "_direct",
          thread_id: this.threadId + "_direct",
        }),
      });

      if (!response.ok) {
        throw new Error(`Python backend error: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body from Python backend");
      }

      const decoder = new TextDecoder();
      let pythonData = null;

      this.logWithTimestamp("🌊 Processing Python backend stream...");

      // Process only a few events to capture the data format
      for (let i = 0; i < 10; i++) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        if (chunk.includes('"type": "sidebar_update"')) {
          const dataMatches = chunk.matchAll(/data: ({.*?})\n/g);
          for (const match of dataMatches) {
            try {
              const eventData = JSON.parse(match[1]);
              if (
                eventData.type === "sidebar_update" &&
                eventData.sidebarData
              ) {
                pythonData = eventData.sidebarData;
                this.captureDataSnapshot(
                  "Python Backend",
                  "sidebar_update",
                  eventData,
                );

                this.logWithTimestamp(
                  "📋 Python backend sidebar data received:",
                );
                console.log("Raw Python Format:");
                console.log(JSON.stringify(pythonData, null, 2));

                const issues = this.validateDataStructure(
                  { sidebarData: pythonData },
                  "Python",
                );
                if (issues.length > 0) {
                  this.logWithTimestamp(
                    "⚠️ Python backend data issues:",
                    issues,
                  );
                }
                break;
              }
            } catch (e) {
              this.logWithTimestamp(
                "❌ Error parsing Python backend data:",
                e.message,
              );
            }
          }
        }
      }

      reader.cancel();
      return pythonData;
    } catch (error) {
      this.logWithTimestamp("❌ Python backend test failed:", error.message);
      return null;
    }
  }

  async testProxyTransformation(message) {
    this.logWithTimestamp("🔄 Testing proxy transformation...");

    try {
      const response = await fetch(`${FRONTEND_URL}/api/onboarding-python`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          threadId: this.threadId,
          userId: this.userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Proxy error: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body from proxy");
      }

      const decoder = new TextDecoder();
      let proxyData = null;

      this.logWithTimestamp("🌊 Processing proxy stream...");

      // Process only a few events to capture the data format
      for (let i = 0; i < 10; i++) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        if (chunk.includes('"type": "sidebar_update"')) {
          const dataMatches = chunk.matchAll(/data: ({.*?})\n/g);
          for (const match of dataMatches) {
            try {
              const eventData = JSON.parse(match[1]);
              if (
                eventData.type === "sidebar_update" &&
                eventData.sidebarData
              ) {
                proxyData = eventData.sidebarData;
                this.captureDataSnapshot(
                  "Proxy Route",
                  "sidebar_update",
                  eventData,
                );

                this.logWithTimestamp(
                  "📋 Proxy transformed sidebar data received:",
                );
                console.log("Transformed TypeScript Format:");
                console.log(JSON.stringify(proxyData, null, 2));

                const issues = this.validateDataStructure(
                  { sidebarData: proxyData },
                  "Proxy",
                );
                if (issues.length > 0) {
                  this.logWithTimestamp("⚠️ Proxy data issues:", issues);
                } else {
                  this.logWithTimestamp("✅ Proxy data validation passed");
                }
                break;
              }
            } catch (e) {
              this.logWithTimestamp("❌ Error parsing proxy data:", e.message);
            }
          }
        }
      }

      reader.cancel();
      return proxyData;
    } catch (error) {
      this.logWithTimestamp("❌ Proxy test failed:", error.message);
      return null;
    }
  }

  compareDataFormats(pythonData, proxyData) {
    this.logWithTimestamp("🔍 Comparing Python vs Proxy data formats...");

    if (!pythonData || !proxyData) {
      this.logWithTimestamp("❌ Cannot compare - missing data");
      return;
    }

    // Check field transformations
    const fieldMappings = {
      current_stage: "currentStage",
      summary_items: "summaryItems",
      generated_plan: "generatedPlan",
      sport_suggestions: "sportSuggestions",
      selected_sport: "selectedSport",
      selected_sports: "selectedSports",
    };

    console.log("\n📊 FIELD TRANSFORMATION ANALYSIS:");
    console.log("=" * 40);

    let transformationIssues = 0;

    for (const [pythonField, tsField] of Object.entries(fieldMappings)) {
      const pythonValue = pythonData[pythonField];
      const tsValue = proxyData[tsField];

      if (pythonValue !== undefined && tsValue !== undefined) {
        // Deep compare values
        const valuesMatch =
          JSON.stringify(pythonValue) === JSON.stringify(tsValue);
        console.log(
          `✅ ${pythonField} → ${tsField}: ${valuesMatch ? "PRESERVED" : "MODIFIED"}`,
        );

        if (!valuesMatch) {
          transformationIssues++;
          console.log(`   Python: ${JSON.stringify(pythonValue)}`);
          console.log(`   TypeScript: ${JSON.stringify(tsValue)}`);
        }
      } else if (pythonValue !== undefined && tsValue === undefined) {
        transformationIssues++;
        console.log(`❌ ${pythonField} → ${tsField}: LOST IN TRANSFORMATION`);
      } else if (pythonValue === undefined && tsValue !== undefined) {
        console.log(`ℹ️ ${pythonField} → ${tsField}: ADDED IN TRANSFORMATION`);
      }
    }

    // Check goals structure specifically
    if (pythonData.goals && proxyData.goals) {
      const goalsMatch =
        pythonData.goals.exists === proxyData.goals.exists &&
        JSON.stringify(pythonData.goals.list) ===
          JSON.stringify(proxyData.goals.list);
      console.log(
        `${goalsMatch ? "✅" : "❌"} goals structure: ${goalsMatch ? "PRESERVED" : "MODIFIED"}`,
      );
      if (!goalsMatch) transformationIssues++;
    }

    console.log(`\n🎯 Transformation Issues Found: ${transformationIssues}`);
    return transformationIssues === 0;
  }

  printDataHistory() {
    console.log("\n📚 DATA FLOW HISTORY:");
    console.log("=" * 30);

    this.dataHistory.forEach((snapshot, index) => {
      console.log(
        `${index + 1}. [${snapshot.timestamp}ms] ${snapshot.source} - ${snapshot.eventType}`,
      );

      if (snapshot.data.sidebarData) {
        const sidebar = snapshot.data.sidebarData;
        console.log(
          `   Stage: ${sidebar.currentStage || sidebar.current_stage}`,
        );
        console.log(`   Goals: ${(sidebar.goals?.list || []).length} items`);
        console.log(
          `   Summary: ${(sidebar.summaryItems || sidebar.summary_items || []).length} items`,
        );
        console.log(
          `   Sports: ${(sidebar.selectedSports || sidebar.selected_sports || []).length} selected`,
        );
        console.log(
          `   Plan: ${sidebar.generatedPlan || sidebar.generated_plan ? "Generated" : "None"}`,
        );
      }
      console.log();
    });
  }

  async runCompleteDebugSession() {
    console.log("🚀 ONBOARDING DATA FLOW DEBUG SESSION");
    console.log("=" * 50);
    console.log(`Debug Session ID: ${this.userId}`);
    console.log(`Thread ID: ${this.threadId}`);
    console.log();

    // Test with a message that should trigger sidebar updates
    const testMessage =
      "Hi, I want to start running and build strength. I'm a beginner.";

    // Test 1: Direct Python backend
    const pythonData = await this.testDirectPythonBackend(testMessage);

    // Test 2: Proxy transformation
    const proxyData = await this.testProxyTransformation(testMessage);

    // Test 3: Compare formats
    if (pythonData && proxyData) {
      const transformationSuccess = this.compareDataFormats(
        pythonData,
        proxyData,
      );
      this.logWithTimestamp(
        `🎯 Transformation Success: ${transformationSuccess ? "YES" : "NO"}`,
      );
    }

    // Print complete history
    this.printDataHistory();

    const totalTime = Date.now() - this.startTime;
    console.log(`\n⏱️ Total Debug Session Time: ${totalTime}ms`);
    console.log(`📈 Total Events Captured: ${this.eventCount}`);

    return {
      pythonData,
      proxyData,
      dataHistory: this.dataHistory,
      totalTime,
    };
  }
}

// Interactive debugging functions
async function debugSingleMessage(message) {
  const debuggerInstance = new OnboardingDataDebugger();
  console.log(`🔍 Debugging single message: "${message}"`);
  return await debuggerInstance.runCompleteDebugSession();
}

async function debugOnboardingFlow() {
  const debuggerInstance = new OnboardingDataDebugger();

  const messages = [
    "Hi, I want to start my fitness journey",
    "Running, Strength Training",
    "I want to run a 10k and build muscle",
    "I'm a beginner and can workout 3 times per week",
  ];

  console.log("🔄 DEBUGGING COMPLETE ONBOARDING FLOW");
  console.log("=" * 45);

  for (let i = 0; i < messages.length; i++) {
    console.log(`\n📨 Message ${i + 1}: "${messages[i]}"`);

    const proxyData = await debuggerInstance.testProxyTransformation(
      messages[i],
    );

    if (proxyData) {
      console.log(`✅ Step ${i + 1} completed successfully`);
    } else {
      console.log(`❌ Step ${i + 1} failed`);
      break;
    }

    // Small delay between messages
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  debuggerInstance.printDataHistory();
  return debuggerInstance.dataHistory;
}

async function debugSpecificDataField(fieldName) {
  console.log(`🎯 DEBUGGING SPECIFIC FIELD: ${fieldName}`);
  console.log("=" * 40);

  const debuggerInstance = new OnboardingDataDebugger();

  // Choose message based on field
  let testMessage;
  switch (fieldName) {
    case "goals":
      testMessage = "I want to run a marathon and build muscle";
      break;
    case "sports":
      testMessage = "Running, Swimming, Strength Training";
      break;
    case "summaryItems":
      testMessage =
        "I'm intermediate level, can workout 4 times per week, have gym access";
      break;
    default:
      testMessage = "Hi, I want to start my fitness journey";
  }

  const result = await debuggerInstance.runCompleteDebugSession();

  // Focus on the specific field
  if (result.proxyData) {
    console.log(`\n🔍 FOCUSED ANALYSIS - ${fieldName}:`);
    const fieldValue = result.proxyData[fieldName];
    console.log(JSON.stringify(fieldValue, null, 2));
  }

  return result;
}

// Export functions for use
export {
  OnboardingDataDebugger,
  debugSingleMessage,
  debugOnboardingFlow,
  debugSpecificDataField,
};

// Command line interface
if (process.argv[1] === new URL(import.meta.url).pathname) {
  const command = process.argv[2];
  const argument = process.argv[3];

  switch (command) {
    case "message":
      debugSingleMessage(argument || "Hi, I want to start my fitness journey");
      break;
    case "flow":
      debugOnboardingFlow();
      break;
    case "field":
      debugSpecificDataField(argument || "goals");
      break;
    default:
      console.log("🛠️ Onboarding Data Flow Debugger");
      console.log("Usage:");
      console.log(
        "  node debug-onboarding-data-flow.js message 'Your message here'",
      );
      console.log("  node debug-onboarding-data-flow.js flow");
      console.log(
        "  node debug-onboarding-data-flow.js field goals|sports|summaryItems",
      );

      // Run default debug session
      const debuggerInstance = new OnboardingDataDebugger();
      debuggerInstance.runCompleteDebugSession();
  }
}
