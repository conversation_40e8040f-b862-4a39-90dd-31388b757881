#!/usr/bin/env node

/**
 * Test script for onboarding integration between athlea-web frontend and python-langgraph backend
 */

// Using built-in fetch (Node.js 18+)

// Test configuration
const FRONTEND_URL = "http://localhost:3000"; // Next.js dev server
const PYTHON_BACKEND_URL = "http://localhost:8000"; // Python backend
const TEST_USER_ID = "test_user_" + Date.now();
const TEST_THREAD_ID = "test_thread_" + Date.now();

console.log("🧪 Testing Onboarding Integration");
console.log("=" * 50);
console.log(`Frontend URL: ${FRONTEND_URL}`);
console.log(`Python Backend URL: ${PYTHON_BACKEND_URL}`);
console.log(`Test User ID: ${TEST_USER_ID}`);
console.log(`Test Thread ID: ${TEST_THREAD_ID}`);
console.log("");

/**
 * Test the direct Python backend health endpoint
 */
async function testPythonBackendHealth() {
  console.log("🔍 Testing Python backend health directly");

  const url = `${PYTHON_BACKEND_URL}/api/health`;
  console.log(`🔗 Request URL: ${url}`);

  try {
    const response = await fetch(url, {
      method: "GET",
      timeout: 5000,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const healthData = await response.json();
    console.log("✅ Python backend is healthy:", healthData);
    return { success: true, healthData };
  } catch (error) {
    console.error("❌ Python backend health check failed:", error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test the onboarding-python proxy route (GET method)
 */
async function testOnboardingProxyGet() {
  console.log("\n📤 Testing GET /api/onboarding-python");

  const params = new URLSearchParams({
    userId: TEST_USER_ID,
    threadId: TEST_THREAD_ID,
    message: "Hi! I want to start my fitness journey.",
    initialMessage: "start_onboarding",
  });

  const url = `${FRONTEND_URL}/api/onboarding-python?${params}`;
  console.log(`🔗 Request URL: ${url}`);

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "text/event-stream",
        "Cache-Control": "no-cache",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log("✅ GET request successful, processing SSE stream...");
    console.log("📡 Response headers:", Object.fromEntries(response.headers));

    // Process SSE stream for a limited time
    let eventCount = 0;
    let lastEvent = null;

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      const timeout = setTimeout(() => {
        console.log("⏰ Timeout reached, stopping stream test");
        reader.cancel();
      }, 10000); // 10 second timeout

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        if (chunk.trim()) {
          eventCount++;
          console.log(
            `📨 Event ${eventCount}:`,
            chunk.substring(0, 200) + "...",
          );
          lastEvent = chunk;
        }

        // Stop after 5 events for testing
        if (eventCount >= 5) {
          console.log("🛑 Stopping after 5 events for testing");
          clearTimeout(timeout);
          break;
        }
      }

      clearTimeout(timeout);
    } finally {
      reader.releaseLock();
    }

    console.log(`✅ GET test completed. Received ${eventCount} events.`);
    return { success: true, eventCount, lastEvent };
  } catch (error) {
    console.error("❌ GET test failed:", error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test the onboarding-python proxy route (POST method)
 */
async function testOnboardingProxyPost() {
  console.log("\n📤 Testing POST /api/onboarding-python");

  const testBody = {
    userId: TEST_USER_ID,
    threadId: TEST_THREAD_ID,
    message:
      "I want to build muscle and lose weight. I have experience with gym workouts.",
    userProfile: {
      name: "Test User",
      age: 28,
      goals: ["muscle_building", "weight_loss"],
    },
  };

  const url = `${FRONTEND_URL}/api/onboarding-python`;
  console.log(`🔗 Request URL: ${url}`);
  console.log("📋 Request body:", JSON.stringify(testBody, null, 2));

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "text/event-stream",
      },
      body: JSON.stringify(testBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `HTTP ${response.status}: ${response.statusText}\n${errorText}`,
      );
    }

    console.log("✅ POST request successful, processing SSE stream...");
    console.log("📡 Response headers:", Object.fromEntries(response.headers));

    // Process SSE stream for a limited time
    let eventCount = 0;
    let tokenCount = 0;
    let sidebarUpdates = 0;

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      const timeout = setTimeout(() => {
        console.log("⏰ Timeout reached, stopping stream test");
        reader.cancel();
      }, 15000); // 15 second timeout

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        if (chunk.trim()) {
          eventCount++;

          // Count different event types
          if (chunk.includes("event: token")) {
            tokenCount++;
          } else if (chunk.includes("sidebar_update")) {
            sidebarUpdates++;
            console.log(
              `📋 Sidebar update ${sidebarUpdates}:`,
              chunk.substring(0, 300) + "...",
            );
          } else {
            console.log(
              `📨 Event ${eventCount}:`,
              chunk.substring(0, 200) + "...",
            );
          }
        }

        // Stop after 10 events for testing
        if (eventCount >= 10) {
          console.log("🛑 Stopping after 10 events for testing");
          clearTimeout(timeout);
          break;
        }
      }

      clearTimeout(timeout);
    } finally {
      reader.releaseLock();
    }

    console.log(
      `✅ POST test completed. Events: ${eventCount}, Tokens: ${tokenCount}, Sidebar updates: ${sidebarUpdates}`,
    );
    return { success: true, eventCount, tokenCount, sidebarUpdates };
  } catch (error) {
    console.error("❌ POST test failed:", error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log("🚀 Starting integration tests...\n");

  const results = {
    pythonHealth: await testPythonBackendHealth(),
    getProxy: await testOnboardingProxyGet(),
    postProxy: await testOnboardingProxyPost(),
  };

  console.log("\n📊 Test Results Summary:");
  console.log("=" * 30);
  console.log(
    `Python Backend Health: ${results.pythonHealth.success ? "✅" : "❌"}`,
  );
  console.log(`GET Proxy Test: ${results.getProxy.success ? "✅" : "❌"}`);
  console.log(`POST Proxy Test: ${results.postProxy.success ? "✅" : "❌"}`);

  if (results.getProxy.success) {
    console.log(`  - GET events received: ${results.getProxy.eventCount}`);
  }

  if (results.postProxy.success) {
    console.log(`  - POST events received: ${results.postProxy.eventCount}`);
    console.log(`  - Tokens streamed: ${results.postProxy.tokenCount}`);
    console.log(`  - Sidebar updates: ${results.postProxy.sidebarUpdates}`);
  }

  const allPassed =
    results.pythonHealth.success &&
    results.getProxy.success &&
    results.postProxy.success;
  console.log(
    `\n🎯 Overall Result: ${allPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"}`,
  );

  if (!allPassed) {
    console.log("\n❌ Failed Tests:");
    if (!results.pythonHealth.success) {
      console.log(`  - Python Backend: ${results.pythonHealth.error}`);
    }
    if (!results.getProxy.success) {
      console.log(`  - GET Proxy: ${results.getProxy.error}`);
    }
    if (!results.postProxy.success) {
      console.log(`  - POST Proxy: ${results.postProxy.error}`);
    }
  }

  return allPassed;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error("💥 Test execution failed:", error);
      process.exit(1);
    });
}

module.exports = {
  runTests,
  testOnboardingProxyGet,
  testOnboardingProxyPost,
  testPythonBackendHealth,
};
