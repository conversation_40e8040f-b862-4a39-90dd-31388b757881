"""
Tests for Nutrition Agent

This module contains comprehensive tests for the nutrition agent,
including basic functionality, integration scenarios, and edge cases.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, Mock, patch
from langchain_core.messages import HumanMessage, AIMessage

from athlea_langgraph.agents.nutrition_agent import NutritionAgent, nutrition_agent_node
from athlea_langgraph.states.state import AgentState

from ..fixtures.user_profiles import (
    ALL_PROFILES,
    BEGINNER_WEIGHT_LOSS,
    INTERMEDIATE_BODYBUILDING,
    VEGAN_ATHLETE,
    OLDER_ADULT,
    NUTRITION_FOCUSED_PROFILES,
    get_profiles_with_dietary_restrictions,
)
from ..synthetic.synthetic_coaching_sessions import (
    ALL_SESSIONS,
    SIMPLE_NUTRITION_QUERY,
    VEGAN_ATHLETE_CONSULTATION,
    LIFE_CHANGE_CONSULTATION,
)


class TestNutritionAgent:
    """Test cases for the NutritionAgent class."""

    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.agent = NutritionAgent()

    def test_agent_initialization(self):
        """Test that the agent initializes correctly."""
        assert self.agent.name == "nutrition_agent"
        assert self.agent.domain == "nutrition"
        assert self.agent.system_prompt is not None
        assert len(self.agent.system_prompt) > 0

        # Test agent configuration
        assert hasattr(self.agent, "domain")
        assert hasattr(self.agent, "name")
        assert hasattr(self.agent, "system_prompt")

        # Test that agent is properly configured for nutrition domain
        assert "nutrition" in self.agent.system_prompt.lower()
        assert (
            "food" in self.agent.system_prompt.lower()
            or "diet" in self.agent.system_prompt.lower()
        )

    def test_get_domain_prompt(self):
        """Test that the agent loads the correct domain prompt."""
        prompt = self.agent.get_domain_prompt()
        assert prompt is not None
        assert len(prompt) > 0
        assert "nutrition" in prompt.lower()

        # Test prompt contains nutrition-specific guidance
        assert any(
            word in prompt.lower() for word in ["diet", "food", "calories", "macro"]
        )

    def test_agent_properties(self):
        """Test agent properties and attributes."""
        assert self.agent.domain == "nutrition"
        assert self.agent.name == "nutrition_agent"
        assert isinstance(self.agent.system_prompt, str)
        assert len(self.agent.system_prompt.strip()) > 0

    @pytest.mark.asyncio
    async def test_get_domain_tools(self):
        """Test that the agent loads domain-specific tools correctly."""
        # Test that the method exists
        assert hasattr(self.agent, "get_domain_tools")
        assert callable(self.agent.get_domain_tools)

        tools = await self.agent.get_domain_tools()

        # Should handle error gracefully and return empty list
        assert isinstance(tools, list)
        assert len(tools) > 0


class TestNutritionAgentNode:
    """Test cases for the nutrition_agent_node function."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_node_basic_functionality(self, mock_process: AsyncMock):
        """Test basic node functionality."""
        state = {
            "messages": [HumanMessage(content="I need nutrition advice")],
            "user_query": "I need nutrition advice",
            "user_profile": BEGINNER_WEIGHT_LOSS,
        }

        # Mock the agent process method
        mock_process.return_value = {
            "response": "For weight loss, focus on a caloric deficit with balanced macronutrients...",
            "specialist_completed": True,
            "metadata": {"tools_used": ["calculate_macros"]},
        }

        result = await nutrition_agent_node(state, {})

        # Verify result structure
        assert "messages" in result
        assert "specialist_completed" in result
        assert "current_agent" in result
        assert result["current_agent"] == "nutrition_agent"
        assert result["specialist_completed"] is True
        mock_process.assert_called_once()


class TestNutritionAgentIntegration:
    """Integration tests for nutrition agent with different scenarios."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_weight_loss_nutrition_scenario(self, mock_process: AsyncMock):
        """Test handling of weight loss nutrition request."""
        session = SIMPLE_NUTRITION_QUERY

        state = {
            "messages": [HumanMessage(content=session["initial_query"])],
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        mock_process.return_value = {
            "response": "As a vegetarian looking to lose weight, focus on protein-rich plant foods, whole grains, and vegetables. Aim for a 500-calorie deficit daily.",
            "specialist_completed": True,
            "metadata": {
                "reasoning_steps": [
                    "Calculated caloric needs",
                    "Considered vegetarian restriction",
                ],
                "tools_used": ["calculate_macros", "azure_cognitive_search"],
            },
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert any(
            word in response_content.lower()
            for word in ["vegetarian", "protein", "deficit"]
        )
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_vegan_athlete_nutrition_scenario(self, mock_process: AsyncMock):
        """Test handling of vegan athlete nutrition needs."""
        session = VEGAN_ATHLETE_CONSULTATION

        state = {
            "messages": [HumanMessage(content=session["initial_query"])],
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        mock_process.return_value = {
            "response": "For vegan endurance athletes, focus on complex carbs for energy, complete proteins from legumes and quinoa, and consider B12 supplementation. Time your nutrition around training.",
            "specialist_completed": True,
            "metadata": {
                "reasoning_steps": [
                    "Analyzed vegan requirements",
                    "Calculated endurance needs",
                ],
                "tools_used": ["azure_cognitive_search", "calculate_macros"],
            },
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert any(
            word in response_content.lower()
            for word in ["vegan", "endurance", "carbs", "protein"]
        )
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_postpartum_nutrition_scenario(self, mock_process: AsyncMock):
        """Test handling of postpartum/breastfeeding nutrition needs."""
        session = LIFE_CHANGE_CONSULTATION

        state = {
            "messages": [HumanMessage(content=session["initial_query"])],
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        mock_process.return_value = {
            "response": "While breastfeeding, you need additional 300-500 calories daily. Focus on nutrient-dense foods and stay hydrated. Avoid restrictive dieting.",
            "specialist_completed": True,
            "metadata": {
                "reasoning_steps": [
                    "Assessed breastfeeding needs",
                    "Safety considerations",
                ],
                "tools_used": ["calculate_macros", "azure_cognitive_search"],
            },
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert any(
            word in response_content.lower()
            for word in ["breastfeeding", "calories", "hydrated"]
        )
        mock_process.assert_called_once()


class TestNutritionAgentWithUserProfiles:
    """Test nutrition agent with different user profile scenarios."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_all_nutrition_focused_profiles(self, mock_process: AsyncMock):
        """Test agent with all nutrition-focused user profiles."""
        for profile in NUTRITION_FOCUSED_PROFILES:
            state = {
                "messages": [HumanMessage(content="I need nutrition advice")],
                "user_query": "I need nutrition advice",
                "user_profile": profile,
            }

            mock_process.return_value = {
                "response": f"Nutrition advice for {profile['name']} with goals: {', '.join(profile['goals'])}",
                "specialist_completed": True,
                "metadata": {},
            }

            result = await nutrition_agent_node(state, {})

            assert result["specialist_completed"] is True
            assert profile["name"] in result["messages"][-1]["content"]
            mock_process.assert_called_with(state, {})
            mock_process.reset_mock()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_dietary_restrictions_handling(self, mock_process: AsyncMock):
        """Test handling of various dietary restrictions."""
        profiles_with_restrictions = get_profiles_with_dietary_restrictions()

        for profile in profiles_with_restrictions[:3]:  # Test first 3
            state = {
                "messages": [
                    HumanMessage(content="What can I eat with my dietary restrictions?")
                ],
                "user_query": "What can I eat with my dietary restrictions?",
                "user_profile": profile,
            }

            restrictions = profile.get("dietary_restrictions", [])
            mock_process.return_value = {
                "response": f"With your {', '.join(restrictions)} diet, you can eat these foods...",
                "specialist_completed": True,
                "metadata": {"dietary_restrictions_considered": restrictions},
            }

            result = await nutrition_agent_node(state, {})

            assert result["specialist_completed"] is True
            # Verify dietary restrictions are mentioned in response
            response_content = result["messages"][-1]["content"]
            for restriction in restrictions:
                assert restriction in response_content
            mock_process.assert_called_with(state, {})
            mock_process.reset_mock()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_weight_loss_vs_muscle_gain_nutrition(self, mock_process: AsyncMock):
        """Test that nutrition advice differs for weight loss vs muscle gain goals."""
        weight_loss_state = {
            "messages": [HumanMessage(content="What should I eat for my goals?")],
            "user_query": "What should I eat for my goals?",
            "user_profile": BEGINNER_WEIGHT_LOSS,
        }

        muscle_gain_state = {
            "messages": [HumanMessage(content="What should I eat for my goals?")],
            "user_query": "What should I eat for my goals?",
            "user_profile": INTERMEDIATE_BODYBUILDING,
        }

        # Mock different responses for different goals
        def mock_process_side_effect(state, config=None):
            profile = state["user_profile"]
            if "weight_loss" in profile["goals"]:
                return {
                    "response": "For weight loss, create a caloric deficit with high protein, moderate carbs, and healthy fats.",
                    "specialist_completed": True,
                    "metadata": {"goal_focus": "weight_loss"},
                }
            elif "muscle_gain" in profile["goals"]:
                return {
                    "response": "For muscle gain, eat in a slight caloric surplus with high protein, complex carbs, and adequate fats.",
                    "specialist_completed": True,
                    "metadata": {"goal_focus": "muscle_gain"},
                }

        mock_process.side_effect = mock_process_side_effect

        # Test weight loss scenario
        weight_loss_result = await nutrition_agent_node(weight_loss_state, {})
        assert "deficit" in weight_loss_result["messages"][-1]["content"]

        # Test muscle gain scenario
        muscle_gain_result = await nutrition_agent_node(muscle_gain_state, {})
        assert "surplus" in muscle_gain_result["messages"][-1]["content"]

        # Verify both were called
        assert mock_process.call_count == 2


class TestNutritionAgentEdgeCases:
    """Test edge cases and error scenarios for nutrition agent."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_empty_user_profile(self, mock_process: AsyncMock):
        """Test agent behavior with empty user profile."""
        state = {
            "messages": [HumanMessage(content="I need nutrition advice")],
            "user_query": "I need nutrition advice",
            "user_profile": {},
        }

        mock_process.return_value = {
            "response": "I'd be happy to help with nutrition! To provide personalized advice, could you tell me about your goals?",
            "specialist_completed": True,
            "metadata": {"profile_status": "empty"},
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_conflicting_dietary_restrictions(self, mock_process: AsyncMock):
        """Test handling of conflicting dietary restrictions."""
        state = {
            "messages": [HumanMessage(content="Help me with my diet")],
            "user_query": "Help me with my diet",
            "user_profile": {
                "name": "Conflicted User",
                "dietary_restrictions": ["vegan", "high_protein", "low_carb"],
                "goals": ["muscle_gain"],
            },
        }

        mock_process.return_value = {
            "response": "I see you have some challenging dietary restrictions. Let's find plant-based proteins that work for you.",
            "specialist_completed": True,
            "metadata": {"conflict_detected": True},
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_malformed_nutrition_query(self, mock_process: AsyncMock):
        """Test handling of malformed or unclear nutrition queries."""
        state = {
            "messages": [HumanMessage(content="food good bad help")],
            "user_query": "food good bad help",
            "user_profile": BEGINNER_WEIGHT_LOSS,
        }

        mock_process.return_value = {
            "response": "Could you provide more specific details about what nutrition help you're looking for?",
            "specialist_completed": False,
            "metadata": {"query_clarity": "low"},
        }

        result = await nutrition_agent_node(state, {})

        # Should handle unclear queries gracefully
        assert result["specialist_completed"] is False
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_pregnancy_safety_considerations(self, mock_process: AsyncMock):
        """Test that agent considers safety for pregnant users."""
        state = {
            "messages": [HumanMessage(content="I'm pregnant and want to lose weight")],
            "user_query": "I'm pregnant and want to lose weight",
            "user_profile": {
                "name": "Pregnant User",
                "life_stage": "pregnant",
                "goals": ["weight_loss"],
            },
        }

        mock_process.return_value = {
            "response": "During pregnancy, weight loss is not recommended. Focus on healthy nutrition for you and your baby's development. Please consult your healthcare provider.",
            "specialist_completed": True,
            "metadata": {"safety_warning": True, "pregnancy_detected": True},
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert "pregnancy" in response_content.lower()
        assert "healthcare provider" in response_content.lower()
        mock_process.assert_called_once()


class TestNutritionAgentSpecializedScenarios:
    """Test specialized nutrition scenarios."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_meal_timing_for_athletes(self, mock_process: AsyncMock):
        """Test meal timing recommendations for athletes."""
        state = {
            "messages": [HumanMessage(content="When should I eat around my workouts?")],
            "user_query": "When should I eat around my workouts?",
            "user_profile": {
                "name": "Athlete",
                "activity_level": "very_active",
                "goals": ["performance"],
                "training_schedule": "morning",
            },
        }

        mock_process.return_value = {
            "response": "For morning workouts, eat a light carb snack 30-60 minutes before training, and have a protein-rich meal within 2 hours after.",
            "specialist_completed": True,
            "metadata": {"meal_timing_advice": True},
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert any(
            word in response_content.lower() for word in ["before", "after", "workout"]
        )
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_supplement_recommendations(self, mock_process: AsyncMock):
        """Test supplement recommendation handling."""
        state = {
            "messages": [HumanMessage(content="What supplements should I take?")],
            "user_query": "What supplements should I take?",
            "user_profile": VEGAN_ATHLETE,
        }

        mock_process.return_value = {
            "response": "As a vegan athlete, consider B12, vitamin D, and plant-based protein powder. Always consult a healthcare professional before starting supplements.",
            "specialist_completed": True,
            "metadata": {"supplement_advice": True, "safety_disclaimer": True},
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert "healthcare professional" in response_content.lower()
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_hydration_guidance(self, mock_process: AsyncMock):
        """Test hydration guidance provision."""
        state = {
            "messages": [HumanMessage(content="How much water should I drink?")],
            "user_query": "How much water should I drink?",
            "user_profile": {
                "name": "Active User",
                "activity_level": "active",
                "goals": ["performance"],
                "environment": "hot_climate",
            },
        }

        mock_process.return_value = {
            "response": "In hot climates with active training, aim for 3-4 liters per day, with additional 500-750ml per hour of exercise. Monitor urine color as a hydration indicator.",
            "specialist_completed": True,
            "metadata": {"hydration_advice": True, "climate_considered": True},
        }

        result = await nutrition_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert any(
            word in response_content.lower()
            for word in ["water", "hydration", "liters"]
        )
        mock_process.assert_called_once()


class TestNutritionAgentPerformance:
    """Performance tests for nutrition agent."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_multiple_concurrent_nutrition_requests(
        self, mock_process: AsyncMock
    ):
        """Test handling multiple concurrent nutrition requests."""
        states = [
            {
                "messages": [HumanMessage(content=f"Nutrition question {i}")],
                "user_query": f"Nutrition question {i}",
                "user_profile": BEGINNER_WEIGHT_LOSS,
            }
            for i in range(5)
        ]

        mock_process.return_value = {
            "response": "Nutrition advice provided",
            "specialist_completed": True,
            "metadata": {},
        }

        # Process all requests concurrently
        tasks = [nutrition_agent_node(state, {}) for state in states]
        results = await asyncio.gather(*tasks)

        # Verify all completed successfully
        assert len(results) == 5
        for result in results:
            assert result["specialist_completed"] is True

        # Verify all were processed
        assert mock_process.call_count == 5


class TestNutritionAgentScenarios:
    """Test nutrition agent with synthetic scenarios."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.nutrition_agent.NutritionAgent.process")
    async def test_generated_scenarios(self, mock_process: AsyncMock):
        """Test agent with generated nutrition scenarios."""
        nutrition_sessions = [
            session
            for session in ALL_SESSIONS
            if "nutrition" in session.get("expected_agents", [])
        ]

        for session in nutrition_sessions[:3]:  # Test first 3
            state = {
                "messages": [HumanMessage(content=session["initial_query"])],
                "user_query": session["initial_query"],
                "user_profile": session["user_profile"],
            }

            mock_process.return_value = {
                "response": f"Nutrition guidance for: {session['initial_query'][:50]}...",
                "specialist_completed": True,
                "metadata": {"scenario": session["scenario_id"]},
            }

            result = await nutrition_agent_node(state, {})

            assert result["specialist_completed"] is True
            mock_process.assert_called_with(state, {})
            mock_process.reset_mock()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
