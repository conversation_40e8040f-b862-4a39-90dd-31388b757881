import asyncio
import json
from unittest.mock import patch, AsyncMock
import pytest
import pytest_asyncio
from langchain_core.messages import HumanMessage
from athlea_langgraph.agents.recovery_agent import RecoveryAgent
from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from langgraph.prebuilt import create_react_agent

pytestmark = pytest.mark.asyncio


@pytest_asyncio.fixture
async def recovery_agent_with_tools():
    agent = RecoveryAgent()
    await agent.get_domain_tools()
    return agent


async def test_mobility_tool_call(recovery_agent_with_tools):
    """Test that the recovery agent has the mobility tool available."""
    agent = recovery_agent_with_tools

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "generate_mobility_protocol" in tool_names

    # Test that the agent can process mobility requests
    from athlea_langgraph.states.state import AgentState

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "My hips feel tight, I need a mobility routine for them. I do a lot of running.",
            }
        ],
        user_query="My hips feel tight, I need a mobility routine for them. I do a lot of running.",
    )

    result = await agent.process(state, {})

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


async def test_sleep_tool_call(recovery_agent_with_tools):
    """Test that the recovery agent has the sleep optimization tool available."""
    agent = recovery_agent_with_tools

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "optimize_sleep" in tool_names

    # Test that the agent can process sleep requests
    from athlea_langgraph.states.state import AgentState

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I can't sleep. I go to bed at midnight and wake up at 6am. My sleep quality is 3/10. It takes me an hour to fall asleep. I wake up 4 times. My alertness is 2/10.",
            }
        ],
        user_query="I can't sleep. I go to bed at midnight and wake up at 6am. My sleep quality is 3/10. It takes me an hour to fall asleep. I wake up 4 times. My alertness is 2/10.",
    )

    result = await agent.process(state, {})

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0
