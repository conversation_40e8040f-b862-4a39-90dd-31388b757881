"""
Tests for Strength Agent

This module contains comprehensive tests for the strength agent,
including basic functionality, integration scenarios, and edge cases.
"""

import asyncio
import logging
from typing import Any, Dict, List
from unittest.mock import AsyncMock, Mock, patch

import pytest
from langchain_core.messages import AIMessage, HumanMessage

from athlea_langgraph.agents.strength_agent import (
    StrengthAgent,
    strength_agent_node,
)
from athlea_langgraph.states.state import Agent<PERSON><PERSON>
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

from ..fixtures.user_profiles import (
    ALL_PROFILES,
    BEGINNER_MUSCLE_GAIN,
    INTERMEDIATE_BODYBUILDING,
    ADVANCED_POWERLIFTER,
    OLDER_ADULT,
    STRENGTH_FOCUSED_PROFILES,
)
from ..synthetic.synthetic_coaching_sessions import (
    ALL_SESSIONS,
    SIMPLE_STRENGTH_QUERY,
    PLATEAU_BREAKTHROUGH,
    INJURY_CONSULTATION,
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestStrengthAgent:
    """Test cases for the StrengthAgent class."""

    def setup_method(self):
        """Setup for each test method."""
        self.agent = StrengthAgent()

    def test_agent_initialization(self):
        """Test that the strength agent initializes correctly."""
        assert self.agent.name == "strength_agent"
        assert self.agent.domain == "strength_training"
        assert isinstance(self.agent.system_prompt, str)
        assert len(self.agent.system_prompt) > 0
        assert self.agent.permissions == [
            "strength_assessment",
            "program_design",
            "exercise_selection",
        ]
        assert self.agent.max_iterations == 10
        assert self.agent.temperature == 0.7

    def test_get_domain_prompt(self):
        """Test that domain prompt is returned correctly."""
        prompt = self.agent.get_domain_prompt()
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert any(
            word in prompt.lower()
            for word in ["strength", "coach", "exercise", "training"]
        )

    def test_agent_properties(self):
        """Test agent properties and methods."""
        assert self.agent.get_domain() == "strength_training"
        assert set(self.agent.get_permissions()) == {
            "strength_assessment",
            "program_design",
            "exercise_selection",
        }
        assert str(self.agent) == "strength_agent (strength_training domain, ReAct)"

    @pytest.mark.asyncio
    async def test_get_domain_tools(self):
        """Test that domain tools method exists and handles errors gracefully."""
        # Test that the method exists
        assert hasattr(self.agent, "get_domain_tools")
        assert callable(self.agent.get_domain_tools)

        tools = await self.agent.get_domain_tools()
        assert isinstance(tools, list)
        assert len(tools) > 0


class TestStrengthAgentNode:
    """Test cases for the strength_agent_node function."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_node_basic_functionality(self, mock_process: AsyncMock):
        """Test basic node functionality with mocked tools."""
        # Create test state
        state = {
            "messages": [{"role": "user", "content": "I want to build muscle"}],
            "user_query": "I want to build muscle",
            "user_profile": BEGINNER_MUSCLE_GAIN,
        }

        mock_process.return_value = {
            "response": "Here's a beginner muscle building program...",
            "specialist_completed": True,
            "metadata": {"tools_used": ["azure_cognitive_search"]},
        }

        result = await strength_agent_node(state, {})

        # Verify result structure
        assert "messages" in result
        assert "specialist_completed" in result
        assert "current_agent" in result
        assert result["current_agent"] == "strength_agent"
        assert result["specialist_completed"] is True
        mock_process.assert_called_once()


class TestStrengthAgentIntegration:
    """Integration tests for strength agent with different scenarios."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_beginner_muscle_building_scenario(self, mock_process: AsyncMock):
        """Test handling of beginner muscle building request."""
        session = SIMPLE_STRENGTH_QUERY

        state = {
            "messages": [HumanMessage(content=session["initial_query"])],
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        mock_process.return_value = {
            "response": "For building muscle as a beginner, I recommend starting with compound exercises like squats, deadlifts, bench press, and rows. Begin with 3 sets of 8-12 reps, 3 times per week.",
            "specialist_completed": True,
            "metadata": {
                "reasoning_steps": [
                    "Assessed user level",
                    "Recommended exercises",
                ],
                "tools_used": ["azure_cognitive_search"],
            },
        }

        result = await strength_agent_node(state, {})

        assert result["specialist_completed"] is True
        assert "compound exercises" in result["messages"][-1]["content"]
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_intermediate_plateau_scenario(self, mock_process: AsyncMock):
        """Test handling of intermediate lifter plateau."""
        session = PLATEAU_BREAKTHROUGH

        state = {
            "messages": [HumanMessage(content=session["initial_query"])],
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        mock_process.return_value = {
            "response": "For breaking through plateaus, consider periodization, deload weeks, or technique refinement. Your current lifts suggest you may benefit from intermediate programming.",
            "specialist_completed": True,
            "metadata": {
                "reasoning_steps": [
                    "Analyzed current lifts",
                    "Identified plateau causes",
                ],
                "tools_used": ["azure_cognitive_search"],
            },
        }

        result = await strength_agent_node(state, {})

        assert result["specialist_completed"] is True
        assert (
            "plateau" in result["messages"][-1]["content"]
            or "periodization" in result["messages"][-1]["content"]
        )
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_injury_accommodation_scenario(self, mock_process: AsyncMock):
        """Test handling of post-injury training."""
        session = INJURY_CONSULTATION

        state = {
            "messages": [HumanMessage(content=session["initial_query"])],
            "user_query": session["initial_query"],
            "user_profile": session["user_profile"],
        }

        mock_process.return_value = {
            "response": "Post-ACL recovery requires careful progression. Start with bodyweight movements, avoid jumping initially, and focus on knee stability exercises.",
            "specialist_completed": True,
            "metadata": {
                "reasoning_steps": [
                    "Assessed injury history",
                    "Recommended safe progressions",
                ],
                "tools_used": ["azure_cognitive_search"],
            },
        }

        result = await strength_agent_node(state, {})

        assert result["specialist_completed"] is True
        response_content = result["messages"][-1]["content"]
        assert any(
            word in response_content.lower()
            for word in ["acl", "injury", "careful", "progression"]
        )
        mock_process.assert_called_once()


class TestStrengthAgentWithUserProfiles:
    """Test strength agent with different user profile scenarios."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_all_strength_focused_profiles(self, mock_process: AsyncMock):
        """Test agent with all strength-focused user profiles."""
        for profile in STRENGTH_FOCUSED_PROFILES:
            state = {
                "messages": [HumanMessage(content="I need strength training advice")],
                "user_query": "I need strength training advice",
                "user_profile": profile,
            }

            mock_process.return_value = {
                "response": f"Strength advice for {profile['fitness_level']} level athlete",
                "specialist_completed": True,
                "metadata": {},
            }

            result = await strength_agent_node(state, {})

            assert result["specialist_completed"] is True
            assert profile["fitness_level"] in result["messages"][-1]["content"]
            mock_process.assert_called_with(state, {})
            mock_process.reset_mock()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_beginner_vs_advanced_responses(self, mock_process: AsyncMock):
        """Test that responses differ appropriately for beginner vs advanced users."""
        beginner_state = {
            "messages": [HumanMessage(content="How do I start lifting?")],
            "user_query": "How do I start lifting?",
            "user_profile": BEGINNER_MUSCLE_GAIN,
        }

        advanced_state = {
            "messages": [HumanMessage(content="How do I start lifting?")],
            "user_query": "How do I start lifting?",
            "user_profile": ADVANCED_POWERLIFTER,
        }

        def mock_process_side_effect(state, config=None):
            fitness_level = state.get("user_profile", {}).get("fitness_level", "")
            if fitness_level == "beginner":
                return {
                    "response": "Start with basic compound movements and focus on form",
                    "specialist_completed": True,
                    "metadata": {},
                }
            else:
                return {
                    "response": "Consider advanced periodization and specialized techniques",
                    "specialist_completed": True,
                    "metadata": {},
                }

        mock_process.side_effect = mock_process_side_effect

        beginner_result = await strength_agent_node(beginner_state, {})
        advanced_result = await strength_agent_node(advanced_state, {})

        beginner_response = beginner_result["messages"][-1]["content"]
        advanced_response = advanced_result["messages"][-1]["content"]

        assert "basic" in beginner_response or "form" in beginner_response
        assert "advanced" in advanced_response or "periodization" in advanced_response
        assert beginner_response != advanced_response


class TestStrengthAgentEdgeCases:
    """Test edge cases and error scenarios for strength agent."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_empty_user_profile(self, mock_process: AsyncMock):
        """Test handling of empty user profile."""
        state = {
            "messages": [HumanMessage(content="I want to get stronger")],
            "user_query": "I want to get stronger",
            "user_profile": {},
        }

        mock_process.return_value = {
            "response": "I'd be happy to help you get stronger! To provide the best guidance, could you tell me about your current fitness level and any experience with weight training?",
            "specialist_completed": True,
            "metadata": {},
        }

        result = await strength_agent_node(state, {})

        assert result["specialist_completed"] is True
        assert "fitness level" in result["messages"][-1]["content"]
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_malformed_user_query(self, mock_process: AsyncMock):
        """Test handling of vague or malformed queries."""
        state = {
            "messages": [HumanMessage(content="gym")],
            "user_query": "gym",
            "user_profile": BEGINNER_MUSCLE_GAIN,
        }

        mock_process.return_value = {
            "response": "Could you be more specific about what you'd like to know about gym training? Are you looking for exercise recommendations, programming advice, or something else?",
            "specialist_completed": False,
            "metadata": {},
        }

        result = await strength_agent_node(state, {})

        assert result["specialist_completed"] is False
        assert "more specific" in result["messages"][-1]["content"]
        mock_process.assert_called_once()

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_older_adult_safety_considerations(self, mock_process: AsyncMock):
        """Test that safety considerations are emphasized for older adults."""
        state = {
            "messages": [
                HumanMessage(content="I'm 65 and want to start strength training")
            ],
            "user_query": "I'm 65 and want to start strength training",
            "user_profile": OLDER_ADULT,
        }

        mock_process.return_value = {
            "response": "Starting strength training at 65 is excellent for health! I recommend beginning with light weights or resistance bands, focusing on proper form, and getting medical clearance before starting.",
            "specialist_completed": True,
            "metadata": {"safety_considerations": True},
        }

        result = await strength_agent_node(state, {})

        response_content = result["messages"][-1]["content"]
        assert any(
            word in response_content.lower()
            for word in ["medical", "clearance", "light", "proper form"]
        )
        mock_process.assert_called_once()


class TestStrengthAgentPerformance:
    """Performance and load tests for strength agent."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_multiple_concurrent_requests(self, mock_process: AsyncMock):
        """Test handling multiple concurrent strength training requests."""
        states = []
        for i in range(5):
            states.append(
                {
                    "messages": [
                        HumanMessage(content=f"Query {i}: Strength training advice")
                    ],
                    "user_query": f"Query {i}: Strength training advice",
                    "user_profile": BEGINNER_MUSCLE_GAIN,
                }
            )

        mock_process.return_value = {
            "response": "Here's your personalized strength training advice",
            "specialist_completed": True,
            "metadata": {},
        }

        # Run concurrent requests
        tasks = [strength_agent_node(state, {}) for state in states]
        results = await asyncio.gather(*tasks)

        # Verify all requests completed successfully
        assert len(results) == 5
        for result in results:
            assert result["specialist_completed"] is True

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_response_time_reasonable(self, mock_process: AsyncMock):
        """Test that response times are reasonable for strength queries."""
        import time

        state = {
            "messages": [HumanMessage(content="I need a strength training program")],
            "user_query": "I need a strength training program",
            "user_profile": INTERMEDIATE_BODYBUILDING,
        }

        mock_process.return_value = {
            "response": "Here's a comprehensive strength training program...",
            "specialist_completed": True,
            "metadata": {},
        }

        start_time = time.time()
        result = await strength_agent_node(state, {})
        end_time = time.time()

        response_time = end_time - start_time
        assert response_time < 5.0  # Should complete within 5 seconds
        assert result["specialist_completed"] is True


class TestStrengthAgentScenarios:
    """Test scenarios based on synthetic coaching sessions."""

    @pytest.mark.asyncio
    @patch("athlea_langgraph.agents.strength_agent.StrengthAgent.process")
    async def test_generated_scenarios(self, mock_process: AsyncMock):
        """Test agent with generated strength training scenarios."""
        strength_sessions = [
            session
            for session in ALL_SESSIONS
            if "strength" in session.get("expected_agents", [])
        ]

        for session in strength_sessions[:3]:  # Test first 3
            state = {
                "messages": [HumanMessage(content=session["initial_query"])],
                "user_query": session["initial_query"],
                "user_profile": session["user_profile"],
            }

            mock_process.return_value = {
                "response": f"Strength guidance for: {session['initial_query'][:50]}...",
                "specialist_completed": True,
                "metadata": {"scenario": session["scenario_id"]},
            }

            result = await strength_agent_node(state, {})

            assert result["specialist_completed"] is True
            mock_process.assert_called_with(state, {})
            mock_process.reset_mock()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
