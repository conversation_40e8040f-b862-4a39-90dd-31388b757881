import pytest
import logging
from unittest.mock import AsyncMock, patch
from langchain_core.messages import HumanMessage, AIMessage

from athlea_langgraph.agents.onboarding.generate_plan_node import GeneratePlanNode
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    SidebarStateData,
    UserGoals,
    SummaryItem,
    create_initial_onboarding_state,
)

# Configure logging for debugging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class TestGeneratePlanNode:
    """Test the GeneratePlanNode directly to debug SidebarStateData conversion issues"""

    @pytest.fixture
    def generate_plan_node(self):
        """Create a GeneratePlanNode instance for testing"""
        return GeneratePlanNode()

    @pytest.fixture
    def mock_llm_response(self):
        """Mock LLM response for plan generation"""
        return {
            "planId": "test-plan-123",
            "name": "Test Fitness Plan",
            "description": "A test fitness plan for debugging",
            "duration": "12 weeks",
            "level": "Beginner",
            "planType": "Strength",
            "disciplines": ["Strength Training"],
            "rationale": "This plan is designed for testing purposes",
            "phases": [
                {
                    "phaseName": "Foundation Phase",
                    "duration": "4 weeks",
                    "weeks": "1-4",
                    "focus": "Building base strength",
                    "description": "Focus on learning proper form",
                    "rationale": "Essential foundation for progression",
                }
            ],
            "exampleSessions": [
                {
                    "SessionName": "Basic Strength Session",
                    "sessionType": "Strength",
                    "Duration": "45 minutes",
                    "SessionDescription": "Basic compound movements",
                }
            ],
        }

    @pytest.fixture
    def test_state_with_sidebar_dict(self):
        """Create test state where sidebar_data is a dictionary (problematic case)"""
        return OnboardingState(
            user_id="test-user-123",
            messages=[
                HumanMessage(
                    content="I have access to all equipment possible. And I want to focus on getting leaner"
                )
            ],
            onboarding_stage="gatherInfo",
            sidebar_data={  # This is the problematic case - it's a dict, not SidebarStateData
                "current_stage": "gatherInfo",
                "goals": {"exists": True, "list": ["getting leaner", "build muscle"]},
                "summary_items": [
                    {
                        "category": "Equipment Access",
                        "details": "Has access to all equipment possible",
                        "isImportant": True,
                    }
                ],
                "selected_sports": ["Strength Training"],
                "selected_sport": "Strength Training",
                "uploaded_documents": [],
                "key_insights": {},
            },
            has_enough_info=True,
            needs_input=False,
            requires_input=False,
            info_gathered=True,
            system_prompt="Test system prompt",
        )

    @pytest.fixture
    def test_state_with_sidebar_object(self):
        """Create test state where sidebar_data is proper SidebarStateData object"""
        sidebar = SidebarStateData(
            current_stage="gatherInfo",
            goals=UserGoals(exists=True, list=["getting leaner", "build muscle"]),
            summary_items=[
                SummaryItem(
                    category="Equipment Access",
                    details="Has access to all equipment possible",
                    isImportant=True,
                )
            ],
            selected_sports=["Strength Training"],
            selected_sport="Strength Training",
            uploaded_documents=[],
            key_insights={},
        )

        return OnboardingState(
            user_id="test-user-123",
            messages=[
                HumanMessage(
                    content="I have access to all equipment possible. And I want to focus on getting leaner"
                )
            ],
            onboarding_stage="gatherInfo",
            sidebar_data=sidebar,
            has_enough_info=True,
            needs_input=False,
            requires_input=False,
            info_gathered=True,
            system_prompt="Test system prompt",
        )

    @pytest.mark.asyncio
    async def test_generate_plan_with_dict_sidebar_data(
        self, generate_plan_node, test_state_with_sidebar_dict, mock_llm_response
    ):
        """Test generate_plan_node when sidebar_data is a dictionary (the problematic case)"""
        logger.info("=== TESTING GENERATE_PLAN_NODE WITH DICT SIDEBAR_DATA ===")

        # Log the input state
        logger.info(
            f"Input state sidebar_data type: {type(test_state_with_sidebar_dict.sidebar_data)}"
        )
        logger.info(
            f"Input state sidebar_data content: {test_state_with_sidebar_dict.sidebar_data}"
        )

        # Mock the LLM call
        with patch.object(generate_plan_node, "llm") as mock_llm:
            mock_structured_llm = AsyncMock()
            mock_structured_llm.ainvoke.return_value = mock_llm_response
            mock_llm.with_structured_output.return_value = mock_structured_llm

            try:
                result = await generate_plan_node(test_state_with_sidebar_dict)
                logger.info("✅ Successfully processed dict sidebar_data")
                logger.info(f"Result keys: {result.keys()}")
                logger.info(
                    f"Generated plan name: {result.get('generated_plan', {}).get('name', 'N/A')}"
                )

                # Verify the result
                assert "generated_plan" in result
                assert "sidebar_data" in result
                assert result["onboarding_stage"] == "complete"

            except Exception as e:
                logger.error(f"❌ Error processing dict sidebar_data: {e}")
                logger.error(f"Error type: {type(e)}")
                logger.error(f"Error args: {e.args}")
                raise

    @pytest.mark.asyncio
    async def test_generate_plan_with_object_sidebar_data(
        self, generate_plan_node, test_state_with_sidebar_object, mock_llm_response
    ):
        """Test generate_plan_node when sidebar_data is a proper SidebarStateData object"""
        logger.info("=== TESTING GENERATE_PLAN_NODE WITH OBJECT SIDEBAR_DATA ===")

        # Log the input state
        logger.info(
            f"Input state sidebar_data type: {type(test_state_with_sidebar_object.sidebar_data)}"
        )
        logger.info(
            f"Input state sidebar_data: {test_state_with_sidebar_object.sidebar_data}"
        )

        # Mock the LLM call
        with patch.object(generate_plan_node, "llm") as mock_llm:
            mock_structured_llm = AsyncMock()
            mock_structured_llm.ainvoke.return_value = mock_llm_response
            mock_llm.with_structured_output.return_value = mock_structured_llm

            try:
                result = await generate_plan_node(test_state_with_sidebar_object)
                logger.info("✅ Successfully processed object sidebar_data")
                logger.info(f"Result keys: {result.keys()}")
                logger.info(
                    f"Generated plan name: {result.get('generated_plan', {}).get('name', 'N/A')}"
                )

                # Verify the result
                assert "generated_plan" in result
                assert "sidebar_data" in result
                assert result["onboarding_stage"] == "complete"

            except Exception as e:
                logger.error(f"❌ Error processing object sidebar_data: {e}")
                logger.error(f"Error type: {type(e)}")
                logger.error(f"Error args: {e.args}")
                raise

    @pytest.mark.asyncio
    async def test_debug_sidebar_data_conversion(
        self, generate_plan_node, test_state_with_sidebar_dict
    ):
        """Debug the specific conversion logic that's causing issues"""
        logger.info("=== DEBUGGING SIDEBAR_DATA CONVERSION ===")

        state = test_state_with_sidebar_dict

        # Test the exact conversion logic from the node
        sidebar_raw = state.get("sidebar_data", {})
        logger.info(f"sidebar_raw type: {type(sidebar_raw)}")
        logger.info(f"sidebar_raw content: {sidebar_raw}")

        try:
            if isinstance(sidebar_raw, SidebarStateData):
                current_sidebar_data = sidebar_raw
                logger.info("✅ Used existing SidebarStateData object")
            else:
                current_sidebar_data = SidebarStateData(**sidebar_raw)
                logger.info("✅ Successfully converted dict to SidebarStateData")

            logger.info(f"current_sidebar_data type: {type(current_sidebar_data)}")
            logger.info(f"current_sidebar_data.goals: {current_sidebar_data.goals}")
            logger.info(
                f"current_sidebar_data.summary_items: {current_sidebar_data.summary_items}"
            )

        except Exception as e:
            logger.error(f"❌ Error in conversion: {e}")
            logger.error(f"Error type: {type(e)}")
            logger.error(
                f"sidebar_raw keys: {sidebar_raw.keys() if isinstance(sidebar_raw, dict) else 'not a dict'}"
            )
            raise

    @pytest.mark.asyncio
    async def test_real_world_scenario(self, generate_plan_node):
        """Test with a real-world scenario that mimics the actual error"""
        logger.info("=== TESTING REAL-WORLD SCENARIO ===")

        # Create a state that mimics what's happening in production
        real_state = {
            "user_id": "test-user-123",
            "messages": [
                HumanMessage(
                    content="I have access to all equipment possible. And I want to focus on getting leaner"
                )
            ],
            "onboarding_stage": "gatherInfo",
            "sidebar_data": {  # This dict structure comes from MongoDB or state transitions
                "current_stage": "gatherInfo",
                "goals": {"exists": True, "list": ["getting leaner", "build muscle"]},
                "summary_items": [
                    {
                        "category": "Equipment Access",
                        "details": "Has access to all equipment possible",
                        "isImportant": True,
                    }
                ],
                "selected_sports": ["Strength Training"],
                "selected_sport": "Strength Training",
                "uploaded_documents": [],
                "key_insights": {},
            },
            "has_enough_info": True,
            "info_gathered": True,
            "system_prompt": "Test system prompt",
        }

        logger.info(f"Real state sidebar_data type: {type(real_state['sidebar_data'])}")
        logger.info(f"Real state sidebar_data: {real_state['sidebar_data']}")

        # Mock the LLM call so we can focus on the conversion issue
        with patch.object(generate_plan_node, "llm") as mock_llm:
            mock_structured_llm = AsyncMock()
            mock_structured_llm.ainvoke.return_value = {
                "planId": "test-plan-123",
                "name": "Test Plan",
                "description": "Test description",
                "duration": "12 weeks",
                "level": "Beginner",
                "planType": "Strength",
                "disciplines": ["Strength Training"],
                "rationale": "Test rationale",
                "phases": [],
                "exampleSessions": [],
            }
            mock_llm.with_structured_output.return_value = mock_structured_llm

            try:
                result = await generate_plan_node(real_state)
                logger.info("✅ Successfully processed real-world scenario")
                logger.info(f"Result type: {type(result)}")
                logger.info(f"Result keys: {result.keys()}")

            except Exception as e:
                logger.error(f"❌ Error in real-world scenario: {e}")
                logger.error(f"Error type: {type(e)}")
                import traceback

                logger.error(f"Full traceback: {traceback.format_exc()}")
                raise


if __name__ == "__main__":
    # Allow running this test file directly for debugging
    import asyncio

    async def run_debug_test():
        test_instance = TestGeneratePlanNode()
        node = test_instance.generate_plan_node()

        # Create the problematic state
        state = {
            "user_id": "test-user-123",
            "sidebar_data": {
                "current_stage": "gatherInfo",
                "goals": {"exists": True, "list": ["getting leaner"]},
                "summary_items": [
                    {
                        "category": "Equipment",
                        "details": "All equipment",
                        "isImportant": True,
                    }
                ],
                "selected_sports": ["Strength Training"],
            },
        }

        print("Testing sidebar_data conversion...")
        await test_instance.test_debug_sidebar_data_conversion(node, state)

    asyncio.run(run_debug_test())
