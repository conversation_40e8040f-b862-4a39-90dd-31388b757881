"""
Tests for the self-contained Mental Agent and its direct tool integration.
"""

import async<PERSON>
import json
from unittest.mock import AsyncMock, patch

import pytest
from athlea_langgraph.agents.mental_agent import MentalAgent
from athlea_langgraph.states import AgentState

# Mark all tests in this module as asyncio
pytestmark = pytest.mark.asyncio


@pytest.fixture
def mental_agent_fixture():
    """Provides a fresh instance of the MentalAgent for each test."""
    return MentalAgent()


async def test_mental_agent_initialization(mental_agent_fixture: MentalAgent):
    """Test that the MentalAgent initializes correctly without tools loaded."""
    agent = mental_agent_fixture
    assert agent.name == "mental_agent"
    assert agent.domain == "mental_training"
    assert not agent.tools  # Tools should be loaded lazily
    assert not agent._tools_loaded


async def test_get_domain_tools_loading(mental_agent_fixture: MentalAgent):
    """Test the get_domain_tools method correctly loads the self-contained tools."""
    agent = mental_agent_fixture
    tools = await agent.get_domain_tools()

    assert len(tools) == 4
    assert agent._tools_loaded
    tool_names = {tool.name for tool in tools}
    expected_tools = {
        "mental_state_assessment",
        "stress_level_tracker",
        "mood_pattern_analyzer",
        "goal_tracker",
    }

    assert tool_names == expected_tools

    # Test that calling it again returns the cached tools without reloading
    with patch.object(agent, "_tools_loaded", True):
        cached_tools = await agent.get_domain_tools()
        assert cached_tools is agent.tools  # Should be the exact same object
        assert agent._tools_loaded


@patch(
    "athlea_langgraph.tools.mental.mental_state_assessment.MentalStateAssessmentTool.assess_mental_state"
)
async def test_mental_agent_calls_assessment_tool(
    mock_assessment: AsyncMock, mental_agent_fixture: MentalAgent
):
    """Verify the agent has the assessment tool available and can process assessment requests."""
    agent = mental_agent_fixture
    mock_response = {
        "mental_readiness": 8,
        "stress_level": 3,
        "motivation_level": 9,
        "confidence_score": 7,
        "focus_ability": 8,
        "recommendations": [
            "Good mental state for high-intensity training",
            "Consider brief meditation before training",
            "Focus on maintaining current positive mindset",
        ],
    }
    mock_assessment.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I need help assessing my current mental state for training readiness.",
            }
        ],
        user_query="I need help assessing my current mental state for training readiness.",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    assert len(tools) == 4
    tool_names = [tool.name for tool in tools]
    assert "mental_state_assessment" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch(
    "athlea_langgraph.tools.mental.stress_tracker.StressLevelTracker.track_stress_level"
)
async def test_mental_agent_calls_stress_tracker_tool(
    mock_stress_tracker: AsyncMock, mental_agent_fixture: MentalAgent
):
    """Verify the agent has the stress tracker tool available."""
    agent = mental_agent_fixture
    mock_response = {
        "current_stress_level": 6,
        "stress_trend": "increasing",
        "stress_sources": ["work deadline", "family commitments"],
        "coping_strategies": [
            "Deep breathing exercises",
            "Schedule regular breaks",
            "Practice progressive muscle relaxation",
        ],
        "recommended_actions": ["Reduce training intensity by 20%"],
    }
    mock_stress_tracker.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I've been feeling really stressed lately. Can you help me track my stress levels?",
            }
        ],
        user_query="I've been feeling really stressed lately. Can you help me track my stress levels?",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "stress_level_tracker" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch(
    "athlea_langgraph.tools.mental.mood_pattern_analyzer.MoodPatternAnalyzer.analyze_mood_patterns"
)
async def test_mental_agent_calls_mood_analyzer_tool(
    mock_mood_analyzer: AsyncMock, mental_agent_fixture: MentalAgent
):
    """Verify the agent has the mood analyzer tool available."""
    agent = mental_agent_fixture
    mock_response = {
        "mood_patterns": {"morning": 7.5, "afternoon": 6.2, "evening": 5.8},
        "weekly_trend": "stable",
        "mood_triggers": ["poor sleep", "work meetings"],
        "positive_influences": ["exercise", "social time"],
        "recommendations": [
            "Maintain consistent sleep schedule",
            "Schedule workouts in the morning when mood is highest",
        ],
    }
    mock_mood_analyzer.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "Can you help me understand my mood patterns? I feel like they fluctuate throughout the day.",
            }
        ],
        user_query="Can you help me understand my mood patterns? I feel like they fluctuate throughout the day.",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "mood_pattern_analyzer" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch("athlea_langgraph.tools.mental.goal_tracker.GoalTracker.track_goal_progress")
async def test_mental_agent_calls_goal_tracker_tool(
    mock_goal_tracker: AsyncMock, mental_agent_fixture: MentalAgent
):
    """Verify the agent has the goal tracker tool available."""
    agent = mental_agent_fixture
    mock_response = {
        "current_goals": [
            {
                "goal": "Run 5K under 25 minutes",
                "progress": 75,
                "target_date": "2024-03-01",
                "status": "on_track",
            },
            {
                "goal": "Meditate daily for 2 weeks",
                "progress": 50,
                "target_date": "2024-02-15",
                "status": "needs_attention",
            },
        ],
        "motivation_score": 8,
        "confidence_level": 7,
        "recommendations": [
            "Celebrate progress on running goal",
            "Set reminder for daily meditation",
            "Break meditation goal into smaller daily targets",
        ],
    }
    mock_goal_tracker.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I want to track my fitness and mental health goals. Can you help me see how I'm doing?",
            }
        ],
        user_query="I want to track my fitness and mental health goals. Can you help me see how I'm doing?",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "goal_tracker" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


async def test_mental_agent_tool_loading_efficiency(mental_agent_fixture: MentalAgent):
    """Test that tools are loaded efficiently and cached properly."""
    agent = mental_agent_fixture

    # First call should load tools
    tools_first = await agent.get_domain_tools()
    assert len(tools_first) == 4
    assert agent._tools_loaded

    # Second call should return cached tools without reloading
    tools_second = await agent.get_domain_tools()
    assert tools_second is agent.tools  # Should be the exact same object
    assert len(tools_second) == 4


async def test_mental_agent_prompt_loading(mental_agent_fixture: MentalAgent):
    """Test that the system prompt is loaded correctly."""
    agent = mental_agent_fixture

    # Initially should use fallback prompt
    assert agent.system_prompt == agent.fallback_prompt
    assert not agent._prompt_loaded

    # Loading prompt should update the system prompt
    await agent._load_system_prompt()
    assert agent._prompt_loaded

    # The prompt should contain mental coaching content
    assert "mental" in agent.system_prompt.lower()
    assert (
        "coach" in agent.system_prompt.lower()
        or "psychology" in agent.system_prompt.lower()
    )

    # Check that prompt contains guidance about available tools
    tools = await agent.get_domain_tools()
    assert len(tools) == 4
    tool_names = [tool.name for tool in tools]
    expected_tools = [
        "mental_state_assessment",
        "stress_level_tracker",
        "mood_pattern_analyzer",
        "goal_tracker",
    ]
    for expected_tool in expected_tools:
        assert expected_tool in tool_names


async def test_mental_agent_process_method(mental_agent_fixture: MentalAgent):
    """Test that the process method correctly loads tools and prompt before processing."""
    agent = mental_agent_fixture

    state = AgentState(
        messages=[{"role": "user", "content": "Hello mental coach"}],
        user_query="Hello mental coach",
    )

    # Mock the parent process method to avoid actual LLM calls
    with patch.object(
        agent.__class__.__bases__[0], "process", new_callable=AsyncMock
    ) as mock_process:
        mock_process.return_value = {
            "response": "Hello! I'm here to help with your mental training.",
            "specialist_completed": True,
        }

        result = await agent.process(state, {})

        # Verify tools and prompt were loaded
        assert agent.tools  # Tools should be loaded
        assert agent._tools_loaded
        assert agent._prompt_loaded

        # Verify parent process was called
        mock_process.assert_called_once_with(state, {})

        # Verify result
        assert (
            result["response"] == "Hello! I'm here to help with your mental training."
        )
