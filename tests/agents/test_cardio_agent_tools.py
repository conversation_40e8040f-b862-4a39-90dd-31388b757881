"""
Tests for the self-contained Cardio Agent and its direct tool integration.
"""

import asyncio
import json
from unittest.mock import AsyncMock, patch

import pytest
from athlea_langgraph.agents.cardio_agent import CardioAgent
from athlea_langgraph.states import AgentState

# Mark all tests in this module as asyncio
pytestmark = pytest.mark.asyncio


@pytest.fixture
def cardio_agent_fixture():
    """Provides a fresh instance of the CardioAgent for each test."""
    return CardioAgent()


async def test_cardio_agent_initialization(cardio_agent_fixture: CardioAgent):
    """Test that the CardioAgent initializes correctly without tools loaded."""
    agent = cardio_agent_fixture
    assert agent.name == "cardio_agent"
    assert agent.domain == "cardio_endurance"
    assert not agent.tools  # Tools should be loaded lazily
    assert not agent._tools_loaded


async def test_get_domain_tools_loading(cardio_agent_fixture: CardioAgent):
    """Test the get_domain_tools method correctly loads the self-contained tools."""
    agent = cardio_agent_fixture
    tools = await agent.get_domain_tools()

    assert len(tools) == 3
    assert agent._tools_loaded
    tool_names = {tool.name for tool in tools}
    assert "comprehensive_cardio_assessment" in tool_names
    assert "calculate_training_zones" in tool_names
    assert "calculate_heart_rate_zones" in tool_names

    # Test that calling it again returns the cached tools without reloading
    with patch.object(agent, "_tools_loaded", True):
        cached_tools = await agent.get_domain_tools()
        assert cached_tools == tools  # Should return the same cached tools
        assert agent._tools_loaded


@patch("athlea_langgraph.tools.cardio.comprehensive_cardio_assessment")
async def test_cardio_agent_calls_assessment_tool(
    mock_assessment: AsyncMock, cardio_agent_fixture: CardioAgent
):
    """Verify the agent has the assessment tool available and can process assessment requests."""
    agent = cardio_agent_fixture
    mock_response = {
        "vo2_max_estimated": 45.2,
        "cardiovascular_fitness_level": "Good",
        "recommendations": [
            "Increase weekly running volume by 10%",
            "Include interval training twice per week",
        ],
        "training_zones": {"zone_1": "120-140 bpm", "zone_2": "140-160 bpm"},
    }
    mock_assessment.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I need a comprehensive cardiovascular fitness assessment. I'm 30 years old, run 3 times per week.",
            }
        ],
        user_query="I need a comprehensive cardiovascular fitness assessment. I'm 30 years old, run 3 times per week.",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    assert len(tools) == 3
    tool_names = [tool.name for tool in tools]
    assert "comprehensive_cardio_assessment" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch("athlea_langgraph.tools.cardio.calculate_training_zones")
async def test_cardio_agent_calls_training_zones_tool(
    mock_zones: AsyncMock, cardio_agent_fixture: CardioAgent
):
    """Verify the agent has the training zones tool available."""
    agent = cardio_agent_fixture
    mock_response = {
        "heart_rate_zones": {
            "recovery": "100-120 bpm",
            "aerobic_base": "120-140 bpm",
            "tempo": "140-160 bpm",
            "lactate_threshold": "160-175 bpm",
            "anaerobic": "175-190 bpm",
        },
        "running_pace_zones": {
            "easy": "9:00-10:00 min/mile",
            "tempo": "7:30-8:00 min/mile",
            "interval": "6:30-7:00 min/mile",
        },
        "recommendations": [
            "80% of training should be in aerobic base zone",
            "Include tempo runs once per week",
        ],
    }
    mock_zones.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "Can you calculate my training zones? I'm 28 years old, my resting heart rate is 55 bpm, and I want zones for running.",
            }
        ],
        user_query="Can you calculate my training zones? I'm 28 years old, my resting heart rate is 55 bpm, and I want zones for running.",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "calculate_training_zones" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


@patch("athlea_langgraph.tools.cardio.calculate_heart_rate_zones")
async def test_cardio_agent_calls_heart_rate_zones_tool(
    mock_hr_zones: AsyncMock, cardio_agent_fixture: CardioAgent
):
    """Verify the agent has the heart rate zones tool available."""
    agent = cardio_agent_fixture
    mock_response = {
        "heart_rate_zones": {
            "zone_1_recovery": "105-125 bpm",
            "zone_2_aerobic": "125-145 bpm",
            "zone_3_tempo": "145-165 bpm",
            "zone_4_threshold": "165-175 bpm",
            "zone_5_neuromuscular": "175-190 bpm",
        },
        "max_heart_rate": 190,
        "resting_heart_rate": 60,
        "method_used": "karvonen",
    }
    mock_hr_zones.return_value = json.dumps(mock_response)

    state = AgentState(
        messages=[
            {
                "role": "user",
                "content": "I'm 32 years old with a resting heart rate of 60. Can you give me my heart rate zones?",
            }
        ],
        user_query="I'm 32 years old with a resting heart rate of 60. Can you give me my heart rate zones?",
    )
    config = {}

    result = await agent.process(state, config)

    # Verify the agent has tools available
    tools = await agent.get_domain_tools()
    tool_names = [tool.name for tool in tools]
    assert "calculate_heart_rate_zones" in tool_names

    # Verify the result is processed correctly
    assert "response" in result
    assert len(result["response"]) > 0


async def test_cardio_agent_tool_loading_efficiency(cardio_agent_fixture: CardioAgent):
    """Test that tools are loaded efficiently and cached properly."""
    agent = cardio_agent_fixture

    # First call should load tools
    tools_first = await agent.get_domain_tools()
    assert len(tools_first) == 3
    assert agent._tools_loaded

    # Second call should return cached tools without reloading
    tools_second = await agent.get_domain_tools()
    assert tools_second is agent.tools  # Should be the exact same object
    assert len(tools_second) == 3


async def test_cardio_agent_prompt_loading(cardio_agent_fixture: CardioAgent):
    """Test that the system prompt is loaded correctly."""
    agent = cardio_agent_fixture

    # Initially should use fallback prompt
    assert agent.system_prompt == agent.fallback_prompt
    assert not agent._prompt_loaded

    # Loading prompt should update the system prompt
    await agent._load_system_prompt()
    assert agent._prompt_loaded

    # The prompt should contain cardio coaching content
    assert (
        "cardio" in agent.system_prompt.lower()
        or "cardiovascular" in agent.system_prompt.lower()
    )
    assert (
        "coach" in agent.system_prompt.lower()
        or "training" in agent.system_prompt.lower()
    )

    # Check that the agent has the expected tools available
    tools = await agent.get_domain_tools()
    assert len(tools) == 3
    tool_names = [tool.name for tool in tools]
    expected_tools = [
        "comprehensive_cardio_assessment",
        "calculate_training_zones",
        "calculate_heart_rate_zones",
    ]
    for expected_tool in expected_tools:
        assert expected_tool in tool_names


async def test_cardio_agent_process_method(cardio_agent_fixture: CardioAgent):
    """Test that the process method correctly loads tools and prompt before processing."""
    agent = cardio_agent_fixture

    state = AgentState(
        messages=[{"role": "user", "content": "Hello cardio coach"}],
        user_query="Hello cardio coach",
    )

    # Mock the parent process method to avoid actual LLM calls
    with patch.object(
        agent.__class__.__bases__[0], "process", new_callable=AsyncMock
    ) as mock_process:
        mock_process.return_value = {
            "response": "Hello! I'm your cardio coach.",
            "specialist_completed": True,
        }

        result = await agent.process(state, {})

        # Verify tools and prompt were loaded
        assert agent.tools  # Tools should be loaded
        assert agent._tools_loaded
        assert agent._prompt_loaded

        # Verify parent process was called
        mock_process.assert_called_once_with(state, {})

        # Verify result
        assert result["response"] == "Hello! I'm your cardio coach."
