"""
Synthetic Coaching Sessions for Testing

This module provides realistic coaching session scenarios for testing agent responses,
conversation flows, and multi-agent coordination.
"""

import random
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Tuple

from ..fixtures.user_profiles import (
    ADVANCED_ATHLETE,
    BEGINNER_MUSCLE_GAIN,
    BEGINNER_WEIGHT_LOSS,
    INJURY_RECOVERY,
    INTERMEDIATE_RUNNER,
    INTERMEDIATE_STRENGTH,
    OLDER_ADULT,
    VEGAN_ATHLETE,
)


class SessionType(Enum):
    """Types of coaching sessions."""

    INITIAL_CONSULTATION = "initial_consultation"
    WORKOUT_PLANNING = "workout_planning"
    NUTRITION_PLANNING = "nutrition_planning"
    PROGRESS_CHECK = "progress_check"
    PROBLEM_SOLVING = "problem_solving"
    GOAL_ADJUSTMENT = "goal_adjustment"
    MULTI_DOMAIN = "multi_domain"


class QueryComplexity(Enum):
    """Complexity levels for user queries."""

    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    MULTI_PART = "multi_part"


def create_coaching_session(
    user_profile: Dict[str, Any],
    session_type: SessionType,
    user_query: str,
    expected_agents: List[str],
    complexity: QueryComplexity = QueryComplexity.SIMPLE,
    follow_up_questions: List[str] = None,
    context: Dict[str, Any] = None,
) -> Dict[str, Any]:
    """
    Create a synthetic coaching session scenario.

    Args:
        user_profile: User profile data
        session_type: Type of coaching session
        user_query: Initial user query
        expected_agents: List of agents expected to be involved
        complexity: Complexity level of the query
        follow_up_questions: Additional questions user might ask
        context: Additional session context

    Returns:
        Dictionary containing coaching session data
    """
    if follow_up_questions is None:
        follow_up_questions = []
    if context is None:
        context = {}

    session = {
        "session_id": f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.randint(1000, 9999)}",
        "user_profile": user_profile,
        "session_type": session_type.value,
        "initial_query": user_query,
        "expected_agents": expected_agents,
        "complexity": complexity.value,
        "follow_up_questions": follow_up_questions,
        "context": context,
        "created_at": datetime.now().isoformat(),
        "expected_duration_minutes": _estimate_session_duration(
            complexity, len(expected_agents)
        ),
    }

    return session


def _estimate_session_duration(complexity: QueryComplexity, num_agents: int) -> int:
    """Estimate session duration based on complexity and agents involved."""
    base_duration = {
        QueryComplexity.SIMPLE: 5,
        QueryComplexity.MODERATE: 10,
        QueryComplexity.COMPLEX: 15,
        QueryComplexity.MULTI_PART: 20,
    }
    return base_duration[complexity] + (num_agents * 2)


# Simple single-agent sessions
SIMPLE_STRENGTH_QUERY = create_coaching_session(
    user_profile=BEGINNER_MUSCLE_GAIN,
    session_type=SessionType.WORKOUT_PLANNING,
    user_query="I want to start lifting weights to build muscle. What exercises should I do?",
    expected_agents=["strength_agent"],
    complexity=QueryComplexity.SIMPLE,
    follow_up_questions=[
        "How many sets and reps should I do?",
        "How often should I work out?",
        "What weight should I start with?",
    ],
)

SIMPLE_NUTRITION_QUERY = create_coaching_session(
    user_profile=BEGINNER_WEIGHT_LOSS,
    session_type=SessionType.NUTRITION_PLANNING,
    user_query="What should I eat to lose weight? I'm vegetarian.",
    expected_agents=["nutrition_agent"],
    complexity=QueryComplexity.SIMPLE,
    follow_up_questions=[
        "How many calories should I eat per day?",
        "What about meal timing?",
        "Can you suggest some meal ideas?",
    ],
)

SIMPLE_CARDIO_QUERY = create_coaching_session(
    user_profile=INTERMEDIATE_RUNNER,
    session_type=SessionType.WORKOUT_PLANNING,
    user_query="I want to train for my first marathon. How should I structure my running training?",
    expected_agents=["cardio_agent"],
    complexity=QueryComplexity.MODERATE,
    follow_up_questions=[
        "How many miles should I run per week?",
        "What pace should I train at?",
        "How do I prevent injuries?",
    ],
)

# Multi-agent sessions
MULTI_AGENT_BEGINNER = create_coaching_session(
    user_profile=BEGINNER_WEIGHT_LOSS,
    session_type=SessionType.MULTI_DOMAIN,
    user_query="I'm new to fitness and want to lose weight. I need help with both workout and diet plans.",
    expected_agents=["strength_agent", "nutrition_agent"],
    complexity=QueryComplexity.MODERATE,
    follow_up_questions=[
        "How do I balance cardio and strength training?",
        "Should I eat before or after workouts?",
        "What supplements do I need?",
    ],
    context={
        "goals": ["weight_loss", "general_fitness"],
        "time_commitment": "3-4 hours per week",
        "equipment_access": "home gym",
    },
)

COMPREHENSIVE_ATHLETE_PLANNING = create_coaching_session(
    user_profile=ADVANCED_ATHLETE,
    session_type=SessionType.MULTI_DOMAIN,
    user_query="I'm preparing for basketball season. I need a complete training and nutrition plan that includes strength, conditioning, and recovery.",
    expected_agents=[
        "strength_agent",
        "cardio_agent",
        "nutrition_agent",
        "recovery_agent",
    ],
    complexity=QueryComplexity.COMPLEX,
    follow_up_questions=[
        "How do I periodize my training for the season?",
        "What should I eat on game days?",
        "How much sleep do I need?",
        "What about injury prevention?",
    ],
    context={
        "sport": "basketball",
        "season_start": "in 3 months",
        "training_phase": "pre-season preparation",
        "position": "point guard",
    },
)

VEGAN_ATHLETE_CONSULTATION = create_coaching_session(
    user_profile=VEGAN_ATHLETE,
    session_type=SessionType.MULTI_DOMAIN,
    user_query="As a vegan triathlete, I'm struggling with energy during long training sessions. I need help optimizing my plant-based nutrition and training schedule.",
    expected_agents=["nutrition_agent", "cardio_agent"],
    complexity=QueryComplexity.COMPLEX,
    follow_up_questions=[
        "Am I getting enough protein?",
        "What should I eat during long rides?",
        "How do I fuel for brick workouts?",
        "Should I take any supplements?",
    ],
    context={
        "dietary_restriction": "vegan",
        "sport": "triathlon",
        "training_volume": "15+ hours per week",
        "primary_concern": "energy and endurance",
    },
)

# Problem-solving sessions
INJURY_CONSULTATION = create_coaching_session(
    user_profile=INJURY_RECOVERY,
    session_type=SessionType.PROBLEM_SOLVING,
    user_query="I had ACL surgery 6 months ago and I'm cleared to exercise. How do I safely get back into strength training?",
    expected_agents=["strength_agent", "recovery_agent"],
    complexity=QueryComplexity.MODERATE,
    follow_up_questions=[
        "What exercises should I avoid?",
        "How do I know if I'm pushing too hard?",
        "Should I still do physical therapy exercises?",
        "When can I return to sports?",
    ],
    context={
        "injury": "ACL reconstruction",
        "surgery_date": "6 months ago",
        "physical_therapy": "completed",
        "doctor_clearance": True,
        "current_restrictions": ["no jumping", "limited pivoting"],
    },
)

PLATEAU_BREAKTHROUGH = create_coaching_session(
    user_profile=INTERMEDIATE_STRENGTH,
    session_type=SessionType.PROBLEM_SOLVING,
    user_query="I've been lifting for 18 months but my lifts have plateaued for the last 3 months. How do I break through?",
    expected_agents=["strength_agent"],
    complexity=QueryComplexity.MODERATE,
    follow_up_questions=[
        "Should I change my program?",
        "Am I eating enough?",
        "Do I need to deload?",
        "What about my sleep and recovery?",
    ],
    context={
        "current_program": "upper/lower split",
        "plateau_duration": "3 months",
        "current_lifts": {
            "squat": "185 lbs",
            "bench": "110 lbs",
            "deadlift": "225 lbs",
        },
        "training_frequency": "4x per week",
    },
)

# Age-specific sessions
OLDER_ADULT_CONSULTATION = create_coaching_session(
    user_profile=OLDER_ADULT,
    session_type=SessionType.INITIAL_CONSULTATION,
    user_query="I'm 62 and want to start exercising for the first time in years. I have osteoporosis and want to improve my strength and balance safely.",
    expected_agents=["strength_agent", "recovery_agent"],
    complexity=QueryComplexity.MODERATE,
    follow_up_questions=[
        "Is strength training safe with osteoporosis?",
        "What about balance exercises?",
        "How often should I exercise?",
        "What warning signs should I watch for?",
    ],
    context={
        "age": 62,
        "medical_conditions": ["osteoporosis", "controlled hypertension"],
        "exercise_history": "sedentary for 10+ years",
        "primary_concerns": ["bone health", "fall prevention", "general fitness"],
    },
)

# Complex multi-part queries
LIFE_CHANGE_CONSULTATION = create_coaching_session(
    user_profile=BEGINNER_WEIGHT_LOSS,
    session_type=SessionType.MULTI_DOMAIN,
    user_query="I just had a baby 6 months ago and want to get back in shape. I'm breastfeeding, have very limited time, and no gym access. I need a complete plan for exercise and nutrition that fits my new lifestyle.",
    expected_agents=["strength_agent", "nutrition_agent", "mental_agent"],
    complexity=QueryComplexity.COMPLEX,
    follow_up_questions=[
        "How many calories do I need while breastfeeding?",
        "What exercises can I do at home with a baby?",
        "How do I stay motivated when I'm exhausted?",
        "Is it safe to diet while breastfeeding?",
    ],
    context={
        "postpartum": "6 months",
        "breastfeeding": True,
        "time_constraints": "30 minutes maximum",
        "equipment": "none",
        "sleep": "disrupted",
        "support_system": "limited",
    },
)

COMPETITION_PREP = create_coaching_session(
    user_profile=INTERMEDIATE_STRENGTH,
    session_type=SessionType.MULTI_DOMAIN,
    user_query="I want to compete in my first powerlifting meet in 6 months. I need help with training periodization, meet preparation, nutrition for strength, and mental preparation for competition.",
    expected_agents=["strength_agent", "nutrition_agent", "mental_agent"],
    complexity=QueryComplexity.COMPLEX,
    follow_up_questions=[
        "How do I peak for competition?",
        "What should I eat to maximize strength?",
        "How do I handle competition nerves?",
        "What about weight class considerations?",
    ],
    context={
        "competition_date": "6 months away",
        "competition_type": "powerlifting",
        "experience_level": "first competition",
        "current_total": "520 lbs",
        "weight_class_target": "148 lbs",
    },
)

# Edge cases and challenging scenarios
CONFLICTING_GOALS = create_coaching_session(
    user_profile=INTERMEDIATE_RUNNER,
    session_type=SessionType.GOAL_ADJUSTMENT,
    user_query="I want to run a sub-3-hour marathon but also want to gain 15 pounds of muscle. Are these goals compatible?",
    expected_agents=["cardio_agent", "strength_agent", "nutrition_agent"],
    complexity=QueryComplexity.COMPLEX,
    follow_up_questions=[
        "Should I focus on one goal at a time?",
        "How would my training need to change?",
        "What about my nutrition strategy?",
        "Can I maintain my current running volume?",
    ],
    context={
        "conflicting_goals": True,
        "current_marathon_pr": "3:15",
        "current_weight": "155 lbs",
        "target_weight": "170 lbs",
        "timeline": "1 year",
    },
)

MINIMAL_INFORMATION = create_coaching_session(
    user_profile={"name": "Anonymous", "age": None, "fitness_level": "unknown"},
    session_type=SessionType.INITIAL_CONSULTATION,
    user_query="I want to get fit.",
    expected_agents=["head_coach"],  # Should route to clarification
    complexity=QueryComplexity.SIMPLE,
    follow_up_questions=[],
    context={"information_level": "minimal", "needs_clarification": True},
)

# Session collections
ALL_SESSIONS = [
    SIMPLE_STRENGTH_QUERY,
    SIMPLE_NUTRITION_QUERY,
    SIMPLE_CARDIO_QUERY,
    MULTI_AGENT_BEGINNER,
    COMPREHENSIVE_ATHLETE_PLANNING,
    VEGAN_ATHLETE_CONSULTATION,
    INJURY_CONSULTATION,
    PLATEAU_BREAKTHROUGH,
    OLDER_ADULT_CONSULTATION,
    LIFE_CHANGE_CONSULTATION,
    COMPETITION_PREP,
    CONFLICTING_GOALS,
    MINIMAL_INFORMATION,
]

SINGLE_AGENT_SESSIONS = [
    SIMPLE_STRENGTH_QUERY,
    SIMPLE_NUTRITION_QUERY,
    SIMPLE_CARDIO_QUERY,
    PLATEAU_BREAKTHROUGH,
]

MULTI_AGENT_SESSIONS = [
    MULTI_AGENT_BEGINNER,
    COMPREHENSIVE_ATHLETE_PLANNING,
    VEGAN_ATHLETE_CONSULTATION,
    INJURY_CONSULTATION,
    OLDER_ADULT_CONSULTATION,
    LIFE_CHANGE_CONSULTATION,
    COMPETITION_PREP,
    CONFLICTING_GOALS,
]

COMPLEX_SESSIONS = [
    COMPREHENSIVE_ATHLETE_PLANNING,
    VEGAN_ATHLETE_CONSULTATION,
    LIFE_CHANGE_CONSULTATION,
    COMPETITION_PREP,
    CONFLICTING_GOALS,
]

BEGINNER_SESSIONS = [
    SIMPLE_STRENGTH_QUERY,
    SIMPLE_NUTRITION_QUERY,
    MULTI_AGENT_BEGINNER,
    OLDER_ADULT_CONSULTATION,
    LIFE_CHANGE_CONSULTATION,
]

PROBLEM_SOLVING_SESSIONS = [
    INJURY_CONSULTATION,
    PLATEAU_BREAKTHROUGH,
    CONFLICTING_GOALS,
]

EDGE_CASE_SESSIONS = [MINIMAL_INFORMATION, CONFLICTING_GOALS, LIFE_CHANGE_CONSULTATION]


def get_sessions_by_type(session_type: SessionType) -> List[Dict[str, Any]]:
    """Get all sessions of a specific type."""
    return [s for s in ALL_SESSIONS if s["session_type"] == session_type.value]


def get_sessions_by_complexity(complexity: QueryComplexity) -> List[Dict[str, Any]]:
    """Get all sessions of a specific complexity level."""
    return [s for s in ALL_SESSIONS if s["complexity"] == complexity.value]


def get_sessions_for_agent(agent_name: str) -> List[Dict[str, Any]]:
    """Get all sessions that should involve a specific agent."""
    return [s for s in ALL_SESSIONS if agent_name in s["expected_agents"]]


def create_conversation_history(
    session: Dict[str, Any], num_exchanges: int = 3
) -> List[Dict[str, str]]:
    """
    Create a realistic conversation history for a session.

    Args:
        session: Session data
        num_exchanges: Number of user-assistant exchanges to simulate

    Returns:
        List of message dictionaries
    """
    messages = []

    # Initial user message
    messages.append({"role": "user", "content": session["initial_query"]})

    # Simulate assistant response
    messages.append(
        {
            "role": "assistant",
            "content": f"I'd be happy to help you with {session['session_type'].replace('_', ' ')}. Based on your profile, I can see you're looking to achieve your fitness goals.",
        }
    )

    # Add follow-up exchanges
    follow_ups = session.get("follow_up_questions", [])
    for i in range(min(num_exchanges - 1, len(follow_ups))):
        messages.append({"role": "user", "content": follow_ups[i]})
        messages.append(
            {
                "role": "assistant",
                "content": f"Great question about {follow_ups[i][:20]}... Let me provide some guidance on that.",
            }
        )

    return messages


def generate_test_scenarios(agent_name: str) -> List[Dict[str, Any]]:
    """
    Generate focused test scenarios for a specific agent.

    Args:
        agent_name: Name of the agent to test

    Returns:
        List of test scenarios relevant to the agent
    """
    relevant_sessions = get_sessions_for_agent(agent_name)

    scenarios = []
    for session in relevant_sessions:
        scenario = {
            "test_name": f"{agent_name}_{session['session_type']}_{session['complexity']}",
            "agent": agent_name,
            "user_profile": session["user_profile"],
            "user_query": session["initial_query"],
            "expected_completion": True,
            "context": session.get("context", {}),
            "follow_up_questions": session.get("follow_up_questions", []),
            "complexity": session["complexity"],
        }
        scenarios.append(scenario)

    return scenarios
