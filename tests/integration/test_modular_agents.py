"""
Integration Tests for Modular Agent Architecture

This test suite validates the modular agent architecture implementation following
multi-agent best practices:

1. Planning node correctly routes to specialized agents
2. Each modular agent executes ReAct pattern properly
3. Integration with existing graph structure
4. Schema validation and error handling
5. Tool integration (when re-enabled)

Tests cover all agents created in GitHub Issue #8:
- StrengthAgent
- NutritionAgent
- CardioAgent
- RecoveryAgent
- MentalAgent

Each test verifies: Planning Node → Specific Agent → ReAct Execution → Response
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List

import pytest

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ModularAgentTester:
    """Comprehensive tester for modular agent architecture."""

    def __init__(self):
        """Initialize the tester."""
        pass

    async def test_individual_modular_agents(self) -> None:
        """Test each individual modular agent's ReAct functionality."""
        print("🤖 Testing Individual Modular Agents (ReAct Pattern)")
        print("=" * 70)

        # Import modular agents
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.cardio_agent import CardioAgent, cardio_agent_node
        from athlea_langgraph.agents.mental_agent import MentalAgent, mental_agent_node
        from athlea_langgraph.agents.nutrition_agent import (
            NutritionAgent,
            nutrition_agent_node,
        )
        from athlea_langgraph.agents.recovery_agent import (
            RecoveryAgent,
            recovery_agent_node,
        )
        from athlea_langgraph.agents.strength_agent import (
            StrengthAgent,
            strength_agent_node,
        )
        from athlea_langgraph.states import AgentState

        # Test cases for each modular agent
        test_cases = [
            {
                "agent_class": StrengthAgent,
                "node_function": strength_agent_node,
                "name": "Strength Agent",
                "domain": "strength_training",
                "query": "I want to build muscle and increase my deadlift. Can you help me create a strength program?",
                "expected_concepts": ["strength", "muscle", "deadlift", "program"],
                "user_profile": {
                    "fitness_level": "intermediate",
                    "goals": ["muscle building", "strength gains"],
                    "equipment": ["barbell", "dumbbells"],
                    "experience": "6 months lifting",
                },
            },
            {
                "agent_class": NutritionAgent,
                "node_function": nutrition_agent_node,
                "name": "Nutrition Agent",
                "domain": "nutrition",
                "query": "I'm trying to build muscle but also lose fat. What should my nutrition look like?",
                "expected_concepts": ["nutrition", "muscle", "fat loss", "macros"],
                "user_profile": {
                    "dietary_restrictions": ["lactose intolerant"],
                    "activity_level": "high",
                    "body_composition_goals": ["body recomposition"],
                },
            },
            {
                "agent_class": CardioAgent,
                "node_function": cardio_agent_node,
                "name": "Cardio Agent",
                "domain": "cardiovascular_training",
                "query": "I want to improve my 5K time and build better endurance. What cardio plan do you recommend?",
                "expected_concepts": ["cardio", "5K", "endurance", "running"],
                "user_profile": {
                    "current_5k_time": "28 minutes",
                    "running_experience": "beginner",
                    "weekly_availability": "4 days",
                },
            },
            {
                "agent_class": RecoveryAgent,
                "node_function": recovery_agent_node,
                "name": "Recovery Agent",
                "domain": "recovery_regeneration",
                "query": "I'm feeling overtrained and my sleep quality is poor. How can I improve my recovery?",
                "expected_concepts": ["recovery", "sleep", "overtraining", "rest"],
                "user_profile": {
                    "training_volume": "high",
                    "sleep_hours": "5-6",
                    "stress_level": "high",
                    "recovery_methods": ["none currently"],
                },
            },
            {
                "agent_class": MentalAgent,
                "node_function": mental_agent_node,
                "name": "Mental Agent",
                "domain": "mental_training",
                "query": "I struggle with consistency and motivation. How can I build better exercise habits?",
                "expected_concepts": ["motivation", "consistency", "habits", "mental"],
                "user_profile": {
                    "biggest_challenge": "consistency",
                    "previous_attempts": "multiple failed gym memberships",
                    "motivation_level": "low",
                },
            },
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. Testing {test_case['name']}")
            print("-" * 50)

            # Test 1: Agent instance creation
            print(f"📋 Test 1a: Agent Instance Creation")
            try:
                agent_instance = test_case["agent_class"]()
                print(f"✅ {test_case['name']} instance created successfully")
                print(f"   Domain: {agent_instance.get_domain()}")
                print(f"   Tools: {len(agent_instance.get_tools())} available")
                print(f"   Permissions: {agent_instance.get_permissions()}")
            except Exception as e:
                print(f"❌ Agent instance creation failed: {e}")
                continue

            # Test 2: Node function execution with ReAct
            print(f"📋 Test 1b: Node Function Execution (ReAct Pattern)")

            # Create comprehensive test state
            test_state = {
                "messages": [HumanMessage(content=test_case["query"])],
                "user_query": test_case["query"],
                "user_profile": test_case["user_profile"],
                # Add all required state fields
                "routing_decision": None,
                "pending_agents": None,
                "plan": None,
                "current_step": None,
                "domain_contributions": {},
                "required_domains": [],
                "completed_domains": [],
                "aggregated_plan": None,
                "proceed_to_generation": False,
                "current_plan": None,
                "is_onboarding": False,
            }

            try:
                print(f"Query: {test_case['query']}")
                print(f"User Profile: {test_case['user_profile']}")

                # Execute the node function
                result = await test_case["node_function"](test_state)

                # Validate response structure
                if result:
                    print(f"✅ {test_case['name']} executed successfully")

                    # Check response content
                    response_content = result.get("response", "")
                    if response_content:
                        print(f"📝 Response preview: {response_content[:150]}...")

                        # Validate domain expertise
                        response_lower = response_content.lower()
                        concepts_found = sum(
                            1
                            for concept in test_case["expected_concepts"]
                            if concept.lower() in response_lower
                        )

                        if concepts_found >= 2:
                            print(
                                f"✅ Response shows domain expertise ({concepts_found}/{len(test_case['expected_concepts'])} concepts found)"
                            )
                        else:
                            print(
                                f"⚠️ Limited domain expertise detected ({concepts_found}/{len(test_case['expected_concepts'])} concepts)"
                            )

                    # Check agent metadata
                    agent_name = result.get("agent_name")
                    agent_domain = result.get("agent_domain")
                    specialist_completed = result.get("specialist_completed", False)

                    if agent_name and agent_domain:
                        print(f"🏷️ Agent Metadata: {agent_name} ({agent_domain})")

                    if specialist_completed:
                        print(f"✅ Specialist marked as completed")

                    # Check for errors
                    error = result.get("error")
                    if error:
                        print(f"⚠️ Agent reported error: {error}")

                    # Check ReAct execution metadata
                    metadata = result.get("metadata", {})
                    if metadata:
                        iterations = metadata.get("iterations_used", 0)
                        tool_calls = metadata.get("tool_calls", [])
                        if iterations > 0:
                            print(f"🔄 ReAct iterations: {iterations}")
                        if tool_calls:
                            print(f"🔧 Tool calls made: {len(tool_calls)}")

                else:
                    print(f"❌ {test_case['name']} returned no result")

            except Exception as e:
                print(f"❌ Error executing {test_case['name']}: {e}")
                logger.error(f"Error in {test_case['name']}: {e}")

            print()

    async def test_planning_node_routing(self) -> None:
        """Test that planning node correctly routes to specialized agents."""
        print("🎯 Testing Planning Node Routing to Modular Agents")
        print("=" * 70)

        # Import planning node and comprehensive graph
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.planning_node import planning_node
        from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
            create_comprehensive_coaching_graph,
        )
        from athlea_langgraph.states import AgentState

        # Routing test cases - queries that should route to specific agents
        routing_test_cases = [
            {
                "query": "I want to start strength training and build muscle",
                "expected_routes": ["strength_agent"],
                "description": "Strength training query → Strength Agent",
            },
            {
                "query": "What should I eat to support my training goals?",
                "expected_routes": ["nutrition_agent"],
                "description": "Nutrition query → Nutrition Agent",
            },
            {
                "query": "I want to improve my running endurance and cardiovascular fitness",
                "expected_routes": ["cardio_agent"],
                "description": "Cardio query → Cardio Agent",
            },
            {
                "query": "I'm feeling burned out and need help with recovery strategies",
                "expected_routes": ["recovery_agent"],
                "description": "Recovery query → Recovery Agent",
            },
            {
                "query": "I struggle with motivation and need help building exercise habits",
                "expected_routes": ["mental_agent"],
                "description": "Mental training query → Mental Agent",
            },
            {
                "query": "I want a complete fitness transformation including strength, nutrition, and mindset",
                "expected_routes": [
                    "strength_agent",
                    "nutrition_agent",
                    "mental_agent",
                ],
                "description": "Multi-domain query → Multiple Agents",
            },
        ]

        for i, test_case in enumerate(routing_test_cases, 1):
            print(f"\n{i}. {test_case['description']}")
            print("-" * 50)

            # Create test state for planning node
            test_state = {
                "messages": [HumanMessage(content=test_case["query"])],
                "user_query": test_case["query"],
                "user_profile": {
                    "name": "Test User",
                    "fitness_level": "intermediate",
                    "goals": ["general fitness"],
                },
                "routing_decision": None,
                "pending_agents": None,
                "plan": None,
                "current_step": None,
                "domain_contributions": {},
                "required_domains": [],
                "completed_domains": [],
                "aggregated_plan": None,
                "proceed_to_generation": False,
                "current_plan": None,
                "is_onboarding": False,
            }

            try:
                print(f"Query: {test_case['query']}")

                # Execute planning node
                planning_result = await planning_node(test_state)

                if planning_result:
                    # Check routing decision
                    routing_decision = planning_result.get("routing_decision")
                    required_domains = planning_result.get("required_domains", [])

                    print(f"🎯 Planning Decision: {routing_decision}")
                    if required_domains:
                        print(f"📋 Required Domains: {required_domains}")

                    # Validate routing
                    if required_domains:
                        correct_routes = set(test_case["expected_routes"])
                        actual_routes = set(required_domains)

                        if correct_routes.intersection(actual_routes):
                            print(f"✅ Correct routing detected")
                            print(f"   Expected: {correct_routes}")
                            print(f"   Actual: {actual_routes}")
                        else:
                            print(f"⚠️ Unexpected routing")
                            print(f"   Expected: {correct_routes}")
                            print(f"   Actual: {actual_routes}")
                    else:
                        print(f"⚠️ No domains specified in routing decision")

                else:
                    print(f"❌ Planning node returned no result")

            except Exception as e:
                print(f"❌ Error in planning node: {e}")
                logger.error(f"Planning node error: {e}")

            print()

    async def test_end_to_end_agent_integration(self) -> None:
        """Test end-to-end integration: Planning → Agent → Response."""
        print("🔄 Testing End-to-End Agent Integration")
        print("=" * 70)

        # Import comprehensive graph
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
            create_comprehensive_coaching_graph,
        )

        # Create the comprehensive graph
        try:
            print("📋 Creating comprehensive coaching graph...")
            graph = await create_comprehensive_coaching_graph()
            print("✅ Graph created successfully")
        except Exception as e:
            print(f"❌ Failed to create graph: {e}")
            return

        # End-to-end test cases
        e2e_test_cases = [
            {
                "query": "I'm a beginner who wants to start strength training safely",
                "expected_agent": "strength",
                "description": "Beginner strength training request",
            },
            {
                "query": "I need a meal plan for muscle building and weight loss",
                "expected_agent": "nutrition",
                "description": "Nutrition planning request",
            },
            {
                "query": "How can I improve my running endurance for a 10K race?",
                "expected_agent": "cardio",
                "description": "Endurance training request",
            },
        ]

        for i, test_case in enumerate(e2e_test_cases, 1):
            print(f"\n{i}. {test_case['description']}")
            print("-" * 50)

            # Create initial state
            initial_state = {
                "messages": [HumanMessage(content=test_case["query"])],
                "user_query": test_case["query"],
                "user_profile": {
                    "name": "Integration Test User",
                    "fitness_level": "beginner",
                    "goals": ["general fitness"],
                },
                "routing_decision": None,
                "pending_agents": None,
                "plan": None,
                "current_step": None,
                "domain_contributions": {},
                "required_domains": [],
                "completed_domains": [],
                "aggregated_plan": None,
                "proceed_to_generation": False,
                "current_plan": None,
                "is_onboarding": False,
            }

            try:
                print(f"Query: {test_case['query']}")

                # Execute graph with limited steps for testing
                config = {"configurable": {"thread_id": f"test_thread_{i}"}}

                step_count = 0
                planning_executed = False
                agent_executed = False
                final_response = None

                async for step in graph.astream(initial_state, config):
                    step_count += 1

                    for node_name, state_update in step.items():
                        print(f"📍 Step {step_count}: {node_name}")

                        # Track planning execution
                        if "planning" in node_name.lower():
                            planning_executed = True
                            routing = state_update.get("routing_decision")
                            if routing:
                                print(f"   🎯 Routing: {routing}")

                        # Track agent execution
                        if any(
                            agent in node_name.lower()
                            for agent in [
                                "strength",
                                "nutrition",
                                "cardio",
                                "recovery",
                                "mental",
                            ]
                        ):
                            agent_executed = True
                            response = state_update.get("response")
                            if response:
                                print(f"   🤖 Agent Response: {response[:100]}...")
                                final_response = response

                        # Check for aggregated response
                        if state_update.get("aggregated_response"):
                            final_response = state_update.get("aggregated_response")
                            print(f"   📋 Final: {final_response[:100]}...")

                    # Limit steps for testing
                    if step_count >= 10 or final_response:
                        break

                # Validate execution flow
                print(f"\n📊 Execution Summary:")
                print(f"   Steps executed: {step_count}")
                print(f"   Planning executed: {'✅' if planning_executed else '❌'}")
                print(f"   Agent executed: {'✅' if agent_executed else '❌'}")
                print(f"   Final response: {'✅' if final_response else '❌'}")

                if planning_executed and agent_executed and final_response:
                    print(f"✅ End-to-end integration successful")
                else:
                    print(f"⚠️ Partial execution - may need more steps")

            except Exception as e:
                print(f"❌ Error in end-to-end test: {e}")
                logger.error(f"E2E test error: {e}")

            print()

    async def test_agent_schema_validation(self) -> None:
        """Test schema validation for modular agents."""
        print("📋 Testing Agent Schema Validation")
        print("=" * 70)

        from athlea_langgraph.agents.base_agent import (
            AgentInputSchema,
            AgentOutputSchema,
        )
        from athlea_langgraph.agents.strength_agent import StrengthAgent

        print("🔍 Testing Input Schema Validation")

        # Test valid input
        try:
            valid_input = AgentInputSchema(
                user_query="I want to build muscle",
                user_profile={"fitness_level": "beginner"},
                conversation_history=[],
            )
            print("✅ Valid input schema accepted")
        except Exception as e:
            print(f"❌ Valid input rejected: {e}")

        # Test invalid input
        try:
            invalid_input = AgentInputSchema(
                user_query="",  # Empty query should fail
                user_profile={"fitness_level": "beginner"},
                conversation_history=[],
            )
            print("⚠️ Invalid input was accepted (should have been rejected)")
        except Exception as e:
            print("✅ Invalid input correctly rejected")

        print("\n🔍 Testing Output Schema Validation")

        # Test valid output
        try:
            valid_output = AgentOutputSchema(
                response="Here's your strength training plan...",
                specialist_completed=True,
                metadata={"iterations": 3},
            )
            print("✅ Valid output schema accepted")
        except Exception as e:
            print(f"❌ Valid output rejected: {e}")

        print("✅ Schema validation tests completed")


async def run_modular_agent_tests():
    """Main function to run all modular agent tests."""

    try:
        tester = ModularAgentTester()

        print("🚀 Starting Modular Agent Architecture Testing Suite")
        print(
            "Testing GitHub Issue #8 Implementation: Break Up Monolithic Specialized Coaches"
        )
        print("=" * 90)

        # Run all tests
        await tester.test_individual_modular_agents()
        await tester.test_planning_node_routing()
        await tester.test_end_to_end_agent_integration()
        await tester.test_agent_schema_validation()

        print("\n🎉 All Modular Agent Tests Completed!")
        print("\n📊 Test Coverage:")
        print("✅ Individual agent ReAct execution")
        print("✅ Planning node routing to correct agents")
        print("✅ End-to-end integration flow")
        print("✅ Schema validation and error handling")
        print("\n🔧 Next Steps:")
        print("1. Re-enable tools for each domain when import issues are resolved")
        print("2. Add performance benchmarking for agent response times")
        print("3. Test with real user scenarios and edge cases")

    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        logger.error(f"Modular agent test suite failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(run_modular_agent_tests())
