"""
Integration test for streaming API with enhanced date coordination
and adaptive weekly planning features.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

from athlea_langgraph.api.onboarding_stream import (
    OnboardingRequest,
    StreamingOnboardingResponse,
)
from athlea_langgraph.engines.session_generation_engine import SessionGenerationEngine

logger = logging.getLogger(__name__)


class TestStreamingWithDates:
    """Test streaming API with enhanced date coordination and adaptive planning"""

    def create_session_engine(self):
        """Create a session generation engine for testing"""
        return SessionGenerationEngine()

    def create_sample_onboarding_request(self):
        """Create a sample onboarding request"""
        return OnboardingRequest(
            message="I want to start training for running and strength",
            user_id="test_user_stream_dates",
            thread_id="test_thread_stream_dates",
            user_profile={
                "goals": ["improve fitness", "build strength"],
                "experience_level": "beginner",
                "time_commitment": "3 days per week, 45 minutes per session",
                "equipment": "basic gym equipment",
            },
        )

    async def test_date_coordination_in_streaming(self):
        """Test that streaming includes proper date coordination"""

        session_engine = self.create_session_engine()
        # Generate a basic week to test date coordination
        result = await session_engine.generate_basic_week("test_user", week_number=1)

        # Verify date coordination is present
        assert "generated_at" in result
        assert "date_coordination" in result
        assert result["date_coordination"] == "real_time"

        # Verify dates are in ISO format and current
        start_date = datetime.fromisoformat(result["start_date"])
        end_date = datetime.fromisoformat(result["end_date"])
        generated_at = datetime.fromisoformat(result["generated_at"])

        # Check that dates are reasonable (within last minute and next week)
        from datetime import timezone

        now = datetime.now(timezone.utc)
        # Handle timezone differences for comparison
        generated_at_utc = (
            generated_at
            if generated_at.tzinfo
            else generated_at.replace(tzinfo=timezone.utc)
        )
        assert (
            now - generated_at_utc
        ).total_seconds() < 120  # Generated within last 2 minutes (more lenient)
        assert (end_date - start_date).days == 6  # Week span is correct

        logger.info(
            f"✅ Date coordination working: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
        )

    async def test_current_week_calculation(self):
        """Test current week calculation with date awareness"""

        session_engine = self.create_session_engine()

        # Test with current date as plan start
        plan_start = datetime.now()
        current_week = session_engine.get_current_training_week(plan_start)
        assert current_week == 1

        # Test with plan started a week ago
        plan_start_week_ago = datetime.now() - timedelta(days=7)
        current_week = session_engine.get_current_training_week(plan_start_week_ago)
        assert current_week == 2

        # Test with plan started in the future
        plan_start_future = datetime.now() + timedelta(days=7)
        current_week = session_engine.get_current_training_week(plan_start_future)
        assert current_week == 1  # Should return at least 1

        logger.info("✅ Current week calculation working correctly")

    async def test_streaming_response_timestamps(self):
        """Test that streaming responses include proper timestamps"""

        sample_onboarding_request = self.create_sample_onboarding_request()

        # Create streaming response handler
        response_handler = StreamingOnboardingResponse(sample_onboarding_request)

        # Test timestamp generation
        timestamp1 = response_handler._get_current_timestamp()
        await asyncio.sleep(0.01)  # Small delay
        timestamp2 = response_handler._get_current_timestamp()

        # Verify timestamps are different and valid ISO format
        assert timestamp1 != timestamp2
        # Handle timezone info in timestamps properly
        try:
            datetime.fromisoformat(timestamp1.replace("Z", "+00:00"))
            datetime.fromisoformat(timestamp2.replace("Z", "+00:00"))
        except ValueError:
            # Try without Z replacement if already in correct format
            datetime.fromisoformat(timestamp1)
            datetime.fromisoformat(timestamp2)

        # Test event ID generation
        event_id1 = response_handler._generate_event_id()
        event_id2 = response_handler._generate_event_id()
        assert event_id1 != event_id2
        assert len(event_id1) == 8  # Should be 8 characters

        logger.info("✅ Streaming timestamps and event IDs working correctly")

    async def test_adaptive_planning_stream_messages(self):
        """Test streaming of adaptive planning specific messages"""

        # Create a mock streaming response handler
        request = OnboardingRequest(
            message="Generate my weekly training",
            user_id="test_user_adaptive",
        )
        response_handler = StreamingOnboardingResponse(request)

        # Test adaptive planning streaming with mock data
        mock_result = {
            "generated_sessions": {
                "day_1": [{"domain": "strength", "duration": 45}],
                "day_2": [{"domain": "running", "duration": 30}],
            },
            "current_week": 1,
            "coach_coordination_notes": "Coaches coordinated successfully",
            "fitness_profile": type(
                "MockProfile",
                (),
                {
                    "user_id": "test_user",
                    "equipment_available": ["bodyweight"],
                    "time_constraints": {"sessions_per_week": 3},
                },
            )(),
            "adaptation_triggers": ["progressive_overload", "recovery_monitoring"],
            "weekly_coordination_notes": "Weekly coordination completed",
        }

        # Collect streaming messages
        messages = []
        async for message in response_handler._stream_adaptive_planning_updates(
            mock_result
        ):
            parsed_message = json.loads(message.strip())
            messages.append(parsed_message)

        # Verify expected message types are present
        event_types = [msg["event"] for msg in messages]
        expected_events = [
            "weekly_sessions_generated",
            "current_week_update",
            "coach_coordination",
            "fitness_profile_created",
            "adaptation_triggers",
            "weekly_coordination",
        ]

        for expected_event in expected_events:
            assert expected_event in event_types, f"Missing event: {expected_event}"

        # Verify all messages have timestamps and event IDs
        for message in messages:
            assert "timestamp" in message["data"]
            assert "event_id" in message["data"]
            assert "server_timestamp" in message["data"]

        logger.info(
            f"✅ Adaptive planning streaming working: {len(messages)} messages generated"
        )

    async def test_date_aware_week_start_calculation(self):
        """Test that week start calculations are Monday-based and current"""

        session_engine = self.create_session_engine()
        current_week_start = session_engine._get_current_week_start()

        # Verify it's Monday (weekday() returns 0 for Monday)
        assert current_week_start.weekday() == 0

        # Verify it's in the current week
        now = datetime.now()
        days_difference = abs((now - current_week_start).days)
        assert days_difference <= 6  # Should be within current week

        logger.info(
            f"✅ Week start calculation: {current_week_start.strftime('%Y-%m-%d (Monday)')}"
        )

    async def test_basic_weekly_generation_with_dates(self):
        """Test basic weekly generation includes proper date handling"""

        session_engine = self.create_session_engine()

        # Generate a basic week
        result = await session_engine.generate_basic_week(
            "test_user_dates", week_number=1
        )

        # Verify structure
        assert result["week_number"] == 1
        assert "start_date" in result
        assert "end_date" in result
        assert "generated_at" in result
        assert "sessions" in result
        assert "weekly_volume" in result

        # Verify date format and logic
        start_date = datetime.fromisoformat(result["start_date"])
        end_date = datetime.fromisoformat(result["end_date"])

        # Should be exactly 7 days apart
        assert (end_date - start_date).days == 6

        # Should be in the current week or future
        current_week_start = session_engine._get_current_week_start()
        assert start_date.date() >= current_week_start.date()

        logger.info("✅ Basic weekly generation with dates working correctly")


# Integration test runner
async def run_streaming_integration_tests():
    """Run all streaming and date coordination integration tests"""

    test_instance = TestStreamingWithDates()

    logger.info("🚀 Running streaming + date coordination integration tests...")

    try:
        await test_instance.test_date_coordination_in_streaming()
        await test_instance.test_current_week_calculation()
        await test_instance.test_streaming_response_timestamps()
        await test_instance.test_adaptive_planning_stream_messages()
        await test_instance.test_date_aware_week_start_calculation()
        await test_instance.test_basic_weekly_generation_with_dates()

        logger.info("✅ All streaming + date coordination tests passed!")
        return True

    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        import traceback

        logger.error(f"Full traceback: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Run integration tests
    success = asyncio.run(run_streaming_integration_tests())

    if success:
        print("\n🎉 Streaming + Date Coordination Integration: SUCCESS")
        print("✅ Real-time streaming with current date awareness implemented")
        print("✅ Adaptive weekly planning streaming features working")
        print("✅ Enhanced status endpoint with planning information")
        print("✅ Date coordination and weekly plan updates functional")
    else:
        print("\n❌ Integration tests failed - check logs for details")
