#!/usr/bin/env python3
"""
ReAct Specialized Coaches Test

This script tests the new ReAct-based specialized coaches implementation
with proper tool integration and reasoning capabilities.
"""

import asyncio
import logging
from typing import Any, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test scenarios for ReAct coaches
REACT_TEST_SCENARIOS = [
    {
        "coach": "strength_coach",
        "query": "What is RPE and how should I use it in my strength training?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for RPE information first, then provide comprehensive explanation",
        "test_type": "informational",
    },
    {
        "coach": "strength_coach",
        "query": "Find me existing deadlift workouts in your database",
        "expected_tools": ["airtable_mcp"],
        "description": "Should search Airtable for existing deadlift workouts",
        "test_type": "lookup",
    },
    {
        "coach": "strength_coach",
        "query": "Create a new hypertrophy workout for chest and triceps",
        "expected_tools": ["session_generation"],
        "description": "Should generate a new workout session",
        "test_type": "generation",
    },
    {
        "coach": "nutrition_coach",
        "query": "Explain the role of electrolytes in athletic performance",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for electrolyte research first",
        "test_type": "informational",
    },
    {
        "coach": "nutrition_coach",
        "query": "Create a high-protein meal plan for muscle building",
        "expected_tools": ["session_generation"],
        "description": "Should generate a nutrition plan",
        "test_type": "generation",
    },
    {
        "coach": "cardio_coach",
        "query": "What are the benefits of HIIT training compared to steady-state cardio?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for HIIT research and comparison studies",
        "test_type": "informational",
    },
    {
        "coach": "cardio_coach",
        "query": "Plan a running route from Central Park to Brooklyn Bridge with elevation data",
        "expected_tools": ["google_maps_elevation", "azure_maps"],
        "description": "Should use mapping tools for route planning",
        "test_type": "route_planning",
    },
    {
        "coach": "cycling_coach",
        "query": "Explain power-based training zones for cycling",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for cycling power zone information",
        "test_type": "informational",
    },
    {
        "coach": "recovery_coach",
        "query": "What does research say about sleep and athletic recovery?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for sleep and recovery research",
        "test_type": "informational",
    },
    {
        "coach": "mental_coach",
        "query": "How can I build mental resilience for competition?",
        "expected_tools": ["azure_cognitive_search"],
        "description": "Should search for sports psychology research on mental resilience",
        "test_type": "informational",
    },
]


async def test_react_coach(
    coach_name: str, query: str, scenario: Dict[str, Any]
) -> Dict[str, Any]:
    """Test a ReAct coach with a specific query."""
    print(f"\n🎯 Testing ReAct {coach_name}")
    print(f"Query: {query}")
    print(f"Expected: {scenario['description']}")
    print(f"Test Type: {scenario['test_type']}")
    print("-" * 80)

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.specialized_coaches_react import (
            get_specialized_coach,
        )
        from athlea_langgraph.state import AgentState

        # Get the ReAct coach
        coach = await get_specialized_coach(coach_name)
        if not coach:
            return {
                "coach": coach_name,
                "query": query,
                "success": False,
                "error": f"Coach {coach_name} not available",
                "tool_calls": [],
                "reasoning_steps": [],
            }

        # Create test state
        test_state = AgentState(
            messages=[HumanMessage(content=query)],
            user_query=query,
            user_profile={
                "name": "Test User",
                "fitness_level": "intermediate",
                "goals": ["general fitness", "strength", "endurance"],
                "restrictions": {"injuries": [], "dietary": [], "time_constraints": []},
            },
        )

        print(f"🤖 Invoking {coach_name} with ReAct pattern...")

        # Execute the ReAct coach
        result = await coach.invoke(test_state)

        # Analyze the result
        analysis = {
            "coach": coach_name,
            "query": query,
            "success": True,
            "tool_calls": [],
            "reasoning_steps": [],
            "final_response": "",
            "expected_tools": scenario["expected_tools"],
            "tools_used": [],
            "tools_matched": False,
            "react_pattern_followed": False,
        }

        if result.get("messages"):
            # Analyze the conversation flow for ReAct pattern
            messages = result["messages"]
            tool_call_count = 0
            reasoning_count = 0

            for i, message in enumerate(messages):
                if hasattr(message, "tool_calls") and message.tool_calls:
                    tool_call_count += 1
                    analysis["tool_calls"].extend(message.tool_calls)
                    analysis["tools_used"].extend(
                        [tc["name"] for tc in message.tool_calls]
                    )

                    print(
                        f"  🔧 Step {i+1}: Tool calls made ({len(message.tool_calls)} tools)"
                    )
                    for tc in message.tool_calls:
                        print(f"    - {tc['name']}: {str(tc['args'])[:100]}...")

                elif hasattr(message, "content") and message.content:
                    if (
                        "think" in message.content.lower()
                        or "reason" in message.content.lower()
                    ):
                        reasoning_count += 1
                        analysis["reasoning_steps"].append(
                            message.content[:200] + "..."
                        )

                    # Check if this is the final response
                    if i == len(messages) - 1:
                        analysis["final_response"] = message.content[:300] + "..."
                        print(f"  💭 Final Response: {message.content[:150]}...")

            # Check if ReAct pattern was followed (reasoning + action)
            analysis["react_pattern_followed"] = (
                tool_call_count > 0 and len(messages) > 1
            )

            # Check if expected tools were used
            expected_tools = set(scenario["expected_tools"])
            used_tools = set(analysis["tools_used"])
            analysis["tools_matched"] = bool(expected_tools.intersection(used_tools))

            print(f"  📊 Analysis:")
            print(f"    - Tool calls made: {tool_call_count}")
            print(f"    - Reasoning steps: {reasoning_count}")
            print(f"    - ReAct pattern followed: {analysis['react_pattern_followed']}")
            print(f"    - Expected tools used: {analysis['tools_matched']}")
            print(f"    - Tools used: {analysis['tools_used']}")

        return analysis

    except Exception as e:
        print(f"❌ Error testing {coach_name}: {e}")
        return {
            "coach": coach_name,
            "query": query,
            "success": False,
            "error": str(e),
            "tool_calls": [],
            "reasoning_steps": [],
            "react_pattern_followed": False,
        }


async def test_react_reasoning_flow():
    """Test the ReAct reasoning flow with a complex multi-step query."""
    print("\n🧠 Testing ReAct Reasoning Flow")
    print("=" * 80)

    complex_query = """I'm a beginner who wants to start strength training. I have a previous knee injury 
    and I'm vegetarian. Can you help me understand what RPE is, find some beginner-friendly exercises 
    that are safe for my knee, and create a starter workout plan?"""

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.specialized_coaches_react import (
            get_specialized_coach,
        )
        from athlea_langgraph.state import AgentState

        coach = await get_specialized_coach("strength_coach")
        if not coach:
            print("❌ Strength coach not available")
            return

        test_state = AgentState(
            messages=[HumanMessage(content=complex_query)],
            user_query=complex_query,
            user_profile={
                "name": "Beginner User",
                "fitness_level": "beginner",
                "goals": ["start strength training", "learn basics"],
                "restrictions": {
                    "injuries": ["knee injury"],
                    "dietary": ["vegetarian"],
                    "time_constraints": [],
                },
            },
        )

        print(f"🎯 Complex Query: {complex_query}")
        print("\n🤖 Executing ReAct flow...")

        result = await coach.invoke(test_state)

        if result.get("messages"):
            print(f"\n📋 ReAct Flow Analysis:")
            messages = result["messages"]

            for i, message in enumerate(messages):
                print(f"\nStep {i+1}:")
                if hasattr(message, "tool_calls") and message.tool_calls:
                    print(f"  🔧 ACTION: Used {len(message.tool_calls)} tools")
                    for tc in message.tool_calls:
                        print(f"    - {tc['name']}")
                elif hasattr(message, "content"):
                    if len(message.content) > 200:
                        print(f"  💭 REASONING/RESPONSE: {message.content[:200]}...")
                    else:
                        print(f"  💭 REASONING/RESPONSE: {message.content}")

            print(f"\n✅ Complex ReAct flow completed with {len(messages)} steps")

    except Exception as e:
        print(f"❌ Error in ReAct reasoning flow test: {e}")


async def test_all_react_scenarios():
    """Test all ReAct scenarios and provide comprehensive analysis."""
    print("🚀 ReAct Specialized Coaches Testing")
    print("=" * 80)
    print("Testing ReAct pattern implementation with proper reasoning and tool usage")
    print("=" * 80)

    results = []

    # Test individual scenarios
    for i, scenario in enumerate(REACT_TEST_SCENARIOS, 1):
        print(f"\n📋 Scenario {i}/{len(REACT_TEST_SCENARIOS)}")

        result = await test_react_coach(scenario["coach"], scenario["query"], scenario)

        results.append(result)

        # Small delay between tests
        await asyncio.sleep(2)

    # Test complex reasoning flow
    await test_react_reasoning_flow()

    # Generate comprehensive summary
    print("\n" + "=" * 80)
    print("📊 REACT IMPLEMENTATION ANALYSIS")
    print("=" * 80)

    successful_tests = [r for r in results if r.get("success", False)]
    react_pattern_tests = [r for r in results if r.get("react_pattern_followed", False)]
    tool_using_tests = [r for r in results if r.get("tool_calls")]
    matched_tool_tests = [r for r in results if r.get("tools_matched", False)]

    print(f"Total scenarios tested: {len(results)}")
    print(f"Successful executions: {len(successful_tests)}/{len(results)}")
    print(f"ReAct pattern followed: {len(react_pattern_tests)}/{len(results)}")
    print(f"Tests that made tool calls: {len(tool_using_tests)}/{len(results)}")
    print(f"Tests that used expected tools: {len(matched_tool_tests)}/{len(results)}")

    # ReAct pattern analysis
    print(f"\n🧠 ReAct Pattern Analysis:")
    pattern_success_rate = (
        len(react_pattern_tests) / len(results) * 100 if results else 0
    )
    print(f"  ReAct pattern success rate: {pattern_success_rate:.1f}%")

    # Tool usage breakdown
    print(f"\n🔧 Tool Usage Breakdown:")
    tool_usage = {}
    for result in results:
        for tool in result.get("tools_used", []):
            tool_usage[tool] = tool_usage.get(tool, 0) + 1

    for tool, count in sorted(tool_usage.items()):
        print(f"  {tool}: {count} times")

    # Coach performance with ReAct
    print(f"\n👥 Coach ReAct Performance:")
    coach_stats = {}
    for result in results:
        coach = result.get("coach", "unknown")
        if coach not in coach_stats:
            coach_stats[coach] = {
                "total": 0,
                "react_pattern": 0,
                "tool_calls": 0,
                "matched": 0,
            }

        coach_stats[coach]["total"] += 1
        if result.get("react_pattern_followed"):
            coach_stats[coach]["react_pattern"] += 1
        if result.get("tool_calls"):
            coach_stats[coach]["tool_calls"] += 1
        if result.get("tools_matched"):
            coach_stats[coach]["matched"] += 1

    for coach, stats in sorted(coach_stats.items()):
        react_rate = (
            stats["react_pattern"] / stats["total"] * 100 if stats["total"] else 0
        )
        tool_rate = stats["tool_calls"] / stats["total"] * 100 if stats["total"] else 0
        match_rate = stats["matched"] / stats["total"] * 100 if stats["total"] else 0
        print(f"  {coach}:")
        print(
            f"    - ReAct pattern: {react_rate:.1f}% ({stats['react_pattern']}/{stats['total']})"
        )
        print(
            f"    - Tool usage: {tool_rate:.1f}% ({stats['tool_calls']}/{stats['total']})"
        )
        print(
            f"    - Expected tools: {match_rate:.1f}% ({stats['matched']}/{stats['total']})"
        )

    # Test type analysis
    print(f"\n📋 Test Type Analysis:")
    test_types = {}
    for i, scenario in enumerate(REACT_TEST_SCENARIOS):
        test_type = scenario["test_type"]
        if test_type not in test_types:
            test_types[test_type] = {"total": 0, "success": 0, "react": 0}

        test_types[test_type]["total"] += 1
        if i < len(results) and results[i].get("success"):
            test_types[test_type]["success"] += 1
        if i < len(results) and results[i].get("react_pattern_followed"):
            test_types[test_type]["react"] += 1

    for test_type, stats in sorted(test_types.items()):
        success_rate = stats["success"] / stats["total"] * 100 if stats["total"] else 0
        react_rate = stats["react"] / stats["total"] * 100 if stats["total"] else 0
        print(
            f"  {test_type}: {success_rate:.1f}% success, {react_rate:.1f}% ReAct pattern"
        )

    # Failed scenarios
    failed_results = [r for r in results if not r.get("success", False)]
    if failed_results:
        print(f"\n❌ Failed Scenarios:")
        for result in failed_results:
            print(f"  {result['coach']}: {result.get('error', 'Unknown error')}")

    print(f"\n✅ ReAct specialized coaches testing completed!")
    print(f"🎯 Key Findings:")
    print(
        f"  - ReAct pattern implementation: {'✅ Working' if pattern_success_rate > 70 else '⚠️ Needs improvement'}"
    )
    print(
        f"  - Tool integration: {'✅ Working' if len(tool_using_tests) > len(results) * 0.7 else '⚠️ Needs improvement'}"
    )
    print(
        f"  - Expected tool usage: {'✅ Working' if len(matched_tool_tests) > len(results) * 0.6 else '⚠️ Needs improvement'}"
    )

    return results


if __name__ == "__main__":
    asyncio.run(test_all_react_scenarios())
