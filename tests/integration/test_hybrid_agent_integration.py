#!/usr/bin/env python3
"""
Hybrid Agent Integration Test

Tests the integration of existing agent files with MCP tools.
This test specifically triggers tool usage to demonstrate MCP activity.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the athlea_langgraph module to path
sys.path.insert(0, str(Path(__file__).parent))

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("/tmp/hybrid_agent_test.log", mode="w"),
    ],
)

logger = logging.getLogger(__name__)


async def test_hybrid_agents_with_tools():
    """Test hybrid agents that should trigger actual tool usage."""
    logger.info("🚀 Testing hybrid agents with tool triggering queries...")

    try:
        from athlea_langgraph.graphs.hybrid_agent_graphs import (
            HybridAgentState,
            create_hybrid_cardio_agent_graph,
            create_hybrid_mental_agent_graph,
            create_hybrid_nutrition_agent_graph,
            create_hybrid_strength_agent_graph,
        )

        logger.info("✅ Successfully imported hybrid agent graphs")

        # Test cases designed to trigger tool usage
        test_cases = [
            {
                "agent": "strength",
                "graph_func": create_hybrid_strength_agent_graph,
                "query": "I need a detailed strength program for building muscle. Can you search for the best compound exercises and create a progressive program?",
                "expected_tools": [
                    "search_strength_exercises",
                    "generate_strength_program",
                ],
            },
            {
                "agent": "nutrition",
                "graph_func": create_hybrid_nutrition_agent_graph,
                "query": "I want to build muscle and need a nutrition plan. Can you calculate my macros and suggest meal plans?",
                "expected_tools": ["calculate_macros", "suggest_meal_plan"],
            },
            {
                "agent": "cardio",
                "graph_func": create_hybrid_cardio_agent_graph,
                "query": "I need a running training plan for a marathon. Can you create a periodized training schedule?",
                "expected_tools": ["create_training_plan", "calculate_zones"],
            },
            {
                "agent": "mental",
                "graph_func": create_hybrid_mental_agent_graph,
                "query": "I'm struggling with motivation for workouts. Can you help me set goals and create a mental training plan?",
                "expected_tools": ["goal_setting", "motivation_techniques"],
            },
        ]

        results = {}

        for test_case in test_cases:
            agent_name = test_case["agent"]
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 Testing {agent_name.title()} Agent with Tool Usage")
            logger.info(f"{'='*60}")

            try:
                # Create graph
                graph = await test_case["graph_func"]({"max_iterations": 3})
                logger.info(f"✅ Created {agent_name} hybrid agent graph")

                # Create state with tool-triggering query
                state = HybridAgentState(
                    messages=[], user_query=test_case["query"], input=test_case["query"]
                )

                logger.info(f"🎯 Query: {test_case['query'][:100]}...")
                logger.info(
                    f"🔧 Expected tools to be triggered: {test_case['expected_tools']}"
                )

                # Execute agent
                logger.info(f"⚡ Executing {agent_name} agent...")
                result = await graph.ainvoke(state)

                # Analyze results
                agent_response = result.get("agent_response", "No response")
                tools_available = result.get("mcp_tools_available", [])
                direct_tools = result.get("direct_tools_available", [])
                tools_used = result.get("tools_used", [])
                debug_info = result.get("debug_info", {})

                logger.info(f"📊 {agent_name.title()} Agent Results:")
                logger.info(f"   🔧 Direct tools available: {len(direct_tools)}")
                logger.info(f"   🌐 MCP tools available: {len(tools_available)}")
                logger.info(f"   ⚡ Tools actually used: {len(tools_used)}")
                logger.info(f"   📝 Response length: {len(agent_response)} chars")

                if tools_available:
                    logger.info(f"   📋 Available MCP tools: {tools_available}")

                if tools_used:
                    logger.info(f"   ✅ Tools used: {tools_used}")
                else:
                    logger.warning(
                        f"   ⚠️ No tools were used - check if agent logic triggers tools"
                    )

                if debug_info:
                    logger.info(
                        f"   🐛 Agent class: {debug_info.get('agent_class', 'Unknown')}"
                    )
                    logger.info(
                        f"   🐛 Hybrid mode: {debug_info.get('hybrid_mode', False)}"
                    )
                    logger.info(
                        f"   🐛 MCP enabled: {debug_info.get('mcp_enabled', False)}"
                    )

                # Store results
                results[agent_name] = {
                    "success": True,
                    "tools_available": len(tools_available),
                    "direct_tools": len(direct_tools),
                    "tools_used": len(tools_used),
                    "response_length": len(agent_response),
                    "hybrid_mode": debug_info.get("hybrid_mode", False),
                }

                logger.info(
                    f"✅ {agent_name.title()} agent test completed successfully"
                )

            except Exception as e:
                logger.error(f"❌ Error testing {agent_name} agent: {e}", exc_info=True)
                results[agent_name] = {
                    "success": False,
                    "error": str(e),
                    "tools_available": 0,
                    "tools_used": 0,
                }

        return results

    except Exception as e:
        logger.error(f"❌ Error in hybrid agent testing: {e}", exc_info=True)
        return {}


async def test_mcp_tool_loading():
    """Test that MCP tools are being loaded correctly."""
    logger.info("🔧 Testing MCP tool loading...")

    try:
        from athlea_langgraph.utils.mcp_agent_integration import get_agent_tools

        domains = ["strength", "nutrition", "cardio", "recovery", "mental"]

        for domain in domains:
            logger.info(f"\n📋 Testing tools for {domain} domain...")

            # Test with MCP enabled
            tools_with_mcp = await get_agent_tools(domain, use_mcp=True)

            # Test without MCP (direct only)
            tools_direct_only = await get_agent_tools(domain, use_mcp=False)

            logger.info(f"   🔧 Direct tools only: {len(tools_direct_only)}")
            logger.info(f"   🌐 With MCP tools: {len(tools_with_mcp)}")
            logger.info(
                f"   ➕ MCP tools added: {len(tools_with_mcp) - len(tools_direct_only)}"
            )

            if len(tools_with_mcp) > len(tools_direct_only):
                logger.info(f"   ✅ MCP tools successfully loaded for {domain}")
            else:
                logger.warning(f"   ⚠️ No MCP tools loaded for {domain}")

        return True

    except Exception as e:
        logger.error(f"❌ Error testing MCP tool loading: {e}", exc_info=True)
        return False


async def check_mcp_server_activity():
    """Check for MCP server activity in logs."""
    logger.info("📋 Checking MCP server activity...")

    try:
        import os

        log_files = [
            "/tmp/strength_mcp_server.log",
            "/tmp/nutrition_mcp_server.log",
            "/tmp/cardio_mcp_server.log",
            "/tmp/recovery_mcp_server.log",
            "/tmp/mental_mcp_server.log",
            "/tmp/hybrid_agent_test.log",
        ]

        for log_file in log_files:
            if os.path.exists(log_file):
                with open(log_file, "r") as f:
                    lines = f.readlines()

                if lines:
                    logger.info(f"✅ {log_file}: {len(lines)} log entries")

                    # Look for tool calls
                    tool_calls = [
                        line
                        for line in lines
                        if any(
                            keyword in line.lower()
                            for keyword in ["tool", "call", "execute", "invoke"]
                        )
                    ]
                    if tool_calls:
                        logger.info(
                            f"   🔧 Found {len(tool_calls)} potential tool calls"
                        )
                        for call in tool_calls[-3:]:  # Show last 3
                            logger.info(f"      📞 {call.strip()}")
                    else:
                        logger.info(f"   📝 No tool calls detected")

                    # Show recent activity
                    logger.info(f"   📄 Recent activity:")
                    for line in lines[-5:]:
                        logger.info(f"      {line.strip()}")
                else:
                    logger.info(f"📝 {log_file}: Empty log file")
            else:
                logger.info(f"❌ {log_file}: Log file not found")

        return True

    except Exception as e:
        logger.error(f"❌ Error checking MCP server logs: {e}")
        return False


async def main():
    """Run comprehensive hybrid agent integration tests."""
    logger.info("🚀 Starting Hybrid Agent Integration Tests...")
    logger.info("These tests use your existing agent files with MCP tools")

    tests = [
        ("MCP Tool Loading", test_mcp_tool_loading),
        ("Hybrid Agents with Tool Usage", test_hybrid_agents_with_tools),
        ("MCP Server Activity", check_mcp_server_activity),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n{'='*70}")
        logger.info(f"🧪 Running: {test_name}")
        logger.info(f"{'='*70}")

        try:
            result = await test_func()
            results[test_name] = result

            if isinstance(result, dict):
                # Handle detailed results
                success = all(r.get("success", False) for r in result.values())
                status = "✅ PASSED" if success else "❌ FAILED"
                logger.info(f"{status}: {test_name}")

                # Show detailed results
                for agent, details in result.items():
                    if details.get("success"):
                        logger.info(
                            f"  ✅ {agent}: {details.get('tools_available', 0)} tools available, {details.get('tools_used', 0)} used"
                        )
                    else:
                        logger.error(
                            f"  ❌ {agent}: {details.get('error', 'Unknown error')}"
                        )
            else:
                # Handle boolean results
                status = "✅ PASSED" if result else "❌ FAILED"
                logger.info(f"{status}: {test_name}")

        except Exception as e:
            results[test_name] = False
            logger.error(f"❌ FAILED: {test_name} - {e}")

    # Summary
    logger.info(f"\n{'='*70}")
    logger.info("📊 HYBRID AGENT INTEGRATION SUMMARY")
    logger.info(f"{'='*70}")

    logger.info("🎯 Key Points:")
    logger.info("  • This test integrates your existing agent files")
    logger.info("  • Agents get both direct tools + MCP tools")
    logger.info("  • Test queries are designed to trigger tool usage")
    logger.info("  • Check logs for MCP server activity")

    logger.info("\n📋 What you should see:")
    logger.info("  • Agent graphs loading successfully")
    logger.info("  • Tools being loaded (direct + MCP)")
    logger.info("  • MCP server connections in logs")
    logger.info("  • Tool calls logged to /tmp/*_mcp_server.log")

    logger.info(f"\n📄 Full test log saved to: /tmp/hybrid_agent_test.log")

    total_passed = sum(1 for r in results.values() if r)
    total_tests = len(results)

    if total_passed == total_tests:
        logger.info("🎉 All hybrid agent integration tests passed!")
        logger.info("🎯 Your existing agents now have MCP tool integration!")
    else:
        logger.warning(
            f"⚠️ {total_tests - total_passed} tests failed. Check logs for details."
        )


if __name__ == "__main__":
    asyncio.run(main())
