#!/usr/bin/env python3
"""
Comprehensive Test for the Comprehensive Coaching Graph

Tests the new comprehensive coaching graph with various sample inputs
to ensure all features work correctly:
- ReAct Coach Executor integration
- All specialized coaches with tools
- Tool input validation
- Human-in-the-loop capabilities
- Memory integration
"""

import asyncio
import json
import logging
from typing import Any, Dict

from langchain_core.messages import HumanMessage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the comprehensive coaching graph
from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
)


class ComprehensiveCoachingTester:
    """Test suite for the comprehensive coaching graph."""

    def __init__(self):
        self.test_results = []
        self.graph = None

    async def setup(self):
        """Initialize the coaching graph for testing."""
        print("🔧 Setting up comprehensive coaching graph...")

        # Test configuration
        config = {
            "user_id": "test_user",
            "mongodb_uri": "mongodb://localhost:27017",
            "thread_id": "test_thread",
            "enable_memory": False,  # Disable for testing
            "use_react_agents": True,
            "max_iterations": 3,  # Reduced for faster testing
            "enable_human_feedback": False,  # Disable for automated testing
        }

        try:
            self.graph = await create_comprehensive_coaching_graph(config)
            print(f"✅ Graph created successfully with {len(self.graph.nodes)} nodes")
            return True
        except Exception as e:
            print(f"❌ Failed to create graph: {e}")
            return False

    async def test_strength_coaching(self):
        """Test strength coaching with ReAct agents."""
        print("\n🏋️ Testing Strength Coaching...")

        test_cases = [
            {
                "name": "Basic strength training request",
                "input": "I want to build muscle and get stronger. I'm a beginner with access to a gym.",
                "expected_coach": "strength_coach",
            },
            {
                "name": "Specific exercise question",
                "input": "How do I perform a proper deadlift? What's the correct form?",
                "expected_coach": "strength_coach",
            },
            {
                "name": "Program design request",
                "input": "Can you create a 3-day strength training program for me? I want to focus on compound movements.",
                "expected_coach": "strength_coach",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("strength", test_case)

    async def test_cardio_coaching(self):
        """Test cardio coaching with route planning tools."""
        print("\n🏃 Testing Cardio Coaching...")

        test_cases = [
            {
                "name": "Running training plan",
                "input": "I want to train for a 5K race. I'm currently running 2 miles comfortably.",
                "expected_coach": "cardio_coach",
            },
            {
                "name": "Route planning request",
                "input": "Can you help me find a good running route in San Francisco with some hills?",
                "expected_coach": "cardio_coach",
            },
            {
                "name": "HIIT workout request",
                "input": "I need a high-intensity interval training workout for fat loss.",
                "expected_coach": "cardio_coach",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("cardio", test_case)

    async def test_cycling_coaching(self):
        """Test cycling coaching with elevation tools."""
        print("\n🚴 Testing Cycling Coaching...")

        test_cases = [
            {
                "name": "Cycling training plan",
                "input": "I want to improve my cycling endurance for long rides. Currently doing 30-mile rides.",
                "expected_coach": "cycling_coach",
            },
            {
                "name": "Route with elevation",
                "input": "Can you help me plan a challenging cycling route with good climbs near Los Angeles?",
                "expected_coach": "cycling_coach",
            },
            {
                "name": "Bike setup question",
                "input": "What's the best bike setup for long-distance touring?",
                "expected_coach": "cycling_coach",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("cycling", test_case)

    async def test_nutrition_coaching(self):
        """Test nutrition coaching with meal planning."""
        print("\n🥗 Testing Nutrition Coaching...")

        test_cases = [
            {
                "name": "Weight loss nutrition",
                "input": "I want to lose 20 pounds. Can you help me with a nutrition plan?",
                "expected_coach": "nutrition_coach",
            },
            {
                "name": "Muscle building nutrition",
                "input": "What should I eat to support muscle growth? I'm doing strength training 4x per week.",
                "expected_coach": "nutrition_coach",
            },
            {
                "name": "Meal timing question",
                "input": "When should I eat before and after workouts for best results?",
                "expected_coach": "nutrition_coach",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("nutrition", test_case)

    async def test_recovery_coaching(self):
        """Test recovery coaching with protocols."""
        print("\n😴 Testing Recovery Coaching...")

        test_cases = [
            {
                "name": "Sleep optimization",
                "input": "I'm having trouble sleeping and it's affecting my workouts. What can I do?",
                "expected_coach": "recovery_coach",
            },
            {
                "name": "Active recovery",
                "input": "What are good active recovery activities for rest days?",
                "expected_coach": "recovery_coach",
            },
            {
                "name": "Injury prevention",
                "input": "How can I prevent injuries while training? I've had some minor aches.",
                "expected_coach": "recovery_coach",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("recovery", test_case)

    async def test_mental_coaching(self):
        """Test mental coaching with psychology techniques."""
        print("\n🧠 Testing Mental Coaching...")

        test_cases = [
            {
                "name": "Motivation issues",
                "input": "I'm struggling to stay motivated with my fitness routine. Any tips?",
                "expected_coach": "mental_coach",
            },
            {
                "name": "Goal setting",
                "input": "How do I set realistic fitness goals and stick to them?",
                "expected_coach": "mental_coach",
            },
            {
                "name": "Performance anxiety",
                "input": "I get nervous before competitions. How can I manage this anxiety?",
                "expected_coach": "mental_coach",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("mental", test_case)

    async def test_multi_domain_requests(self):
        """Test requests that might involve multiple coaches."""
        print("\n🔄 Testing Multi-Domain Requests...")

        test_cases = [
            {
                "name": "Complete fitness transformation",
                "input": "I want to lose weight, build muscle, and improve my mental health. Where do I start?",
                "expected_coach": "multiple",
            },
            {
                "name": "Marathon training",
                "input": "I want to train for a marathon. I need help with training, nutrition, and mental preparation.",
                "expected_coach": "multiple",
            },
            {
                "name": "Recovery from injury",
                "input": "I'm recovering from a knee injury and want to get back to strength training safely.",
                "expected_coach": "multiple",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("multi_domain", test_case)

    async def test_edge_cases(self):
        """Test edge cases and error handling."""
        print("\n⚠️ Testing Edge Cases...")

        test_cases = [
            {"name": "Empty input", "input": "", "expected_coach": "clarification"},
            {
                "name": "Unclear request",
                "input": "Help me with stuff",
                "expected_coach": "clarification",
            },
            {
                "name": "Non-fitness related",
                "input": "What's the weather like today?",
                "expected_coach": "clarification",
            },
        ]

        for test_case in test_cases:
            await self._run_test_case("edge_cases", test_case)

    async def _run_test_case(self, category: str, test_case: Dict[str, Any]):
        """Run a single test case."""
        print(f"\n  📝 {test_case['name']}")
        print(f"     Input: {test_case['input']}")

        try:
            # Create initial state
            initial_state = {
                "messages": [HumanMessage(content=test_case["input"])],
                "user_query": test_case["input"],
                "execution_steps": [],
                "active_coaches": [],
                "coach_responses": {},
                "tool_calls_made": [],
            }

            # Run the graph
            result = await self.graph.ainvoke(initial_state)

            # Analyze results
            success = self._analyze_result(test_case, result)

            self.test_results.append(
                {
                    "category": category,
                    "name": test_case["name"],
                    "input": test_case["input"],
                    "success": success,
                    "result": result,
                }
            )

            if success:
                print(f"     ✅ Test passed")
            else:
                print(f"     ❌ Test failed")

        except Exception as e:
            print(f"     ❌ Test failed with error: {e}")
            self.test_results.append(
                {
                    "category": category,
                    "name": test_case["name"],
                    "input": test_case["input"],
                    "success": False,
                    "error": str(e),
                }
            )

    def _analyze_result(
        self, test_case: Dict[str, Any], result: Dict[str, Any]
    ) -> bool:
        """Analyze test result to determine if it passed."""
        try:
            # Check if execution completed
            if not result:
                return False

            # Check if we have execution steps
            execution_steps = result.get("execution_steps", [])
            if not execution_steps:
                return False

            # Check if appropriate coach was activated
            active_coaches = result.get("active_coaches", [])
            expected_coach = test_case.get("expected_coach")

            if expected_coach == "multiple":
                # For multi-domain, expect at least one coach
                return len(active_coaches) > 0
            elif expected_coach == "clarification":
                # For clarification, expect user_input in steps
                return (
                    "user_input" in execution_steps
                    or "clarification" in execution_steps
                )
            elif expected_coach:
                # For specific coach, check if it was activated
                return expected_coach in active_coaches

            # If no specific expectation, just check that something happened
            return len(execution_steps) > 0

        except Exception as e:
            print(f"     Error analyzing result: {e}")
            return False

    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("🧪 COMPREHENSIVE COACHING GRAPH TEST SUMMARY")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        # Group by category
        categories = {}
        for result in self.test_results:
            category = result["category"]
            if category not in categories:
                categories[category] = {"total": 0, "passed": 0}
            categories[category]["total"] += 1
            if result["success"]:
                categories[category]["passed"] += 1

        print("\n📊 Results by Category:")
        for category, stats in categories.items():
            success_rate = (stats["passed"] / stats["total"]) * 100
            print(
                f"  {category}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)"
            )

        # Show failed tests
        failed_results = [r for r in self.test_results if not r["success"]]
        if failed_results:
            print("\n❌ Failed Tests:")
            for result in failed_results:
                print(f"  - {result['category']}: {result['name']}")
                if "error" in result:
                    print(f"    Error: {result['error']}")

        print("\n" + "=" * 60)


async def main():
    """Run comprehensive tests."""
    print("🚀 Starting Comprehensive Coaching Graph Tests")
    print("=" * 60)

    tester = ComprehensiveCoachingTester()

    # Setup
    if not await tester.setup():
        print("❌ Setup failed, aborting tests")
        return

    # Run all test suites
    await tester.test_strength_coaching()
    await tester.test_cardio_coaching()
    await tester.test_cycling_coaching()
    await tester.test_nutrition_coaching()
    await tester.test_recovery_coaching()
    await tester.test_mental_coaching()
    await tester.test_multi_domain_requests()
    await tester.test_edge_cases()

    # Print summary
    tester.print_summary()


if __name__ == "__main__":
    asyncio.run(main())
