#!/usr/bin/env python3
"""
Test Questions for Optimized Coaching Graph

This script contains categorized test questions to validate the Intelligence Hub
routing decisions in the optimized coaching graph.

Expected Routing:
- Greeting Questions → automated_greeting → optimized_greeting → END
- Direct Coach Questions → direct_coach → [specific_coach] → END
- Multi-Coach Questions → multi_coach → [primary_coach] → aggregation → END
"""

import asyncio
import json
import logging
import time
from typing import Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test question categories
TEST_QUESTIONS = {
    "greeting": [
        # Simple greetings that should trigger automated_greeting
        "Hello",
        "Hi there",
        "Hey",
        "Good morning",
        "What's up?",
        "How are you?",
        "Hello, I'm new here",
        "Hi, I'm looking for a fitness coach",
        "Good afternoon, can you help me?",
        "Hey there, what can you do?",
        "I'm new to fitness, where do I start?",
        "What services do you offer?",
    ],
    "direct_coach": {
        "strength_coach": [
            # Questions specifically for strength training
            "How do I increase my bench press?",
            "What's the best way to build muscle mass?",
            "Can you create a deadlift program for me?",
            "I want to get stronger, what should I do?",
            "How many sets and reps for muscle growth?",
            "What's the proper squat form?",
            "I need a powerlifting routine",
            "How do I break through a strength plateau?",
            "What are the best compound exercises?",
            "How often should I train each muscle group?",
        ],
        "cardio_coach": [
            # Questions specifically for cardiovascular training
            "How can I improve my running endurance?",
            "What's the best cardio for fat loss?",
            "I want to train for a marathon",
            "How do I increase my VO2 max?",
            "What's the difference between HIIT and steady-state cardio?",
            "I need a cycling training plan",
            "How can I run faster?",
            "What's the best heart rate zone for training?",
            "I want to improve my 5K time",
            "How do I build aerobic capacity?",
        ],
        "nutrition_coach": [
            # Questions specifically for nutrition
            "What should I eat to lose weight?",
            "How many calories do I need daily?",
            "Can you create a meal plan for muscle gain?",
            "What are the best protein sources?",
            "I'm vegetarian, how do I get enough protein?",
            "When should I eat before and after workouts?",
            "What supplements do I need?",
            "How do I calculate my macros?",
            "I have dietary restrictions, can you help?",
            "What foods should I avoid for fat loss?",
        ],
        "recovery_coach": [
            # Questions specifically for recovery
            "How much sleep do I need for muscle recovery?",
            "What's the best way to recover from workouts?",
            "I'm always sore, what should I do?",
            "How do I prevent overtraining?",
            "What are good recovery techniques?",
            "Should I take rest days?",
            "How do I manage workout fatigue?",
            "What about foam rolling and stretching?",
            "I'm feeling burned out from training",
            "How do I optimize my recovery?",
        ],
        "mental_coach": [
            # Questions specifically for mental performance
            "How do I stay motivated to work out?",
            "I have gym anxiety, can you help?",
            "How do I set realistic fitness goals?",
            "I keep skipping workouts, what should I do?",
            "How do I build healthy habits?",
            "I'm stressed about my fitness progress",
            "How do I overcome workout plateaus mentally?",
            "I need help with consistency",
            "How do I handle fitness setbacks?",
            "I want to improve my mindset around fitness",
        ],
    },
    "multi_coach": [
        # Complex questions requiring multiple coaches
        "I want to lose weight and build muscle at the same time",
        "Can you create a complete fitness transformation plan?",
        "I'm training for a triathlon, need help with everything",
        "I want to get fit but don't know where to start with diet and exercise",
        "I need help with strength training, cardio, and nutrition for weight loss",
        "I'm recovering from an injury and want to get back in shape safely",
        "Can you help me optimize my training, diet, sleep, and mindset?",
        "I want to compete in bodybuilding, need comprehensive guidance",
        "I'm an athlete looking to improve performance in all areas",
        "Help me balance strength training with endurance training and proper nutrition",
        "I want to lose fat while maintaining muscle and staying mentally motivated",
        "Can you design a complete lifestyle change program for me?",
        "I need help with workout programming, meal planning, and recovery strategies",
        "I'm preparing for military fitness tests, need comprehensive training",
        "Help me with strength, cardio, nutrition, and mental preparation for competition",
        "I want to improve my overall health through fitness, diet, and lifestyle changes",
        "Can you help me with exercise selection, meal timing, and stress management?",
        "I need guidance on training intensity, nutrition timing, and recovery protocols",
        "Help me create a sustainable fitness routine with proper nutrition and mindset",
        "I want to optimize my performance through training, nutrition, recovery, and psychology",
    ],
}


async def test_optimized_coaching_graph():
    """Test the optimized coaching graph with categorized questions."""
    try:
        # Import the optimized graph
        from python_langgraph.athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import (
            create_optimized_test_graph,
        )

        logger.info("🚀 Creating optimized coaching graph...")
        graph = await create_optimized_test_graph()
        logger.info("✅ Graph created successfully!")

        # Test results storage
        test_results = {
            "greeting": [],
            "direct_coach": {},
            "multi_coach": [],
            "errors": [],
        }

        # Test greeting questions
        logger.info("\n" + "=" * 80)
        logger.info("🧪 TESTING GREETING QUESTIONS")
        logger.info("=" * 80)

        for i, question in enumerate(TEST_QUESTIONS["greeting"][:3], 1):  # Test first 3
            logger.info(f"\n🔍 Test {i}/3: '{question}'")

            try:
                start_time = time.time()

                result = await graph.ainvoke(
                    {
                        "user_query": question,
                        "messages": [],
                        "user_profile": {},
                    }
                )

                end_time = time.time()
                execution_time = end_time - start_time

                routing_decision = result.get("routing_decision", "unknown")
                current_node = result.get("current_node", "unknown")
                final_response = result.get("final_response", "")
                execution_steps = result.get("execution_steps", [])

                logger.info(f"  ⚡ Execution time: {execution_time:.2f}s")
                logger.info(f"  🎯 Routing decision: {routing_decision}")
                logger.info(f"  📍 Final node: {current_node}")
                logger.info(f"  📝 Response length: {len(final_response)} chars")
                logger.info(f"  🔄 Execution path: {' → '.join(execution_steps)}")

                # Validate expected routing
                expected_routing = "automated_greeting"
                if routing_decision == expected_routing:
                    logger.info("  ✅ CORRECT ROUTING!")
                else:
                    logger.warning(
                        f"  ⚠️  UNEXPECTED ROUTING! Expected: {expected_routing}"
                    )

                test_results["greeting"].append(
                    {
                        "question": question,
                        "routing_decision": routing_decision,
                        "current_node": current_node,
                        "execution_time": execution_time,
                        "execution_steps": execution_steps,
                        "correct_routing": routing_decision == expected_routing,
                    }
                )

            except Exception as e:
                logger.error(f"  ❌ ERROR: {e}")
                test_results["errors"].append(
                    {
                        "category": "greeting",
                        "question": question,
                        "error": str(e),
                    }
                )

        # Test direct coach questions
        logger.info("\n" + "=" * 80)
        logger.info("🧪 TESTING DIRECT COACH QUESTIONS")
        logger.info("=" * 80)

        for coach_name, questions in TEST_QUESTIONS["direct_coach"].items():
            logger.info(f"\n🎯 Testing {coach_name.upper()} questions:")
            test_results["direct_coach"][coach_name] = []

            for i, question in enumerate(questions[:2], 1):  # Test first 2 per coach
                logger.info(f"\n🔍 Test {i}/2: '{question}'")

                try:
                    start_time = time.time()

                    result = await graph.ainvoke(
                        {
                            "user_query": question,
                            "messages": [],
                            "user_profile": {},
                        }
                    )

                    end_time = time.time()
                    execution_time = end_time - start_time

                    routing_decision = result.get("routing_decision", "unknown")
                    primary_coach = result.get("primary_coach", "unknown")
                    current_node = result.get("current_node", "unknown")
                    final_response = result.get("final_response", "")
                    execution_steps = result.get("execution_steps", [])

                    logger.info(f"  ⚡ Execution time: {execution_time:.2f}s")
                    logger.info(f"  🎯 Routing decision: {routing_decision}")
                    logger.info(f"  👨‍🏫 Primary coach: {primary_coach}")
                    logger.info(f"  📍 Final node: {current_node}")
                    logger.info(f"  📝 Response length: {len(final_response)} chars")
                    logger.info(f"  🔄 Execution path: {' → '.join(execution_steps)}")

                    # Validate expected routing
                    expected_routing = "direct_coach"
                    expected_coach = coach_name

                    routing_correct = routing_decision == expected_routing
                    coach_correct = primary_coach == expected_coach

                    if routing_correct and coach_correct:
                        logger.info("  ✅ CORRECT ROUTING AND COACH!")
                    elif routing_correct:
                        logger.warning(
                            f"  ⚠️  CORRECT ROUTING but wrong coach! Expected: {expected_coach}"
                        )
                    else:
                        logger.warning(
                            f"  ⚠️  UNEXPECTED ROUTING! Expected: {expected_routing}"
                        )

                    test_results["direct_coach"][coach_name].append(
                        {
                            "question": question,
                            "routing_decision": routing_decision,
                            "primary_coach": primary_coach,
                            "current_node": current_node,
                            "execution_time": execution_time,
                            "execution_steps": execution_steps,
                            "correct_routing": routing_correct,
                            "correct_coach": coach_correct,
                        }
                    )

                except Exception as e:
                    logger.error(f"  ❌ ERROR: {e}")
                    test_results["errors"].append(
                        {
                            "category": f"direct_coach_{coach_name}",
                            "question": question,
                            "error": str(e),
                        }
                    )

        # Test multi-coach questions
        logger.info("\n" + "=" * 80)
        logger.info("🧪 TESTING MULTI-COACH QUESTIONS")
        logger.info("=" * 80)

        for i, question in enumerate(
            TEST_QUESTIONS["multi_coach"][:5], 1
        ):  # Test first 5
            logger.info(f"\n🔍 Test {i}/5: '{question}'")

            try:
                start_time = time.time()

                result = await graph.ainvoke(
                    {
                        "user_query": question,
                        "messages": [],
                        "user_profile": {},
                    }
                )

                end_time = time.time()
                execution_time = end_time - start_time

                routing_decision = result.get("routing_decision", "unknown")
                required_coaches = result.get("required_coaches", [])
                primary_coach = result.get("primary_coach", "unknown")
                current_node = result.get("current_node", "unknown")
                final_response = result.get("final_response", "")
                coach_responses = result.get("coach_responses", {})
                execution_steps = result.get("execution_steps", [])

                logger.info(f"  ⚡ Execution time: {execution_time:.2f}s")
                logger.info(f"  🎯 Routing decision: {routing_decision}")
                logger.info(f"  👥 Required coaches: {required_coaches}")
                logger.info(f"  👨‍🏫 Primary coach: {primary_coach}")
                logger.info(f"  🤖 Coach responses: {len(coach_responses)} coaches")
                logger.info(f"  📍 Final node: {current_node}")
                logger.info(f"  📝 Response length: {len(final_response)} chars")
                logger.info(f"  🔄 Execution path: {' → '.join(execution_steps)}")

                # Validate expected routing
                expected_routing = "multi_coach"
                routing_correct = routing_decision == expected_routing
                multi_coach_responses = len(coach_responses) > 1

                if routing_correct and multi_coach_responses:
                    logger.info("  ✅ CORRECT MULTI-COACH ROUTING!")
                elif routing_correct:
                    logger.warning(
                        "  ⚠️  CORRECT ROUTING but insufficient coach responses!"
                    )
                else:
                    logger.warning(
                        f"  ⚠️  UNEXPECTED ROUTING! Expected: {expected_routing}"
                    )

                test_results["multi_coach"].append(
                    {
                        "question": question,
                        "routing_decision": routing_decision,
                        "required_coaches": required_coaches,
                        "primary_coach": primary_coach,
                        "coach_responses_count": len(coach_responses),
                        "current_node": current_node,
                        "execution_time": execution_time,
                        "execution_steps": execution_steps,
                        "correct_routing": routing_correct,
                        "multi_coach_responses": multi_coach_responses,
                    }
                )

            except Exception as e:
                logger.error(f"  ❌ ERROR: {e}")
                test_results["errors"].append(
                    {
                        "category": "multi_coach",
                        "question": question,
                        "error": str(e),
                    }
                )

        # Print test summary
        logger.info("\n" + "=" * 80)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 80)

        # Greeting summary
        greeting_tests = len(test_results["greeting"])
        greeting_correct = sum(
            1 for t in test_results["greeting"] if t["correct_routing"]
        )
        logger.info(
            f"🧪 Greeting Tests: {greeting_correct}/{greeting_tests} correct routing"
        )

        # Direct coach summary
        total_direct_tests = 0
        total_direct_correct_routing = 0
        total_direct_correct_coach = 0

        for coach_name, results in test_results["direct_coach"].items():
            coach_tests = len(results)
            coach_correct_routing = sum(1 for t in results if t["correct_routing"])
            coach_correct_coach = sum(1 for t in results if t["correct_coach"])

            total_direct_tests += coach_tests
            total_direct_correct_routing += coach_correct_routing
            total_direct_correct_coach += coach_correct_coach

            logger.info(
                f"🧪 {coach_name}: {coach_correct_routing}/{coach_tests} routing, {coach_correct_coach}/{coach_tests} coach"
            )

        logger.info(
            f"🧪 Total Direct Coach: {total_direct_correct_routing}/{total_direct_tests} routing, {total_direct_correct_coach}/{total_direct_tests} coach"
        )

        # Multi-coach summary
        multi_tests = len(test_results["multi_coach"])
        multi_correct_routing = sum(
            1 for t in test_results["multi_coach"] if t["correct_routing"]
        )
        multi_correct_responses = sum(
            1 for t in test_results["multi_coach"] if t["multi_coach_responses"]
        )
        logger.info(
            f"🧪 Multi-Coach Tests: {multi_correct_routing}/{multi_tests} routing, {multi_correct_responses}/{multi_tests} multi-responses"
        )

        # Error summary
        error_count = len(test_results["errors"])
        logger.info(f"❌ Errors: {error_count}")

        if error_count > 0:
            logger.info("\nError Details:")
            for error in test_results["errors"]:
                logger.info(f"  - {error['category']}: {error['error']}")

        # Performance summary
        all_execution_times = []
        for result in test_results["greeting"]:
            all_execution_times.append(result["execution_time"])
        for coach_results in test_results["direct_coach"].values():
            for result in coach_results:
                all_execution_times.append(result["execution_time"])
        for result in test_results["multi_coach"]:
            all_execution_times.append(result["execution_time"])

        if all_execution_times:
            avg_time = sum(all_execution_times) / len(all_execution_times)
            min_time = min(all_execution_times)
            max_time = max(all_execution_times)

            logger.info(f"\n⚡ Performance Summary:")
            logger.info(f"  - Average execution time: {avg_time:.2f}s")
            logger.info(f"  - Fastest execution: {min_time:.2f}s")
            logger.info(f"  - Slowest execution: {max_time:.2f}s")

        # Save detailed results
        with open("optimized_graph_test_results.json", "w") as f:
            json.dump(test_results, f, indent=2, default=str)

        logger.info(
            f"\n💾 Detailed results saved to: optimized_graph_test_results.json"
        )
        logger.info("✅ Testing completed!")

        return test_results

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: {e}")
        import traceback

        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise


def print_test_questions():
    """Print all test questions categorized."""
    print("\n" + "=" * 80)
    print("🧪 TEST QUESTIONS FOR OPTIMIZED COACHING GRAPH")
    print("=" * 80)

    # Greeting questions
    print(f"\n📋 GREETING QUESTIONS ({len(TEST_QUESTIONS['greeting'])} total)")
    print("Expected routing: automated_greeting → optimized_greeting → END")
    print("-" * 60)
    for i, question in enumerate(TEST_QUESTIONS["greeting"], 1):
        print(f'{i:2d}. "{question}"')

    # Direct coach questions
    print(f"\n📋 DIRECT COACH QUESTIONS")
    print("Expected routing: direct_coach → [specific_coach] → END")
    print("-" * 60)

    for coach_name, questions in TEST_QUESTIONS["direct_coach"].items():
        print(f"\n🎯 {coach_name.upper()} ({len(questions)} questions)")
        for i, question in enumerate(questions, 1):
            print(f'{i:2d}. "{question}"')

    # Multi-coach questions
    print(f"\n📋 MULTI-COACH QUESTIONS ({len(TEST_QUESTIONS['multi_coach'])} total)")
    print("Expected routing: multi_coach → [primary_coach] → aggregation → END")
    print("-" * 60)
    for i, question in enumerate(TEST_QUESTIONS["multi_coach"], 1):
        print(f'{i:2d}. "{question}"')

    print(
        f"\n📊 TOTAL TEST QUESTIONS: {len(TEST_QUESTIONS['greeting']) + sum(len(q) for q in TEST_QUESTIONS['direct_coach'].values()) + len(TEST_QUESTIONS['multi_coach'])}"
    )


if __name__ == "__main__":
    print("🚀 Optimized Coaching Graph Test Questions")

    # Print all questions
    print_test_questions()

    # Run tests
    print(f"\n{'='*80}")
    print("🧪 RUNNING COMPREHENSIVE TESTS")
    print("=" * 80)

    try:
        results = asyncio.run(test_optimized_coaching_graph())
        print("\n✅ All tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
