/**
 * Proxy Data Transformation Unit Tests
 *
 * Tests the data transformation logic used in the onboarding proxy route
 * to ensure Python snake_case data is correctly transformed to TypeScript camelCase
 */

// Mock Python backend response data (snake_case format)
const MOCK_PYTHON_SIDEBAR_DATA = {
  current_stage: "gathering",
  goals: {
    exists: true,
    list: ["Build muscle", "Lose weight", "Run a 10k"],
  },
  summary_items: [
    {
      category: "Training Frequency",
      details: "3-4 times per week",
      isImportant: false,
    },
    {
      category: "Medical Conditions",
      details: "Previous knee injury",
      isImportant: true,
    },
  ],
  generated_plan: {
    planId: "test-plan-123",
    name: "Beginner Strength & Cardio Plan",
    description: "A comprehensive plan for beginners",
    duration: "12 weeks",
    level: "Beginner",
    planType: "Mixed",
    disciplines: ["Strength Training", "Running"],
    rationale: "Based on user goals and experience",
    phases: [
      {
        phaseName: "Foundation Phase",
        duration: "4 weeks",
        description: "Build basic strength and endurance",
      },
    ],
    exampleSessions: [
      {
        SessionName: "Full Body Strength",
        sessionType: "Strength",
        Duration: "60 minutes",
        SessionDescription: "Basic compound movements",
      },
    ],
  },
  sport_suggestions: [
    { label: "🏃 Running", value: "Running" },
    { label: "🏋️ Strength", value: "Strength Training" },
  ],
  selected_sport: "Running",
  selected_sports: ["Running", "Strength Training"],
};

// Expected TypeScript format (camelCase)
const EXPECTED_TYPESCRIPT_SIDEBAR_DATA = {
  currentStage: "gathering",
  goals: {
    exists: true,
    list: ["Build muscle", "Lose weight", "Run a 10k"],
  },
  summaryItems: [
    {
      category: "Training Frequency",
      details: "3-4 times per week",
      isImportant: false,
    },
    {
      category: "Medical Conditions",
      details: "Previous knee injury",
      isImportant: true,
    },
  ],
  generatedPlan: {
    planId: "test-plan-123",
    name: "Beginner Strength & Cardio Plan",
    description: "A comprehensive plan for beginners",
    duration: "12 weeks",
    level: "Beginner",
    planType: "Mixed",
    disciplines: ["Strength Training", "Running"],
    rationale: "Based on user goals and experience",
    phases: [
      {
        phaseName: "Foundation Phase",
        duration: "4 weeks",
        description: "Build basic strength and endurance",
      },
    ],
    exampleSessions: [
      {
        SessionName: "Full Body Strength",
        sessionType: "Strength",
        Duration: "60 minutes",
        SessionDescription: "Basic compound movements",
      },
    ],
  },
  sportSuggestions: [
    { label: "🏃 Running", value: "Running" },
    { label: "🏋️ Strength", value: "Strength Training" },
  ],
  selectedSport: "Running",
  selectedSports: ["Running", "Strength Training"],
};

/**
 * Transform Python sidebar data to TypeScript format
 * This replicates the transformation logic from the proxy route
 */
function transformPythonSidebarToTypeScript(pythonSidebarData) {
  return {
    currentStage: pythonSidebarData.current_stage || "initial",
    goals: {
      exists: pythonSidebarData.goals?.exists || false,
      list: pythonSidebarData.goals?.list || [],
    },
    summaryItems: pythonSidebarData.summary_items || [],
    generatedPlan: pythonSidebarData.generated_plan || null,
    sportSuggestions: pythonSidebarData.sport_suggestions || null,
    selectedSport: pythonSidebarData.selected_sport || null,
    selectedSports: pythonSidebarData.selected_sports || [],
  };
}

/**
 * Deep equality check for objects
 */
function deepEqual(obj1, obj2, path = "") {
  if (obj1 === obj2) return { isEqual: true };

  if (obj1 == null || obj2 == null) {
    return {
      isEqual: false,
      diff: `${path}: ${obj1} !== ${obj2}`,
    };
  }

  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return {
      isEqual: false,
      diff: `${path}: Array type mismatch`,
    };
  }

  if (Array.isArray(obj1)) {
    if (obj1.length !== obj2.length) {
      return {
        isEqual: false,
        diff: `${path}: Array length mismatch (${obj1.length} vs ${obj2.length})`,
      };
    }

    for (let i = 0; i < obj1.length; i++) {
      const result = deepEqual(obj1[i], obj2[i], `${path}[${i}]`);
      if (!result.isEqual) return result;
    }
    return { isEqual: true };
  }

  if (typeof obj1 !== "object" || typeof obj2 !== "object") {
    return {
      isEqual: obj1 === obj2,
      diff: obj1 !== obj2 ? `${path}: ${obj1} !== ${obj2}` : undefined,
    };
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return {
      isEqual: false,
      diff: `${path}: Key count mismatch (${keys1.length} vs ${keys2.length})`,
    };
  }

  for (const key of keys1) {
    if (!(key in obj2)) {
      return {
        isEqual: false,
        diff: `${path}: Missing key '${key}' in second object`,
      };
    }

    const result = deepEqual(
      obj1[key],
      obj2[key],
      path ? `${path}.${key}` : key,
    );
    if (!result.isEqual) return result;
  }

  return { isEqual: true };
}

/**
 * Test individual transformation scenarios
 */
function runTransformationTests() {
  console.log("🧪 PROXY TRANSFORMATION TESTS");
  console.log("=" * 40);

  let totalTests = 0;
  let passedTests = 0;
  const failures = [];

  function test(testName, testFn) {
    totalTests++;
    try {
      const result = testFn();
      if (result.passed) {
        passedTests++;
        console.log(`✅ ${testName}`);
      } else {
        failures.push({ testName, reason: result.reason });
        console.log(`❌ ${testName}: ${result.reason}`);
      }
    } catch (error) {
      failures.push({ testName, reason: error.message });
      console.log(`❌ ${testName}: ${error.message}`);
    }
  }

  // Test 1: Complete transformation
  test("Complete sidebar data transformation", () => {
    const transformed = transformPythonSidebarToTypeScript(
      MOCK_PYTHON_SIDEBAR_DATA,
    );
    const comparison = deepEqual(transformed, EXPECTED_TYPESCRIPT_SIDEBAR_DATA);

    return {
      passed: comparison.isEqual,
      reason: comparison.diff || "Transformation successful",
    };
  });

  // Test 2: Empty/null data handling
  test("Empty data handling", () => {
    const emptyData = {};
    const transformed = transformPythonSidebarToTypeScript(emptyData);

    const expected = {
      currentStage: "initial",
      goals: { exists: false, list: [] },
      summaryItems: [],
      generatedPlan: null,
      sportSuggestions: null,
      selectedSport: null,
      selectedSports: [],
    };

    const comparison = deepEqual(transformed, expected);
    return {
      passed: comparison.isEqual,
      reason: comparison.diff || "Empty data handled correctly",
    };
  });

  // Test 3: Partial data scenarios
  test("Partial data with only goals", () => {
    const partialData = {
      current_stage: "collecting_goals",
      goals: {
        exists: true,
        list: ["Single goal"],
      },
    };

    const transformed = transformPythonSidebarToTypeScript(partialData);

    return {
      passed:
        transformed.currentStage === "collecting_goals" &&
        transformed.goals.exists === true &&
        transformed.goals.list.length === 1 &&
        transformed.summaryItems.length === 0,
      reason: "Partial data transformation",
    };
  });

  // Test 4: Sport suggestions handling
  test("Sport suggestions transformation", () => {
    const dataWithSuggestions = {
      sport_suggestions: [
        { label: "🏃 Running", value: "Running" },
        { label: "🚴 Cycling", value: "Cycling" },
      ],
    };

    const transformed = transformPythonSidebarToTypeScript(dataWithSuggestions);

    return {
      passed:
        transformed.sportSuggestions?.length === 2 &&
        transformed.sportSuggestions[0].label === "🏃 Running",
      reason: "Sport suggestions transformation",
    };
  });

  // Test 5: Plan structure preservation
  test("Generated plan structure preservation", () => {
    const dataWithPlan = {
      generated_plan: {
        planId: "test-123",
        name: "Test Plan",
        phases: [{ phaseName: "Phase 1", duration: "2 weeks" }],
        exampleSessions: [{ SessionName: "Test Session" }],
      },
    };

    const transformed = transformPythonSidebarToTypeScript(dataWithPlan);

    return {
      passed:
        transformed.generatedPlan?.planId === "test-123" &&
        transformed.generatedPlan?.phases?.length === 1 &&
        transformed.generatedPlan?.exampleSessions?.length === 1,
      reason: "Plan structure preservation",
    };
  });

  // Test 6: Summary items structure
  test("Summary items array transformation", () => {
    const dataWithSummary = {
      summary_items: [
        {
          category: "Test Category",
          details: "Test details",
          isImportant: true,
        },
      ],
    };

    const transformed = transformPythonSidebarToTypeScript(dataWithSummary);

    return {
      passed:
        Array.isArray(transformed.summaryItems) &&
        transformed.summaryItems.length === 1 &&
        transformed.summaryItems[0].category === "Test Category",
      reason: "Summary items transformation",
    };
  });

  // Test 7: Selected sports array handling
  test("Selected sports array handling", () => {
    const dataWithSports = {
      selected_sports: ["Running", "Swimming", "Cycling"],
      selected_sport: "Running",
    };

    const transformed = transformPythonSidebarToTypeScript(dataWithSports);

    return {
      passed:
        Array.isArray(transformed.selectedSports) &&
        transformed.selectedSports.length === 3 &&
        transformed.selectedSport === "Running",
      reason: "Selected sports transformation",
    };
  });

  console.log("\n📊 TRANSFORMATION TEST RESULTS:");
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(
    `Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`,
  );

  if (failures.length > 0) {
    console.log("\n❌ FAILED TESTS:");
    failures.forEach((failure, index) => {
      console.log(`${index + 1}. ${failure.testName}: ${failure.reason}`);
    });
  }

  return {
    totalTests,
    passedTests,
    failedTests: totalTests - passedTests,
    failures,
    success: failures.length === 0,
  };
}

/**
 * Test the SSE event transformation
 */
function testSSEEventTransformation() {
  console.log("\n🌊 SSE EVENT TRANSFORMATION TEST");
  console.log("=" * 35);

  // Mock SSE chunk from Python backend
  const mockSSEChunk = `event: sidebar_update
data: {"type": "sidebar_update", "sidebarData": ${JSON.stringify(MOCK_PYTHON_SIDEBAR_DATA)}}

`;

  // Simulate the transformation logic from the proxy
  const processedChunk = mockSSEChunk.replace(
    /data: ({.*?})\n/g,
    (match, dataStr) => {
      try {
        const data = JSON.parse(dataStr);
        if (data.type === "sidebar_update" && data.sidebarData) {
          const transformedSidebarData = transformPythonSidebarToTypeScript(
            data.sidebarData,
          );
          const transformedData = {
            ...data,
            sidebarData: transformedSidebarData,
          };
          return `data: ${JSON.stringify(transformedData)}\n`;
        }
      } catch (e) {
        console.error("❌ Transformation error:", e);
      }
      return match;
    },
  );

  // Extract and parse the transformed data
  const dataMatch = processedChunk.match(/data: ({.*?})\n/);
  if (dataMatch) {
    try {
      const transformedEventData = JSON.parse(dataMatch[1]);
      const comparison = deepEqual(
        transformedEventData.sidebarData,
        EXPECTED_TYPESCRIPT_SIDEBAR_DATA,
      );

      if (comparison.isEqual) {
        console.log("✅ SSE event transformation successful");
        return { success: true };
      } else {
        console.log("❌ SSE event transformation failed:", comparison.diff);
        return { success: false, error: comparison.diff };
      }
    } catch (error) {
      console.log("❌ SSE parsing error:", error.message);
      return { success: false, error: error.message };
    }
  } else {
    console.log("❌ No data found in transformed SSE chunk");
    return { success: false, error: "No data match found" };
  }
}

/**
 * Main test runner
 */
function runAllTransformationTests() {
  console.log("🚀 Starting Proxy Transformation Tests\n");

  const transformationResults = runTransformationTests();
  const sseResults = testSSEEventTransformation();

  console.log("\n🎯 OVERALL TRANSFORMATION TEST RESULTS:");
  console.log(
    `Unit Tests: ${transformationResults.success ? "✅ PASSED" : "❌ FAILED"}`,
  );
  console.log(`SSE Tests: ${sseResults.success ? "✅ PASSED" : "❌ FAILED"}`);

  const overallSuccess = transformationResults.success && sseResults.success;
  console.log(
    `Overall: ${overallSuccess ? "✅ ALL PASSED" : "❌ SOME FAILED"}`,
  );

  return {
    transformationResults,
    sseResults,
    overallSuccess,
  };
}

// Export for use in other tests
export {
  transformPythonSidebarToTypeScript,
  deepEqual,
  runTransformationTests,
  testSSEEventTransformation,
  runAllTransformationTests,
  MOCK_PYTHON_SIDEBAR_DATA,
  EXPECTED_TYPESCRIPT_SIDEBAR_DATA,
};

// Run if executed directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  runAllTransformationTests();
}
