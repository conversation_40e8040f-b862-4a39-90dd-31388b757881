"""
Test Multi-Coach Executor Fix

Test that verifies the multi-coach executor properly handles Intelligence Hub decisions
and doesn't override them with hardcoded strength_coach defaults.

This test focuses on multi-coach scenarios where multiple coaches are needed.
"""

import asyncio
import logging
import pytest
from typing import Dict, Any
from unittest.mock import AsyncMock, patch, MagicMock

# Configure logging for the test
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_multi_coach_executor_preserves_required_coaches():
    """Test that multi-coach executor uses the required_coaches from Intelligence Hub."""

    logger.info("\n🧪 Testing Multi-Coach Executor with Intelligence Hub decisions")

    # Import the optimized graph to get the smart_coach_executor_node
    from athlea_langgraph.graphs.coaching_graph import (
        build_optimized_graph,
    )
    from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
        ComprehensiveCoachingConfig,
    )

    # Simulate Intelligence Hub output for a multi-coach scenario
    intelligence_hub_output = {
        "routing_decision": "multi_coach",
        "required_coaches": [
            "cardio",
            "nutrition",
            "recovery",
        ],  # Marathon training needs multiple coaches
        "primary_coach": "cardio_coach",  # Primary is cardio but all 3 should be executed
        "user_query": "I want to train for a marathon, including nutrition and recovery planning",
        "execution_steps": ["intelligence_hub"],
        "messages": [],
        "user_profile": {},
    }

    logger.info(f"🧠 Intelligence Hub decision:")
    logger.info(f"  - Primary coach: {intelligence_hub_output['primary_coach']}")
    logger.info(f"  - Required coaches: {intelligence_hub_output['required_coaches']}")
    logger.info(f"  - Routing decision: {intelligence_hub_output['routing_decision']}")

    # Create a mock config and build the graph to get the smart_coach_executor_node
    config = ComprehensiveCoachingConfig()

    # Mock the react_coaches to simulate coach execution
    mock_coaches = {}
    expected_executions = []

    for coach in intelligence_hub_output["required_coaches"]:
        coach_key = f"{coach}_coach"
        mock_coach = AsyncMock()
        mock_coach.execute.return_value = {
            "final_answer": f"Response from {coach} coach for marathon training"
        }
        mock_coaches[coach_key] = mock_coach
        expected_executions.append(coach_key)

    logger.info(f"🎯 Expected coach executions: {expected_executions}")

    # Also add strength_coach to detect if it gets called incorrectly
    mock_coaches["strength_coach"] = AsyncMock()
    mock_coaches["strength_coach"].execute.return_value = {
        "final_answer": "Response from strength coach (should NOT be called)"
    }

    # Mock the create_react_coaches function to return our mock coaches
    with patch(
        "athlea_langgraph.graphs.comprehensive_coaching_graph.create_react_coaches",
        return_value=mock_coaches,
    ):

        # Build the graph to get access to the smart_coach_executor_node
        graph_builder = await build_optimized_graph(config)

        # Extract the smart_coach_executor_node function
        # We need to find it in the graph nodes
        smart_executor_node = None
        for node_name, node_func in graph_builder.nodes.items():
            if node_name == "smart_executor":
                smart_executor_node = node_func
                break

        assert (
            smart_executor_node is not None
        ), "Could not find smart_executor node in graph"

        # Call the smart coach executor with Intelligence Hub state
        result = await smart_executor_node(intelligence_hub_output)

        # Verify all required coaches were executed
        coach_responses = result.get("coach_responses", {})
        logger.info(f"✅ Coach responses received: {list(coach_responses.keys())}")

        # Check that all required coaches were executed
        for coach in intelligence_hub_output["required_coaches"]:
            coach_key = f"{coach}_coach"
            assert coach_key in coach_responses, (
                f"❌ {coach_key} was not executed! "
                f"Expected coaches: {expected_executions}, "
                f"Actually executed: {list(coach_responses.keys())}"
            )

            # Verify the mock was called
            mock_coaches[coach_key].execute.assert_called_once()
            logger.info(f"✅ {coach_key} was properly executed")

        # Verify strength_coach was NOT executed (should not fallback)
        assert "strength_coach" not in coach_responses, (
            f"❌ strength_coach was unexpectedly executed! "
            f"This suggests incorrect fallback logic. "
            f"Executed coaches: {list(coach_responses.keys())}"
        )

        # Verify the strength_coach mock was NOT called
        mock_coaches["strength_coach"].execute.assert_not_called()
        logger.info("✅ strength_coach was correctly NOT executed")

        logger.info(
            "🎉 Multi-coach executor correctly preserved Intelligence Hub decisions!"
        )


@pytest.mark.asyncio
async def test_multi_coach_executor_fallback_behavior():
    """Test fallback behavior when primary_coach is missing but required_coaches exist."""

    logger.info("\n🧪 Testing Multi-Coach Executor fallback when primary_coach missing")

    from athlea_langgraph.graphs.coaching_graph import (
        build_optimized_graph,
    )
    from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
        ComprehensiveCoachingConfig,
    )

    # State with required_coaches but no primary_coach (edge case)
    state_without_primary = {
        "routing_decision": "multi_coach",
        "required_coaches": ["nutrition", "recovery"],  # Clear required coaches
        # Note: No primary_coach field!
        "user_query": "Help with nutrition and recovery",
        "execution_steps": ["intelligence_hub"],
        "messages": [],
        "user_profile": {},
    }

    # Mock the react_coaches
    mock_coaches = {}
    for coach in [
        "nutrition_coach",
        "recovery_coach",
        "strength_coach",
    ]:  # Include strength_coach to catch fallback
        mock_coach = AsyncMock()
        mock_coach.execute.return_value = {"final_answer": f"Response from {coach}"}
        mock_coaches[coach] = mock_coach

    config = ComprehensiveCoachingConfig()

    with patch(
        "athlea_langgraph.graphs.comprehensive_coaching_graph.create_react_coaches",
        return_value=mock_coaches,
    ):

        graph_builder = await build_optimized_graph(config)
        smart_executor_node = None

        for node_name, node_func in graph_builder.nodes.items():
            if node_name == "smart_executor":
                smart_executor_node = node_func
                break

        result = await smart_executor_node(state_without_primary)
        coach_responses = result.get("coach_responses", {})

        logger.info(
            f"📋 Executed coaches when primary_coach missing: {list(coach_responses.keys())}"
        )

        # Should still execute the required coaches, not just fallback to strength
        assert (
            "nutrition_coach" in coach_responses
        ), "nutrition_coach should be executed based on required_coaches"
        assert (
            "recovery_coach" in coach_responses
        ), "recovery_coach should be executed based on required_coaches"

        # Verify strength_coach is NOT executed just because it's the fallback
        # The multi-coach logic should use required_coaches, not the fallback primary_coach
        if "strength_coach" in coach_responses:
            logger.warning(
                "⚠️ strength_coach was executed - this might indicate fallback logic issues"
            )

        logger.info("✅ Multi-coach executor handles missing primary_coach correctly")


@pytest.mark.asyncio
async def test_single_coach_vs_multi_coach_routing():
    """Test that single coach scenarios don't get routed to multi-coach execution."""

    logger.info("\n🧪 Testing Single Coach vs Multi-Coach routing logic")

    from athlea_langgraph.graphs.coaching_graph import (
        build_optimized_graph,
    )
    from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
        ComprehensiveCoachingConfig,
    )

    # Single coach scenario
    single_coach_state = {
        "routing_decision": "direct_coach",
        "required_coaches": ["nutrition"],  # Only one coach
        "primary_coach": "nutrition_coach",
        "user_query": "What should I eat for breakfast?",
        "execution_steps": ["intelligence_hub"],
        "messages": [],
        "user_profile": {},
    }

    # Multi-coach scenario
    multi_coach_state = {
        "routing_decision": "multi_coach",
        "required_coaches": ["strength", "nutrition"],  # Multiple coaches
        "primary_coach": "strength_coach",
        "user_query": "Help me build muscle and optimize my nutrition",
        "execution_steps": ["intelligence_hub"],
        "messages": [],
        "user_profile": {},
    }

    test_cases = [
        ("Single Coach", single_coach_state, 1),  # Should execute 1 coach
        ("Multi Coach", multi_coach_state, 2),  # Should execute 2 coaches
    ]

    config = ComprehensiveCoachingConfig()

    for test_name, state, expected_count in test_cases:
        logger.info(f"\n📝 Testing {test_name} scenario")

        # Mock coaches
        mock_coaches = {}
        for coach in ["strength_coach", "nutrition_coach", "cardio_coach"]:
            mock_coach = AsyncMock()
            mock_coach.execute.return_value = {"final_answer": f"Response from {coach}"}
            mock_coaches[coach] = mock_coach

        with patch(
            "athlea_langgraph.graphs.comprehensive_coaching_graph.create_react_coaches",
            return_value=mock_coaches,
        ):

            graph_builder = await build_optimized_graph(config)
            smart_executor_node = None

            for node_name, node_func in graph_builder.nodes.items():
                if node_name == "smart_executor":
                    smart_executor_node = node_func
                    break

            result = await smart_executor_node(state)
            coach_responses = result.get("coach_responses", {})

            actual_count = len(coach_responses)
            logger.info(
                f"✅ {test_name}: Expected {expected_count} coaches, got {actual_count}"
            )
            logger.info(f"   Executed coaches: {list(coach_responses.keys())}")

            assert actual_count == expected_count, (
                f"❌ {test_name} failed: Expected {expected_count} coaches, got {actual_count}. "
                f"Executed: {list(coach_responses.keys())}"
            )


async def run_multi_coach_executor_tests():
    """Run all multi-coach executor tests."""

    print("🚀 Starting Multi-Coach Executor Tests\n")

    try:
        await test_multi_coach_executor_preserves_required_coaches()
        print("✅ Multi-coach preservation test passed")

        await test_multi_coach_executor_fallback_behavior()
        print("✅ Multi-coach fallback behavior test passed")

        await test_single_coach_vs_multi_coach_routing()
        print("✅ Single vs multi-coach routing test passed")

        print("\n🎉 ALL MULTI-COACH EXECUTOR TESTS PASSED!")
        print("\nThe multi-coach executor correctly:")
        print("  ✅ Preserves Intelligence Hub's required_coaches decisions")
        print("  ✅ Executes all required coaches (not just primary_coach)")
        print("  ✅ Handles single vs multi-coach scenarios correctly")
        print("  ✅ Doesn't fallback to strength_coach unnecessarily")

        return True

    except Exception as e:
        print(f"\n❌ Multi-coach executor test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(run_multi_coach_executor_tests())
    exit(0 if success else 1)
