"""
GraphRAG Integration Test Script

Test script to verify GraphRAG integration with existing Athlea infrastructure.
Specifically tests the connection to the futsal study document (DOI: 10.3390/healthcare12141387).
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

# Add the athlea_langgraph package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "athlea_langgraph"))

from athlea_langgraph.services.graphrag_service import (
    get_graphrag_service,
    GraphRAGQueryInput,
)
from athlea_langgraph.services.gremlin_service import get_gremlin_service
from athlea_langgraph.tools.external.azure_search_retriever import (
    AzureSearchRetrieverTool,
)
from athlea_langgraph.tools.external.graphrag_tool import create_graphrag_tool
from athlea_langgraph.config.graphrag_config import (
    get_graphrag_config,
    validate_graphrag_config,
)
from athlea_langgraph.agents.graphrag_node import graphrag_agent_node
from athlea_langgraph.states import AgentState

from langchain_core.messages import HumanMessage

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_graphrag_config():
    """Test GraphRAG configuration validation."""
    print("\n=== Testing GraphRAG Configuration ===")
    try:
        config = get_graphrag_config()
        print(f"✓ Configuration loaded successfully")
        print(f"  - Cosmos endpoint: {config.cosmos_endpoint}")
        print(f"  - ACS index: {config.acs_index_name}")
        print(f"  - Test document ID: {config.test_document_id}")
        print(f"  - Test DOI: {config.test_document_doi}")

        # Validate configuration
        validate_graphrag_config()
        print("✓ Configuration validation passed")
        return True

    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


async def test_azure_search_connection():
    """Test Azure Cognitive Search connection with futsal document."""
    print("\n=== Testing Azure Cognitive Search Connection ===")
    try:
        acs_tool = AzureSearchRetrieverTool()
        if not acs_tool.is_initialized:
            print("✗ ACS tool not initialized")
            return False

        # Test query for futsal document
        config = get_graphrag_config()
        query_params = {
            "query": "FIFA 11+ futsal injury prevention",
            "filter_expression": f"source_id eq '{config.test_document_id}'",
            "top_k": 5,
            "vector_search": True,
            "hybrid_search": True,
        }

        result = await acs_tool.invoke(query_params)

        if result.success:
            print(f"✓ ACS query successful")
            print(f"  - Found {result.result_count} chunks")
            print(f"  - Execution time: {result.execution_time_ms}ms")
            if result.results:
                first_chunk = result.results[0]
                print(f"  - First chunk preview: {first_chunk.content[:100]}...")
            return True
        else:
            print(f"✗ ACS query failed: {result.message}")
            return False

    except Exception as e:
        print(f"✗ ACS connection test failed: {e}")
        return False


async def test_gremlin_connection():
    """Test Gremlin Graph connection with futsal document."""
    print("\n=== Testing Gremlin Graph Connection ===")
    try:
        gremlin_service = get_gremlin_service()
        config = get_graphrag_config()

        # Test finding document by ID
        print("Testing document lookup by ID...")
        result = await gremlin_service.find_document_by_id(config.test_document_id)

        if result.success and result.results:
            print(f"✓ Found document by ID")
            print(f"  - Result count: {result.result_count}")
            print(f"  - Execution time: {result.execution_time_ms}ms")
            doc_details = result.results[0] if result.results else {}
            print(f"  - Document details: {list(doc_details.keys())}")
        else:
            print(f"◐ Document not found by ID (might not be in graph yet)")

        # Test finding document by DOI
        print("Testing document lookup by DOI...")
        result = await gremlin_service.find_document_by_doi(config.test_document_doi)

        if result.success and result.results:
            print(f"✓ Found document by DOI")
            print(f"  - Result count: {result.result_count}")
            doc_details = result.results[0] if result.results else {}
            print(f"  - Document details: {list(doc_details.keys())}")
        else:
            print(f"◐ Document not found by DOI (might not be in graph yet)")

        # Test finding FIFA 11+ entity
        print("Testing entity lookup (FIFA 11+)...")
        result = await gremlin_service.find_entity_relationships("FIFA 11+")

        if result.success:
            print(f"✓ Entity query successful")
            print(f"  - Found {result.result_count} results")
            if result.results:
                entity_details = result.results[0] if result.results else {}
                print(f"  - Entity details: {list(entity_details.keys())}")
        else:
            print(f"◐ FIFA 11+ entity not found in graph")

        return True

    except Exception as e:
        print(f"✗ Gremlin connection test failed: {e}")
        return False


async def test_unified_graphrag_service():
    """Test the unified GraphRAG service with futsal study."""
    print("\n=== Testing Unified GraphRAG Service ===")
    try:
        graphrag_service = get_graphrag_service()

        # Test the pre-built futsal study query
        print("Testing futsal study query...")
        result = await graphrag_service.query_futsal_study()

        if result.success:
            print(f"✓ GraphRAG futsal query successful")
            print(f"  - ACS chunks found: {result.acs_chunk_count}")
            print(f"  - Graph entities found: {result.graph_entity_count}")
            print(f"  - Graph relationships: {result.graph_relationship_count}")
            print(f"  - Causalities extracted: {len(result.causalities)}")
            print(f"  - Recommendations: {len(result.recommendations)}")
            print(f"  - Execution time: {result.execution_time_ms}ms")

            if result.synthesis:
                print(f"  - Synthesis preview: {result.synthesis[:150]}...")

            if result.causalities:
                print("  - Example causality:")
                causality = result.causalities[0]
                print(
                    f"    {causality.get('source_entity')} {causality.get('relationship')} {causality.get('target_entity')}"
                )

            return True
        else:
            print(f"✗ GraphRAG query failed: {result.message}")
            return False

    except Exception as e:
        print(f"✗ GraphRAG service test failed: {e}")
        return False


async def test_graphrag_tool():
    """Test the GraphRAG LangChain tool."""
    print("\n=== Testing GraphRAG LangChain Tool ===")
    try:
        tool = create_graphrag_tool()

        # Test tool with futsal query
        result = await tool._arun(
            query="How effective is FIFA 11+ for preventing injuries in futsal players?",
            entities=["FIFA 11+", "FUTSAL"],
            include_causalities=True,
            top_k=5,
        )

        if "Error" not in result:
            print(f"✓ GraphRAG tool execution successful")
            print(f"  - Response length: {len(result)} characters")
            print(f"  - Response preview: {result[:200]}...")
            return True
        else:
            print(f"✗ GraphRAG tool failed: {result}")
            return False

    except Exception as e:
        print(f"✗ GraphRAG tool test failed: {e}")
        return False


async def test_graphrag_agent_node():
    """Test the GraphRAG agent node."""
    print("\n=== Testing GraphRAG Agent Node ===")
    try:
        # Create test state
        test_state: AgentState = {
            "messages": [
                HumanMessage(
                    content="Tell me about the FIFA 11+ program for futsal injury prevention"
                )
            ]
        }

        # Run the GraphRAG agent node
        result_state = await graphrag_agent_node(test_state)

        if result_state and "messages" in result_state:
            messages = result_state["messages"]
            if len(messages) > 1:
                ai_response = messages[-1]
                print(f"✓ GraphRAG agent node successful")
                print(f"  - Response type: {type(ai_response).__name__}")
                print(f"  - Response length: {len(ai_response.content)} characters")
                print(f"  - Response preview: {ai_response.content[:200]}...")

                # Check for GraphRAG metadata
                if (
                    hasattr(ai_response, "additional_kwargs")
                    and "graphrag_metadata" in ai_response.additional_kwargs
                ):
                    metadata = ai_response.additional_kwargs["graphrag_metadata"]
                    print(f"  - GraphRAG metadata: {metadata}")

                return True
            else:
                print(f"✗ No AI response generated")
                return False
        else:
            print(f"✗ Agent node returned invalid state")
            return False

    except Exception as e:
        print(f"✗ GraphRAG agent node test failed: {e}")
        return False


async def test_specific_causalities():
    """Test extraction of specific causalities from futsal research."""
    print("\n=== Testing Specific Causality Extraction ===")
    try:
        graphrag_service = get_graphrag_service()

        # Test queries for each known causality from the futsal research
        test_queries = [
            "FIFA 11+ injury prevention protocol",
            "Pilates flexibility improvement",
            "Proprioceptive training ankle injury prevention",
            "HIIT game performance improvement",
        ]

        total_causalities = 0
        for query in test_queries:
            print(f"  Testing: {query}")
            result = await graphrag_service.execute_graphrag_query(
                GraphRAGQueryInput(query=query, top_k=5)
            )

            if result.success:
                causalities_found = len(result.causalities)
                total_causalities += causalities_found
                print(f"    ✓ Found {causalities_found} causalities")

                for causality in result.causalities[:2]:  # Show first 2
                    conf = causality.get("confidence", 0)
                    rel = causality.get("relationship", "UNKNOWN")
                    source = causality.get("source_entity", "Unknown")
                    target = causality.get("target_entity", "Unknown")
                    print(f"      - {source} {rel} {target} (conf: {conf:.2f})")
            else:
                print(f"    ✗ Query failed: {result.message}")

        print(f"✓ Total causalities extracted: {total_causalities}")
        return total_causalities > 0

    except Exception as e:
        print(f"✗ Causality extraction test failed: {e}")
        return False


async def run_comprehensive_test():
    """Run comprehensive GraphRAG integration test."""
    print("🚀 Starting Comprehensive GraphRAG Integration Test")
    print("=" * 60)

    test_results = {}

    # Run all tests
    test_results["config"] = await test_graphrag_config()
    test_results["acs"] = await test_azure_search_connection()
    test_results["gremlin"] = await test_gremlin_connection()
    test_results["unified_service"] = await test_unified_graphrag_service()
    test_results["tool"] = await test_graphrag_tool()
    test_results["agent_node"] = await test_graphrag_agent_node()
    test_results["causalities"] = await test_specific_causalities()

    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)

    passed = sum(test_results.values())
    total = len(test_results)

    for test_name, result in test_results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name.upper():<20} {status}")

    print(f"\nOVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("\n🎉 ALL TESTS PASSED! GraphRAG integration is working correctly.")
        print("\n📚 The system can now:")
        print("   - Connect to Azure Cognitive Search for textual evidence")
        print("   - Query Cosmos DB Gremlin Graph for structured relationships")
        print("   - Extract causal relationships with confidence scores")
        print("   - Generate evidence-based coaching recommendations")
        print("   - Provide specific protocols with dosages and conditions")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Check the logs above for details.")
        print("\n🔧 Common issues:")
        print("   - Missing environment variables (Azure credentials)")
        print("   - Network connectivity to Azure services")
        print("   - Data not yet populated in the knowledge graph")

    return passed == total


if __name__ == "__main__":
    # Set up environment variables if not already set
    if not os.getenv("AZURE_SEARCH_SERVICE_NAME"):
        print("⚠️  Warning: AZURE_SEARCH_SERVICE_NAME not set in environment")
    if not os.getenv("AZURE_SEARCH_API_KEY"):
        print("⚠️  Warning: AZURE_SEARCH_API_KEY not set in environment")

    # Run the comprehensive test
    asyncio.run(run_comprehensive_test())
