"""
Comprehensive Tests for Web Search Implementation

This test suite thoroughly tests all components of the web search implementation
including tools, agents, state management, and workflows.
"""

import asyncio
import json
import os
from unittest.mock import AsyncMock, MagicMock, patch
import pytest
from langchain_core.messages import AIMessage, SystemMessage

from athlea_langgraph.tools.web_search_tool import (
    WebSearchTool,
    WebScrapingTool,
    WebResearchTool,
    SerperSearchTool,
    WebScraperTool,
    WebSearchInput,
    WebScrapingInput,
    WebResearchInput,
)
from athlea_langgraph.states.web_search_state import (
    WebSearchState,
    WebSearchResult,
    ScrapedWebContent,
    WebSearchPlan,
)
from athlea_langgraph.agents.web_search_agents import (
    WebSearchPlannerAgent,
    WebSearchExecutorAgent,
    WebContentScrapingAgent,
    WebContentAnalyzerAgent,
    WebResearchSynthesizerAgent,
)
from athlea_langgraph.graphs.web_search_graph import (
    WebSearchGraph,
    WebSearchGraphFactory,
    quick_web_search,
    comprehensive_web_research,
)


# Mock data for testing
MOCK_SERPER_RESPONSE = {
    "organic": [
        {
            "title": "AI Research Paper 2024",
            "link": "https://example.com/ai-research",
            "snippet": "Latest developments in artificial intelligence research...",
        },
        {
            "title": "Machine Learning Trends",
            "link": "https://example.com/ml-trends",
            "snippet": "Current trends in machine learning and deep learning...",
        },
    ]
}

MOCK_SCRAPED_CONTENT = """
<html>
<head><title>AI Research Paper 2024</title></head>
<body>
<h1>Artificial Intelligence Research</h1>
<p>This paper discusses the latest advances in AI research including large language models, computer vision, and robotics.</p>
<p>Key findings include improved performance in natural language understanding and generation.</p>
</body>
</html>
"""


class TestWebSearchTools:
    """Test suite for web search tools."""

    @pytest.fixture
    def mock_serper_tool(self):
        """Create a mock SerperSearchTool."""
        tool = SerperSearchTool(api_key="test_key")
        tool.session = AsyncMock()
        return tool

    @pytest.fixture
    def mock_scraper_tool(self):
        """Create a mock WebScraperTool."""
        return WebScraperTool()

    @pytest.mark.asyncio
    async def test_serper_search_tool_success(self, mock_serper_tool):
        """Test successful search with SerperSearchTool."""
        # Mock the HTTP response
        mock_response = AsyncMock()
        mock_response.json.return_value = MOCK_SERPER_RESPONSE
        mock_response.raise_for_status.return_value = None

        mock_serper_tool.session.post.return_value.__aenter__.return_value = (
            mock_response
        )

        result = await mock_serper_tool.search("AI research 2024")

        assert "organic" in result
        assert len(result["organic"]) == 2
        assert result["organic"][0]["title"] == "AI Research Paper 2024"

    @pytest.mark.asyncio
    async def test_web_scraper_tool_success(self, mock_scraper_tool):
        """Test successful web scraping."""
        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.text.return_value = MOCK_SCRAPED_CONTENT
            mock_response.status = 200
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await mock_scraper_tool.scrape_url(
                "https://example.com/ai-research"
            )

            assert result.status == "success"
            assert "Artificial Intelligence Research" in result.content
            assert result.url == "https://example.com/ai-research"

    @pytest.mark.asyncio
    async def test_web_search_tool_integration(self):
        """Test WebSearchTool with mocked dependencies."""
        with patch.dict(os.environ, {"SERPER_API_KEY": "test_key"}):
            tool = WebSearchTool()

            # Mock the SerperSearchTool
            with patch.object(tool.serper_tool, "search") as mock_search:
                mock_search.return_value = MOCK_SERPER_RESPONSE

                input_data = WebSearchInput(query="AI research 2024", max_results=5)

                result = await tool._execute_tool(input_data)

                assert result["query"] == "AI research 2024"
                assert result["total_results"] == 2
                assert len(result["search_results"]) == 2

    @pytest.mark.asyncio
    async def test_web_scraping_tool_integration(self):
        """Test WebScrapingTool with mocked dependencies."""
        tool = WebScrapingTool()

        # Mock the WebScraperTool
        with patch.object(tool.scraper_tool, "scrape_url") as mock_scrape:
            mock_content = ScrapedWebContent(
                url="https://example.com/test",
                title="Test Page",
                content="Test content",
                content_length=12,
                status="success",
            )
            mock_scrape.return_value = mock_content

            input_data = WebScrapingInput(url="https://example.com/test")

            result = await tool._execute_tool(input_data)

            assert result["scraped_content"]["status"] == "success"
            assert result["scraped_content"]["content"] == "Test content"

    @pytest.mark.asyncio
    async def test_web_research_tool_integration(self):
        """Test WebResearchTool end-to-end functionality."""
        with patch.dict(os.environ, {"SERPER_API_KEY": "test_key"}):
            tool = WebResearchTool()

            # Mock search and scraping
            with (
                patch.object(tool.search_tool, "_execute_tool") as mock_search,
                patch.object(tool.scraping_tool, "_execute_tool") as mock_scrape,
            ):

                mock_search.return_value = {
                    "query": "AI research",
                    "total_results": 1,
                    "search_results": [
                        {
                            "title": "AI Research",
                            "url": "https://example.com/ai",
                            "snippet": "AI research content",
                            "position": 1,
                            "source_domain": "example.com",
                        }
                    ],
                }

                mock_scrape.return_value = {
                    "scraped_content": {
                        "url": "https://example.com/ai",
                        "title": "AI Research",
                        "content": "Detailed AI research content",
                        "content_length": 26,
                        "status": "success",
                    }
                }

                input_data = WebResearchInput(
                    research_question="What is AI research?", max_search_results=3
                )

                result = await tool._execute_tool(input_data)

                assert result["research_question"] == "What is AI research?"
                assert len(result["search_results"]) == 1
                assert len(result["scraped_contents"]) == 1
                assert "research_summary" in result


class TestWebSearchState:
    """Test suite for web search state management."""

    def test_web_search_state_initialization(self):
        """Test WebSearchState initialization."""
        state = WebSearchState(
            research_question="What is machine learning?", research_type="academic"
        )

        assert state.research_question == "What is machine learning?"
        assert state.research_type == "academic"
        assert state.current_step == "planning"
        assert len(state.search_results) == 0

    def test_add_search_result(self):
        """Test adding search results to state."""
        state = WebSearchState()

        result = WebSearchResult(
            title="ML Paper",
            url="https://example.com/ml",
            snippet="Machine learning research",
            position=1,
            source_domain="example.com",
        )

        state.add_search_result(result)

        assert len(state.search_results) == 1
        assert state.search_results[0].title == "ML Paper"

    def test_add_scraped_content(self):
        """Test adding scraped content to state."""
        state = WebSearchState()

        content = ScrapedWebContent(
            url="https://example.com/test",
            title="Test Page",
            content="Test content",
            content_length=12,
            status="success",
        )

        state.add_scraped_content(content)

        assert len(state.scraped_contents) == 1
        assert len(state.successful_scrapes) == 1

    def test_get_research_metrics(self):
        """Test research metrics calculation."""
        state = WebSearchState()

        # Add search result
        result = WebSearchResult(
            title="Test",
            url="https://example.com/test",
            snippet="Test snippet",
            position=1,
            source_domain="example.com",
        )
        state.add_search_result(result)

        # Add scraped content
        content = ScrapedWebContent(
            url="https://example.com/test",
            title="Test",
            content="Test content",
            content_length=12,
            status="success",
        )
        state.add_scraped_content(content)

        metrics = state.get_research_metrics()

        assert metrics["total_search_results"] == 1
        assert metrics["successful_scrapes"] == 1
        assert metrics["total_content_length"] == 12
        assert metrics["unique_domains"] == 1


class TestWebSearchAgents:
    """Test suite for web search agents."""

    @pytest.fixture
    def mock_llm(self):
        """Create a mock LLM."""
        llm = AsyncMock()
        llm.ainvoke.return_value = AIMessage(
            content='{"search_terms": ["AI research", "machine learning"], "search_strategy": "comprehensive", "expected_sources": ["academic"], "content_focus": "research"}'
        )
        return llm

    @pytest.mark.asyncio
    async def test_planner_agent(self, mock_llm):
        """Test WebSearchPlannerAgent."""
        agent = WebSearchPlannerAgent(mock_llm)

        state = WebSearchState(
            research_question="What is artificial intelligence?",
            research_type="general",
        )

        result_state = await agent.plan_research(state)

        assert result_state.search_plan is not None
        assert len(result_state.search_queries) > 0
        assert result_state.current_step == "searching"

    @pytest.mark.asyncio
    async def test_search_executor_agent(self):
        """Test WebSearchExecutorAgent."""
        mock_search_tool = AsyncMock()
        mock_search_tool._execute_tool.return_value = {
            "query": "AI research",
            "total_results": 1,
            "search_results": [
                {
                    "title": "AI Research",
                    "url": "https://example.com/ai",
                    "snippet": "AI content",
                    "position": 1,
                    "source_domain": "example.com",
                }
            ],
        }

        agent = WebSearchExecutorAgent(mock_search_tool)

        state = WebSearchState(search_queries=["AI research"], max_search_results=5)

        result_state = await agent.execute_searches(state)

        assert len(result_state.search_results) == 1
        assert len(result_state.selected_urls) == 1

    @pytest.mark.asyncio
    async def test_content_scraping_agent(self):
        """Test WebContentScrapingAgent."""
        mock_scraping_tool = AsyncMock()
        mock_scraping_tool._execute_tool.return_value = {
            "scraped_content": {
                "url": "https://example.com/test",
                "title": "Test",
                "content": "Test content",
                "content_length": 12,
                "status": "success",
            }
        }

        agent = WebContentScrapingAgent(mock_scraping_tool)

        state = WebSearchState(
            selected_urls=["https://example.com/test"], max_pages_to_scrape=3
        )

        result_state = await agent.scrape_content(state)

        assert len(result_state.scraped_contents) == 1
        assert result_state.scraped_contents[0].status == "success"

    @pytest.mark.asyncio
    async def test_content_analyzer_agent(self, mock_llm):
        """Test WebContentAnalyzerAgent."""
        mock_llm.ainvoke.return_value = AIMessage(
            content='{"quality_score": 0.85, "key_findings": ["Finding 1", "Finding 2"], "content_themes": ["AI", "Research"]}'
        )

        agent = WebContentAnalyzerAgent(mock_llm)

        content = ScrapedWebContent(
            url="https://example.com/test",
            title="Test",
            content="Test content about AI research",
            content_length=26,
            status="success",
        )

        state = WebSearchState(
            research_question="What is AI?", scraped_contents=[content]
        )

        result_state = await agent.analyze_content(state)

        assert result_state.quality_score == 0.85
        assert len(result_state.key_findings) == 2

    @pytest.mark.asyncio
    async def test_research_synthesizer_agent(self, mock_llm):
        """Test WebResearchSynthesizerAgent."""
        mock_llm.ainvoke.return_value = AIMessage(
            content="This is a comprehensive research summary based on the analyzed content."
        )

        agent = WebResearchSynthesizerAgent(mock_llm)

        content = ScrapedWebContent(
            url="https://example.com/test",
            title="AI Research",
            content="Detailed AI research content",
            content_length=26,
            status="success",
        )

        state = WebSearchState(
            research_question="What is AI research?",
            scraped_contents=[content],
            key_findings=["AI is important", "Research is ongoing"],
        )

        result_state = await agent.synthesize_research(state)

        assert result_state.research_summary is not None
        assert "comprehensive research summary" in result_state.research_summary
        assert result_state.current_step == "complete"


class TestWebSearchGraph:
    """Test suite for web search graph workflow."""

    @pytest.fixture
    def mock_llm(self):
        """Create a mock LLM."""
        llm = AsyncMock()
        return llm

    @pytest.mark.asyncio
    async def test_web_search_graph_initialization(self, mock_llm):
        """Test WebSearchGraph initialization."""
        with patch.dict(os.environ, {"SERPER_API_KEY": "test_key"}):
            graph = WebSearchGraph(llm=mock_llm, serper_api_key="test_key")

            assert graph.llm == mock_llm
            assert graph.serper_api_key == "test_key"
            assert graph.search_tool is not None
            assert graph.scraping_tool is not None

    @pytest.mark.asyncio
    async def test_web_search_graph_factory(self, mock_llm):
        """Test WebSearchGraphFactory."""
        with patch.dict(os.environ, {"SERPER_API_KEY": "test_key"}):
            # Test basic graph
            basic_graph = WebSearchGraphFactory.create_basic_graph(
                llm=mock_llm, serper_api_key="test_key"
            )
            assert isinstance(basic_graph, WebSearchGraph)

            # Test comprehensive graph
            comprehensive_graph = WebSearchGraphFactory.create_comprehensive_graph(
                llm=mock_llm, serper_api_key="test_key"
            )
            assert isinstance(comprehensive_graph, WebSearchGraph)

            # Test research graph
            research_graph = WebSearchGraphFactory.create_research_graph(
                llm=mock_llm, serper_api_key="test_key"
            )
            assert isinstance(research_graph, WebSearchGraph)

    @pytest.mark.asyncio
    async def test_quick_web_search(self, mock_llm):
        """Test quick_web_search utility function."""
        with patch(
            "athlea_langgraph.graphs.web_search_graph.WebSearchGraph"
        ) as MockGraph:
            mock_graph = AsyncMock()
            mock_graph.run_research.return_value = WebSearchState(
                research_question="Test question", research_summary="Test summary"
            )
            MockGraph.return_value = mock_graph

            result = await quick_web_search(
                question="What is AI?", llm=mock_llm, serper_api_key="test_key"
            )

            assert "research_question" in result
            assert "research_summary" in result

    @pytest.mark.asyncio
    async def test_comprehensive_web_research(self, mock_llm):
        """Test comprehensive_web_research utility function."""
        with patch(
            "athlea_langgraph.graphs.web_search_graph.WebSearchGraph"
        ) as MockGraph:
            mock_graph = AsyncMock()
            mock_graph.run_research.return_value = WebSearchState(
                research_question="Test question",
                research_context="Test context",
                research_summary="Comprehensive summary",
            )
            MockGraph.return_value = mock_graph

            result = await comprehensive_web_research(
                question="Impact of AI on society",
                context="Focus on ethical implications",
                llm=mock_llm,
                serper_api_key="test_key",
            )

            assert result.research_question == "Test question"
            assert result.research_context == "Test context"


class TestErrorHandling:
    """Test suite for error handling scenarios."""

    @pytest.mark.asyncio
    async def test_serper_api_error_handling(self):
        """Test handling of Serper API errors."""
        with patch.dict(os.environ, {"SERPER_API_KEY": "test_key"}):
            tool = WebSearchTool()

            # Mock API failure
            with patch.object(tool.serper_tool, "search") as mock_search:
                mock_search.side_effect = Exception("API Error")

                input_data = WebSearchInput(query="test query")

                # Should handle error gracefully
                result = await tool._generate_fallback(Exception("API Error"))
                assert result is not None

    @pytest.mark.asyncio
    async def test_scraping_error_handling(self):
        """Test handling of web scraping errors."""
        tool = WebScrapingTool()

        # Mock scraping failure
        with patch.object(tool.scraper_tool, "scrape_url") as mock_scrape:
            mock_scrape.side_effect = Exception("Network Error")

            input_data = WebScrapingInput(url="https://invalid-url.com")

            # Should handle error gracefully
            result = await tool._generate_fallback(Exception("Network Error"))
            assert result is not None

    @pytest.mark.asyncio
    async def test_agent_error_recovery(self):
        """Test agent error recovery mechanisms."""
        mock_llm = AsyncMock()
        mock_llm.ainvoke.side_effect = Exception("LLM Error")

        agent = WebSearchPlannerAgent(mock_llm)

        state = WebSearchState(research_question="Test question")

        # Should create fallback plan
        result_state = await agent.plan_research(state)

        assert result_state.search_plan is not None
        assert len(result_state.search_queries) > 0


class TestPerformanceAndLimits:
    """Test suite for performance and limit testing."""

    @pytest.mark.asyncio
    async def test_content_length_limits(self):
        """Test content length limiting."""
        tool = WebScraperTool()

        # Create very long content
        long_content = "a" * 10000  # 10k characters

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.text.return_value = (
                f"<html><body>{long_content}</body></html>"
            )
            mock_response.status = 200
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.scrape_url(
                "https://example.com/long-content", max_content_length=4000
            )

            # Should be limited to max_content_length
            assert len(result.content) <= 4000

    @pytest.mark.asyncio
    async def test_search_result_limits(self):
        """Test search result limiting."""
        with patch.dict(os.environ, {"SERPER_API_KEY": "test_key"}):
            tool = WebSearchTool()

            # Mock many search results
            large_response = {
                "organic": [
                    {
                        "title": f"Result {i}",
                        "link": f"https://example.com/{i}",
                        "snippet": f"Snippet {i}",
                    }
                    for i in range(50)  # 50 results
                ]
            }

            with patch.object(tool.serper_tool, "search") as mock_search:
                mock_search.return_value = large_response

                input_data = WebSearchInput(query="test query", max_results=10)

                result = await tool._execute_tool(input_data)

                # Should be limited to max_results
                assert len(result["search_results"]) <= 10


if __name__ == "__main__":
    # Run basic test to verify imports
    print("Testing web search tool imports...")

    from athlea_langgraph.tools.web_search_tool import WebSearchTool
    from athlea_langgraph.states.web_search_state import WebSearchState
    from athlea_langgraph.agents.web_search_agents import WebSearchPlannerAgent
    from athlea_langgraph.graphs.web_search_graph import WebSearchGraph

    print("✅ All web search components imported successfully!")
    print("🔍 Web search implementation is ready for testing!")
    print("\nTo run the full test suite:")
    print("pytest tests/test_web_search_comprehensive.py -v")
