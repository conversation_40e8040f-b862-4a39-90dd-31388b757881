/**
 * Comprehensive Onboarding Data Validation Test
 *
 * This test validates that:
 * 1. Python backend sends correct data formats
 * 2. <PERSON><PERSON> correctly transforms Python data to TypeScript format
 * 3. Frontend receives all expected data structures
 * 4. Data transformations preserve all necessary information
 * 5. Sidebar updates contain complete information
 */

import { v4 as uuidv4 } from "uuid";

const FRONTEND_URL = "http://localhost:3000";
const PYTHON_BACKEND_URL = "http://localhost:8000";

// Test configuration
const TEST_CONFIG = {
  userId: `data-validation-${Date.now()}`,
  threadId: `thread-${Date.now()}`,
  timeout: 30000, // 30 seconds
  maxEvents: 100,
};

// Expected data structures based on the TypeScript interfaces
const EXPECTED_STRUCTURES = {
  sidebarData: {
    currentStage: "string",
    goals: {
      exists: "boolean",
      list: "array",
    },
    summaryItems: "array",
    generatedPlan: "object|null",
    sportSuggestions: "array|null",
    selectedSport: "string|null",
    selectedSports: "array",
  },
  goalStructure: {
    exists: "boolean",
    list: "array",
  },
  summaryItemStructure: {
    category: "string",
    details: "string",
    isImportant: "boolean",
  },
  planStructure: {
    planId: "string",
    name: "string",
    description: "string",
    duration: "string",
    level: "string",
    planType: "string",
    disciplines: "array",
    rationale: "string",
    phases: "array",
    exampleSessions: "array",
  },
};

// Test messages that should trigger specific data updates
const TEST_SEQUENCE = [
  {
    message: "Hi, I want to start my fitness journey",
    expectation: "Should trigger initial greeting and sport suggestions",
  },
  {
    message: "Running, Strength Training",
    expectation:
      "Should update selectedSports with 2 sports and remove suggestions",
  },
  {
    message:
      "I want to run a 10k race in under 50 minutes and build upper body strength",
    expectation: "Should extract goals and update goals.list",
  },
  {
    message: "I'm intermediate in running but beginner in strength training",
    expectation: "Should add summary item about experience level",
  },
  {
    message: "I can work out 4 times per week, about 60 minutes each session",
    expectation: "Should add summary item about time commitment",
  },
  {
    message:
      "I have access to a gym with weights and can run outdoors on trails",
    expectation: "Should add summary item about equipment access",
  },
  {
    message:
      "Running is my priority for the summer season, strength training for winter prep",
    expectation:
      "Should add summary item about priorities and trigger completion check",
  },
  {
    message: "Yes, that sounds perfect. Let's create the plan!",
    expectation: "Should trigger plan generation and complete onboarding",
  },
];

class DataValidator {
  constructor() {
    this.results = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      errors: [],
      dataCaptures: [],
    };
  }

  validateType(value, expectedType, path = "") {
    if (expectedType.includes("|")) {
      const types = expectedType.split("|");
      return types.some((type) => this.validateSingleType(value, type.trim()));
    }
    return this.validateSingleType(value, expectedType);
  }

  validateSingleType(value, expectedType) {
    switch (expectedType) {
      case "string":
        return typeof value === "string";
      case "boolean":
        return typeof value === "boolean";
      case "array":
        return Array.isArray(value);
      case "object":
        return (
          typeof value === "object" && value !== null && !Array.isArray(value)
        );
      case "null":
        return value === null;
      default:
        return false;
    }
  }

  validateStructure(data, expectedStructure, path = "") {
    const results = [];

    for (const [key, expectedType] of Object.entries(expectedStructure)) {
      const fullPath = path ? `${path}.${key}` : key;
      const value = data[key];

      if (typeof expectedType === "object") {
        // Nested structure
        if (value && typeof value === "object") {
          results.push(
            ...this.validateStructure(value, expectedType, fullPath),
          );
        } else {
          results.push({
            path: fullPath,
            expected: "object",
            actual: typeof value,
            valid: false,
          });
        }
      } else {
        // Simple type check
        const isValid = this.validateType(value, expectedType);
        results.push({
          path: fullPath,
          expected: expectedType,
          actual: typeof value,
          value: value,
          valid: isValid,
        });
      }
    }

    return results;
  }

  recordTest(testName, passed, details = {}) {
    this.results.totalTests++;
    if (passed) {
      this.results.passedTests++;
      console.log(`✅ ${testName}`);
    } else {
      this.results.failedTests++;
      console.log(`❌ ${testName}`);
      this.results.errors.push({ testName, details });
    }
  }

  captureData(stepName, data) {
    this.results.dataCaptures.push({
      step: stepName,
      timestamp: new Date().toISOString(),
      data: JSON.parse(JSON.stringify(data)), // Deep copy
    });
  }
}

async function testDataTransformation() {
  console.log("🧪 ONBOARDING DATA VALIDATION TEST");
  console.log("=" * 50);
  console.log(`User ID: ${TEST_CONFIG.userId}`);
  console.log(`Thread ID: ${TEST_CONFIG.threadId}`);
  console.log("");

  const validator = new DataValidator();
  let lastSidebarData = null;

  for (let i = 0; i < TEST_SEQUENCE.length; i++) {
    const { message, expectation } = TEST_SEQUENCE[i];
    console.log(`\n📨 Step ${i + 1}: "${message}"`);
    console.log(`Expected: ${expectation}`);

    try {
      const response = await fetch(`${FRONTEND_URL}/api/onboarding-python`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          threadId: TEST_CONFIG.threadId,
          userId: TEST_CONFIG.userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body");
      }

      const decoder = new TextDecoder();
      let eventCount = 0;
      let sidebarUpdatesCount = 0;
      let currentSidebarData = null;

      console.log("🌊 Processing stream...");

      while (eventCount < TEST_CONFIG.maxEvents) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        if (!chunk) continue;

        eventCount++;

        // Parse sidebar_update events
        if (chunk.includes('"type": "sidebar_update"')) {
          try {
            const dataMatches = chunk.matchAll(/data: ({.*?})\n/g);
            for (const match of dataMatches) {
              const eventData = JSON.parse(match[1]);
              if (
                eventData.type === "sidebar_update" &&
                eventData.sidebarData
              ) {
                sidebarUpdatesCount++;
                currentSidebarData = eventData.sidebarData;

                console.log(
                  `📋 Sidebar Update ${sidebarUpdatesCount} received`,
                );

                // Validate sidebar data structure
                const structureResults = validator.validateStructure(
                  currentSidebarData,
                  EXPECTED_STRUCTURES.sidebarData,
                );

                const structureValid = structureResults.every((r) => r.valid);
                validator.recordTest(
                  `Step ${i + 1}: Sidebar structure validation`,
                  structureValid,
                  { structureResults, data: currentSidebarData },
                );

                // Validate goals structure if present
                if (currentSidebarData.goals) {
                  const goalsResults = validator.validateStructure(
                    currentSidebarData.goals,
                    EXPECTED_STRUCTURES.goalStructure,
                  );
                  const goalsValid = goalsResults.every((r) => r.valid);
                  validator.recordTest(
                    `Step ${i + 1}: Goals structure validation`,
                    goalsValid,
                    { goalsResults, goals: currentSidebarData.goals },
                  );
                }

                // Validate summary items structure if present
                if (
                  currentSidebarData.summaryItems &&
                  currentSidebarData.summaryItems.length > 0
                ) {
                  const summaryValid = currentSidebarData.summaryItems.every(
                    (item) => {
                      const itemResults = validator.validateStructure(
                        item,
                        EXPECTED_STRUCTURES.summaryItemStructure,
                      );
                      return itemResults.every((r) => r.valid);
                    },
                  );
                  validator.recordTest(
                    `Step ${i + 1}: Summary items structure validation`,
                    summaryValid,
                    { summaryItems: currentSidebarData.summaryItems },
                  );
                }

                // Validate plan structure if present
                if (currentSidebarData.generatedPlan) {
                  const planResults = validator.validateStructure(
                    currentSidebarData.generatedPlan,
                    EXPECTED_STRUCTURES.planStructure,
                  );
                  const planValid = planResults.every((r) => r.valid);
                  validator.recordTest(
                    `Step ${i + 1}: Generated plan structure validation`,
                    planValid,
                    { planResults, plan: currentSidebarData.generatedPlan },
                  );
                }

                validator.captureData(`step_${i + 1}`, currentSidebarData);
              }
            }
          } catch (parseError) {
            console.error("❌ Error parsing sidebar update:", parseError);
            validator.recordTest(`Step ${i + 1}: Sidebar parsing`, false, {
              error: parseError.message,
              chunk,
            });
          }
        }

        // Stop if we detect completion
        if (
          chunk.includes('"type": "complete"') ||
          chunk.includes("event: complete")
        ) {
          console.log("🏁 Detected completion event");
          break;
        }
      }

      // Test specific expectations for this step
      if (currentSidebarData) {
        switch (i) {
          case 0: // Initial greeting
            validator.recordTest(
              `Step ${i + 1}: Sport suggestions provided`,
              currentSidebarData.sportSuggestions &&
                currentSidebarData.sportSuggestions.length > 0,
            );
            break;
          case 1: // Sport selection
            validator.recordTest(
              `Step ${i + 1}: Sports selected correctly`,
              currentSidebarData.selectedSports &&
                currentSidebarData.selectedSports.includes("Running") &&
                currentSidebarData.selectedSports.includes("Strength Training"),
            );
            validator.recordTest(
              `Step ${i + 1}: Sport suggestions removed after selection`,
              !currentSidebarData.sportSuggestions,
            );
            break;
          case 2: // Goals extraction
            validator.recordTest(
              `Step ${i + 1}: Goals extracted and exist`,
              currentSidebarData.goals &&
                currentSidebarData.goals.exists &&
                currentSidebarData.goals.list.length > 0,
            );
            break;
          case 7: // Plan generation
            validator.recordTest(
              `Step ${i + 1}: Plan generated`,
              currentSidebarData.generatedPlan &&
                currentSidebarData.generatedPlan.planId,
            );
            break;
        }

        lastSidebarData = currentSidebarData;
      }

      console.log(
        `✅ Step ${i + 1} completed: ${sidebarUpdatesCount} sidebar updates received`,
      );
    } catch (error) {
      console.error(`❌ Step ${i + 1} failed:`, error.message);
      validator.recordTest(`Step ${i + 1}: Execution`, false, {
        error: error.message,
      });
    }

    // Small delay between steps
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  return validator.results;
}

async function testDirectPythonBackend() {
  console.log("\n🐍 Testing Direct Python Backend Response...");

  try {
    const response = await fetch(`${PYTHON_BACKEND_URL}/api/onboarding`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message: "Hi! I want to test the python backend",
        user_id: TEST_CONFIG.userId,
        thread_id: TEST_CONFIG.threadId + "_direct",
      }),
    });

    if (!response.ok) {
      throw new Error(`Python backend error: ${response.status}`);
    }

    console.log("✅ Python backend is responding correctly");
    return true;
  } catch (error) {
    console.log("❌ Python backend test failed:", error.message);
    return false;
  }
}

async function runCompleteValidation() {
  console.log("🚀 Starting Complete Onboarding Data Validation\n");

  // Test 1: Direct Python backend
  const pythonBackendWorking = await testDirectPythonBackend();

  // Test 2: Full data transformation flow
  const validationResults = await testDataTransformation();

  // Print comprehensive results
  console.log("\n📊 VALIDATION RESULTS SUMMARY");
  console.log("=" * 40);
  console.log(
    `Python Backend: ${pythonBackendWorking ? "✅ Working" : "❌ Failed"}`,
  );
  console.log(`Total Tests: ${validationResults.totalTests}`);
  console.log(`Passed: ${validationResults.passedTests}`);
  console.log(`Failed: ${validationResults.failedTests}`);
  console.log(
    `Success Rate: ${((validationResults.passedTests / validationResults.totalTests) * 100).toFixed(1)}%`,
  );

  if (validationResults.failedTests > 0) {
    console.log("\n❌ FAILED TESTS:");
    validationResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.testName}`);
      if (error.details.error) {
        console.log(`   Error: ${error.details.error}`);
      }
      if (error.details.structureResults) {
        const invalid = error.details.structureResults.filter((r) => !r.valid);
        invalid.forEach((result) => {
          console.log(
            `   ${result.path}: expected ${result.expected}, got ${result.actual}`,
          );
        });
      }
    });
  }

  // Print data evolution
  console.log("\n📈 DATA EVOLUTION:");
  validationResults.dataCaptures.forEach((capture, index) => {
    console.log(`${index + 1}. ${capture.step}:`);
    console.log(`   Goals: ${capture.data.goals?.list?.length || 0} items`);
    console.log(`   Summary: ${capture.data.summaryItems?.length || 0} items`);
    console.log(
      `   Sports: ${capture.data.selectedSports?.length || 0} selected`,
    );
    console.log(
      `   Plan: ${capture.data.generatedPlan ? "Generated" : "Not yet"}`,
    );
  });

  const overallSuccess =
    pythonBackendWorking &&
    validationResults.failedTests === 0 &&
    validationResults.totalTests > 0;

  console.log(
    `\n🎯 OVERALL RESULT: ${overallSuccess ? "✅ ALL VALIDATIONS PASSED" : "❌ VALIDATION ISSUES DETECTED"}`,
  );

  return overallSuccess;
}

// Run if executed directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  runCompleteValidation()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error("💥 Validation failed:", error);
      process.exit(1);
    });
}

export { runCompleteValidation, testDataTransformation, DataValidator };
