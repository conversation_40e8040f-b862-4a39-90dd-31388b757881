/**
 * Test for Onboarding Sidebar Data Integration
 *
 * This test verifies that:
 * 1. The Python backend sends sidebar_update events
 * 2. The proxy transforms the data correctly
 * 3. The frontend receives properly formatted sidebar data
 * 4. Goals, summary items, and selected sports are displayed
 */

const PYTHON_BACKEND_URL = "http://localhost:8000";
const FRONTEND_PROXY_URL = "http://localhost:3000";

console.log("🧪 SIDEBAR INTEGRATION TEST: Starting test...");

async function testSidebarIntegration() {
  const testUserId = "sidebar-test-" + Date.now();
  const threadId = `sidebar-test-thread-${testUserId}`;

  console.log(
    `📋 SIDEBAR TEST: Using userId=${testUserId}, threadId=${threadId}`,
  );

  // Test messages that should trigger sidebar updates
  const testMessages = [
    "Hello, I want to start my fitness journey",
    "I want to improve my running and build strength",
    "I'm a beginner and can work out 3 times per week",
    "I have access to a gym with weights and cardio equipment",
  ];

  let sidebarUpdatesReceived = 0;
  let goalsDetected = false;
  let summaryItemsDetected = false;
  let sportsDetected = false;

  for (let i = 0; i < testMessages.length; i++) {
    const message = testMessages[i];
    console.log(`\n📨 SIDEBAR TEST: Sending message ${i + 1}: "${message}"`);

    try {
      // Use the frontend proxy (which transforms the data)
      const response = await fetch(
        `${FRONTEND_PROXY_URL}/api/onboarding-python`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            message: message,
            threadId: threadId,
            userId: testUserId,
          }),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body");
      }

      const decoder = new TextDecoder();
      let eventCount = 0;
      let messageComplete = false;

      while (!messageComplete && eventCount < 50) {
        // Limit events to prevent infinite loop
        const { done, value } = await reader.read();

        if (done) {
          console.log(`✅ SIDEBAR TEST: Message ${i + 1} stream completed`);
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        if (!chunk) continue;

        eventCount++;

        // Parse events from the chunk
        const lines = chunk.split("\n");
        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const eventData = JSON.parse(line.substring(6));

              // Check for sidebar updates
              if (eventData.type === "sidebar_update") {
                sidebarUpdatesReceived++;
                console.log(
                  `🔄 SIDEBAR TEST: Received sidebar_update #${sidebarUpdatesReceived}:`,
                  {
                    currentStage: eventData.sidebarData?.currentStage,
                    goalsExists: eventData.sidebarData?.goals?.exists,
                    goalsCount: eventData.sidebarData?.goals?.list?.length || 0,
                    summaryItemsCount:
                      eventData.sidebarData?.summaryItems?.length || 0,
                    selectedSports: eventData.sidebarData?.selectedSports || [],
                  },
                );

                // Check what was detected
                if (
                  eventData.sidebarData?.goals?.exists &&
                  eventData.sidebarData?.goals?.list?.length > 0
                ) {
                  goalsDetected = true;
                  console.log(
                    `🎯 SIDEBAR TEST: Goals detected:`,
                    eventData.sidebarData.goals.list,
                  );
                }

                if (eventData.sidebarData?.summaryItems?.length > 0) {
                  summaryItemsDetected = true;
                  console.log(
                    `📝 SIDEBAR TEST: Summary items detected:`,
                    eventData.sidebarData.summaryItems.map(
                      (item) => `${item.category}: ${item.details}`,
                    ),
                  );
                }

                if (eventData.sidebarData?.selectedSports?.length > 0) {
                  sportsDetected = true;
                  console.log(
                    `🏃 SIDEBAR TEST: Sports detected:`,
                    eventData.sidebarData.selectedSports,
                  );
                }
              }

              // Check for completion
              if (eventData.type === "complete") {
                messageComplete = true;
                console.log(`✅ SIDEBAR TEST: Message ${i + 1} completed`);
                break;
              }
            } catch (e) {
              // Ignore JSON parse errors for non-JSON lines
            }
          }
        }

        // Prevent infinite loops
        if (eventCount >= 50) {
          console.log(
            `⚠️ SIDEBAR TEST: Message ${i + 1} exceeded event limit, stopping`,
          );
          break;
        }
      }

      reader.releaseLock();
    } catch (error) {
      console.error(`❌ SIDEBAR TEST: Error in message ${i + 1}:`, error);
    }

    // Small delay between messages
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  // Test Results Summary
  console.log("\n📊 SIDEBAR INTEGRATION TEST RESULTS:");
  console.log("=====================================");
  console.log(`📋 Sidebar updates received: ${sidebarUpdatesReceived}`);
  console.log(`🎯 Goals detected: ${goalsDetected ? "✅ YES" : "❌ NO"}`);
  console.log(
    `📝 Summary items detected: ${summaryItemsDetected ? "✅ YES" : "❌ NO"}`,
  );
  console.log(`🏃 Sports detected: ${sportsDetected ? "✅ YES" : "❌ NO"}`);

  // Overall test result
  const testPassed =
    sidebarUpdatesReceived > 0 && (goalsDetected || summaryItemsDetected);

  if (testPassed) {
    console.log("\n🎉 SIDEBAR INTEGRATION TEST: PASSED!");
    console.log("✅ Sidebar data is being properly collected and transformed");
  } else {
    console.log("\n❌ SIDEBAR INTEGRATION TEST: FAILED!");
    console.log("❌ Sidebar data integration is not working properly");

    if (sidebarUpdatesReceived === 0) {
      console.log("💡 Issue: No sidebar_update events received from backend");
    } else if (!goalsDetected && !summaryItemsDetected) {
      console.log(
        "💡 Issue: Sidebar updates received but no meaningful data extracted",
      );
    }
  }

  return {
    passed: testPassed,
    sidebarUpdatesReceived,
    goalsDetected,
    summaryItemsDetected,
    sportsDetected,
  };
}

// Run the test
testSidebarIntegration()
  .then((results) => {
    console.log("\n🏁 SIDEBAR TEST: Test completed");
    process.exit(results.passed ? 0 : 1);
  })
  .catch((error) => {
    console.error("💥 SIDEBAR TEST: Test failed with error:", error);
    process.exit(1);
  });
