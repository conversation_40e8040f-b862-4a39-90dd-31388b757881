#!/usr/bin/env python3
"""
Direct n8n Cloud Webhook Test Script

Tests the actual n8n Cloud webhook URLs provided by the user.
"""

import requests
import json
import time
from datetime import datetime

# Your actual n8n Cloud webhook URLs
N8N_WEBHOOK_TEST_URL = (
    "https://athlea.app.n8n.cloud/webhook-test/0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef"
)
N8N_WEBHOOK_PROD_URL = (
    "https://athlea.app.n8n.cloud/webhook/0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef"
)


def test_n8n_cloud_webhook():
    """Test both n8n Cloud webhook URLs with sample coaching data."""
    print("🌤️ Testing n8n Cloud Webhook Integration")
    print("=" * 60)

    # Test both URLs
    webhooks_to_test = [
        {"name": "Test Webhook", "url": N8N_WEBHOOK_TEST_URL},
        {"name": "Production Webhook", "url": N8N_WEBHOOK_PROD_URL},
    ]

    # Sample coaching request data
    test_data = {
        "message": "I want to start strength training as a beginner. What should I do?",
        "user_id": "test_user_123",
        "coach_type": "strength",
        "user_email": "<EMAIL>",
        "thread_id": f"thread-test-{int(time.time())}",
        "session_data": {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "source": "python_test_script",
        },
    }

    print("📤 Test data to send:")
    print(json.dumps(test_data, indent=2))
    print("")

    for webhook in webhooks_to_test:
        print(f"🔗 Testing {webhook['name']}")
        print(f"URL: {webhook['url']}")
        print("-" * 50)

        try:
            # Send POST request to webhook
            response = requests.post(
                webhook["url"],
                json=test_data,
                headers={
                    "Content-Type": "application/json",
                    "User-Agent": "LangGraph-n8n-Integration-Test",
                },
                timeout=30,
            )

            print(f"📥 Response Status: {response.status_code}")

            if response.status_code == 200:
                print(f"✅ SUCCESS! {webhook['name']} responded successfully!")

                try:
                    response_data = response.json()
                    print("📋 Response Data:")
                    print(json.dumps(response_data, indent=2))
                except json.JSONDecodeError:
                    print("📋 Response Text:")
                    print(response.text)

            elif response.status_code == 404:
                print("❌ Webhook not found (404)")
                if "webhook-test" in webhook["url"]:
                    print("💡 Make sure you clicked 'Listen for Test Event' in n8n")
                else:
                    print("💡 Make sure your workflow is ACTIVATED in n8n Cloud")

            elif response.status_code == 500:
                print("❌ Server error (500)")
                print("💡 There might be an error in your n8n workflow")
                print("💡 Check the workflow execution logs in n8n Cloud")

            else:
                print(f"⚠️  Unexpected status code: {response.status_code}")
                print("Response text:", response.text[:200])

        except requests.exceptions.Timeout:
            print("❌ Request timed out after 30 seconds")

        except requests.exceptions.ConnectionError:
            print("❌ Connection error")

        except Exception as e:
            print(f"❌ Unexpected error: {e}")

        print("")  # Space between tests


def test_webhook_variations():
    """Test different types of requests to the webhook."""
    print("\n🔄 Testing Webhook Variations")
    print("=" * 40)

    variations = [
        {
            "name": "Minimal Request",
            "data": {"message": "Hello from test", "user_id": "test_minimal"},
        },
        {
            "name": "Full Coaching Request",
            "data": {
                "message": "I need a running plan for marathon training",
                "user_id": "test_full",
                "coach_type": "running",
                "user_email": "<EMAIL>",
                "session_data": {"goal": "marathon", "experience": "intermediate"},
            },
        },
    ]

    for i, variation in enumerate(variations, 1):
        print(f"\n{i}. Testing {variation['name']}")
        print("-" * 30)

        try:
            response = requests.post(
                N8N_WEBHOOK_TEST_URL, json=variation["data"], timeout=15
            )

            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Success")
            else:
                print(f"   ❌ Failed: {response.text[:100]}...")

        except Exception as e:
            print(f"   ❌ Error: {e}")


if __name__ == "__main__":
    print("🚀 n8n Cloud Integration Test")
    print("Testing with actual webhook URLs from athlea.app.n8n.cloud")
    print("")

    # Main webhook test
    test_n8n_cloud_webhook()

    # Additional variations
    test_webhook_variations()

    print("\n🎯 Next Steps:")
    print("1. If successful, check your n8n Cloud workflow execution logs")
    print("2. Verify the workflow processed the data correctly")
    print("3. Test the complete end-to-end flow")
    print("4. Set up your actual LangGraph endpoints in the workflow")
