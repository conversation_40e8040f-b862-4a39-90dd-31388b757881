#!/usr/bin/env python3
"""
LangGraph ↔ n8n Integration Test Script

This script tests the integration between your LangGraph coaching workflows
and n8n automation by making requests to both systems.
"""

import asyncio
import json
import time
from typing import Dict, Any
import aiohttp
import requests
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()


class IntegrationTester:
    def __init__(self):
        self.langgraph_url = os.getenv("LANGGRAPH_API_URL", "http://localhost:8001")
        self.n8n_url = os.getenv("N8N_API_URL", "https://athlea.app.n8n.cloud/api/v1")
        self.n8n_api_key = os.getenv("N8N_API_KEY")
        self.n8n_webhook_url = "https://athlea.app.n8n.cloud/webhook-test/0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef"

    def test_environment_setup(self):
        """Test that environment variables are properly configured."""
        print("🔧 Testing Environment Setup")
        print("=" * 50)

        print(f"✅ LangGraph API URL: {self.langgraph_url}")
        print(f"✅ n8n API URL: {self.n8n_url}")
        print(f"✅ n8n API Key configured: {bool(self.n8n_api_key)}")

        if self.n8n_api_key:
            print(f"   API Key preview: {self.n8n_api_key[:20]}...")

        return True

    async def test_langgraph_api_health(self):
        """Test if the LangGraph API wrapper is running and healthy."""
        print("\n🚀 Testing LangGraph API Health")
        print("=" * 50)

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.langgraph_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ LangGraph API is healthy!")
                        print(f"   Status: {data.get('status')}")
                        print(f"   Service: {data.get('service')}")
                        print(f"   Timestamp: {data.get('timestamp')}")
                        return True
                    else:
                        print(
                            f"❌ LangGraph API health check failed: {response.status}"
                        )
                        return False
        except Exception as e:
            print(f"❌ Failed to connect to LangGraph API: {e}")
            return False

    async def test_n8n_connectivity(self):
        """Test if n8n API is accessible with the provided credentials."""
        print("\n🔗 Testing n8n API Connectivity")
        print("=" * 50)

        if not self.n8n_api_key:
            print("❌ No n8n API key provided, skipping n8n tests")
            return False

        try:
            headers = {"X-N8N-API-KEY": self.n8n_api_key}
            async with aiohttp.ClientSession(headers=headers) as session:
                async with session.get(f"{self.n8n_url}/workflows") as response:
                    if response.status == 200:
                        workflows = await response.json()
                        print(f"✅ n8n API connection successful!")
                        print(f"   Found {len(workflows.get('data', []))} workflows")

                        # List some workflows if they exist
                        if workflows.get("data"):
                            print("   Available workflows:")
                            for wf in workflows["data"][:3]:  # Show first 3
                                print(
                                    f"     - {wf.get('name', 'Unnamed')} (ID: {wf.get('id')})"
                                )

                        return True
                    else:
                        print(f"❌ n8n API connection failed: {response.status}")
                        response_text = await response.text()
                        print(f"   Error: {response_text}")
                        return False
        except Exception as e:
            print(f"❌ Failed to connect to n8n API: {e}")
            return False

    async def test_coaching_execution(self):
        """Test executing a coaching workflow through the LangGraph API."""
        print("\n🎯 Testing Coaching Workflow Execution")
        print("=" * 50)

        test_request = {
            "message": "I want to start strength training as a beginner. What should I do?",
            "user_id": "test_user_123",
            "coach_type": "strength",
            "session_data": {"test": True},
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.langgraph_url}/coaching/execute", json=test_request
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ Coaching workflow executed successfully!")
                        print(
                            f"   Execution time: {result.get('execution_time', 0):.2f}s"
                        )
                        print(
                            f"   Tools used: {', '.join(result.get('tools_used', []))}"
                        )
                        print(f"   Thread ID: {result.get('thread_id')}")
                        print(
                            f"   Response preview: {result.get('response', '')[:100]}..."
                        )

                        return result
                    else:
                        print(f"❌ Coaching execution failed: {response.status}")
                        error_text = await response.text()
                        print(f"   Error: {error_text}")
                        return None
        except Exception as e:
            print(f"❌ Failed to execute coaching workflow: {e}")
            return None

    async def test_streaming_coaching(self):
        """Test the streaming coaching endpoint."""
        print("\n📡 Testing Streaming Coaching")
        print("=" * 50)

        test_request = {
            "message": "How should I structure my weekly running training?",
            "user_id": "test_user_123",
            "coach_type": "running",
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.langgraph_url}/coaching/stream", json=test_request
                ) as response:
                    if response.status == 200:
                        print("✅ Streaming coaching started!")
                        print("   Stream events:")

                        async for line in response.content:
                            line_str = line.decode("utf-8").strip()
                            if line_str.startswith("data: "):
                                try:
                                    event_data = json.loads(
                                        line_str[6:]
                                    )  # Remove 'data: ' prefix
                                    event_type = event_data.get("type", "unknown")
                                    message = event_data.get("message", "")
                                    print(f"     [{event_type}] {message}")

                                    if event_type == "complete":
                                        break
                                except json.JSONDecodeError:
                                    continue

                        print("✅ Streaming completed successfully!")
                        return True
                    else:
                        print(f"❌ Streaming failed: {response.status}")
                        return False
        except Exception as e:
            print(f"❌ Failed to test streaming: {e}")
            return False

    def test_coaches_endpoint(self):
        """Test the coaches listing endpoint."""
        print("\n👥 Testing Coaches Endpoint")
        print("=" * 50)

        try:
            response = requests.get(f"{self.langgraph_url}/coaching/coaches")
            if response.status_code == 200:
                coaches = response.json()
                print("✅ Coaches endpoint working!")
                print("   Available coaches:")
                for coach in coaches.get("coaches", []):
                    print(
                        f"     - {coach['name']} ({coach['id']}): {coach['description']}"
                    )
                return True
            else:
                print(f"❌ Coaches endpoint failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Failed to test coaches endpoint: {e}")
            return False

    async def test_n8n_workflow_trigger(self):
        """Test triggering an n8n workflow from the LangGraph API."""
        print("\n⚙️ Testing n8n Workflow Trigger")
        print("=" * 50)

        test_request = {
            "workflow_id": "test-workflow",
            "user_id": "test_user_123",
            "input_data": {
                "test": True,
                "coaching_result": "Sample coaching result",
                "timestamp": time.time(),
            },
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.langgraph_url}/n8n/trigger", json=test_request
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print("✅ n8n workflow trigger test successful!")
                        print(f"   Execution ID: {result.get('execution_id')}")
                        print(f"   Status: {result.get('status')}")
                        return True
                    else:
                        print(f"❌ n8n workflow trigger failed: {response.status}")
                        error_text = await response.text()
                        print(f"   Error: {error_text}")
                        return False
        except Exception as e:
            print(f"❌ Failed to test n8n workflow trigger: {e}")
            return False

    async def test_n8n_cloud_webhook_direct(self):
        """Test the actual n8n Cloud webhook directly."""
        print("\n🌤️ Testing n8n Cloud Webhook Direct")
        print("=" * 50)

        test_data = {
            "message": "Test from integration script - I need fitness guidance",
            "user_id": "integration_test_user",
            "coach_type": "general",
            "user_email": "<EMAIL>",
            "session_data": {"source": "integration_test"},
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.n8n_webhook_url,
                    json=test_data,
                    headers={"Content-Type": "application/json"},
                ) as response:
                    if response.status == 200:
                        result = await response.text()
                        print("✅ n8n Cloud webhook test successful!")
                        print(f"   Response: {result}")
                        print(
                            "💡 Check your n8n Cloud executions to see the workflow run"
                        )
                        return True
                    else:
                        print(f"❌ n8n Cloud webhook failed: {response.status}")
                        error_text = await response.text()
                        print(f"   Error: {error_text}")
                        return False
        except Exception as e:
            print(f"❌ Failed to test n8n Cloud webhook: {e}")
            return False

    async def run_all_tests(self):
        """Run all integration tests."""
        print("🚀 LangGraph ↔ n8n Integration Test Suite")
        print("=" * 60)

        test_results = []

        # Environment setup
        test_results.append(("Environment Setup", self.test_environment_setup()))

        # LangGraph API tests
        test_results.append(
            ("LangGraph Health", await self.test_langgraph_api_health())
        )
        test_results.append(("Coaches Endpoint", self.test_coaches_endpoint()))
        test_results.append(
            ("Coaching Execution", bool(await self.test_coaching_execution()))
        )
        test_results.append(
            ("Streaming Coaching", await self.test_streaming_coaching())
        )
        test_results.append(("n8n Trigger", await self.test_n8n_workflow_trigger()))

        # n8n API tests (if configured)
        test_results.append(("n8n Connectivity", await self.test_n8n_connectivity()))
        test_results.append(
            ("n8n Cloud Webhook", await self.test_n8n_cloud_webhook_direct())
        )

        # Summary
        print("\n📊 Test Results Summary")
        print("=" * 60)

        passed = 0
        total = len(test_results)

        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<25} {status}")
            if result:
                passed += 1

        print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

        if passed == total:
            print("\n🎉 All tests passed! Your integration is ready to use!")
        elif passed >= total * 0.8:
            print("\n⚠️  Most tests passed. Check failed tests above.")
        else:
            print("\n❌ Multiple tests failed. Please check your configuration.")

        return passed == total


async def main():
    """Main test execution."""
    tester = IntegrationTester()
    success = await tester.run_all_tests()

    if success:
        print("\n🚀 Next Steps:")
        print(
            "1. Import the n8n workflow template (n8n_coaching_workflow_template.json)"
        )
        print(
            "2. Configure your actual LangGraph imports in n8n_langgraph_integration.py"
        )
        print("3. Test with real coaching workflows!")
        print("4. Set up monitoring and production deployment")
    else:
        print("\n🔧 Troubleshooting:")
        print("1. Make sure both LangGraph API and n8n are running")
        print("2. Check your .env file has correct URLs and API keys")
        print("3. Verify network connectivity between services")
        print("4. Check logs for detailed error messages")

    return success


if __name__ == "__main__":
    asyncio.run(main())
