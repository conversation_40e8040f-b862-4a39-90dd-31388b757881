/**
 * Onboarding Resume Functionality Test
 *
 * Tests the new resume functionality including:
 * 1. Status checking API calls
 * 2. Reset functionality
 * 3. Resume flag handling
 * 4. Development controls
 */

const FRONTEND_URL = "http://localhost:3000";
const PYTHON_BACKEND_URL = "http://localhost:8000";

// Test configuration
const TEST_CONFIG = {
  userId: `resume-test-${Date.now()}`,
  threadId: `resume-thread-${Date.now()}`,
  timeout: 30000,
};

class OnboardingResumeTest {
  constructor() {
    this.userId = TEST_CONFIG.userId;
    this.threadId = TEST_CONFIG.threadId;
    this.testResults = {
      statusCheck: false,
      resetFunctionality: false,
      resumeFlag: false,
      developmentControls: false,
    };
  }

  log(message, data = null) {
    console.log(`[RESUME TEST] ${message}`);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }

  async testStatusCheckAPI() {
    this.log("🔍 Testing onboarding status check API...");

    try {
      // First check - should return 'not_started'
      const initialResponse = await fetch(
        `${PYTHON_BACKEND_URL}/api/onboarding/status/${this.userId}?thread_id=${this.threadId}`,
      );

      if (!initialResponse.ok) {
        throw new Error(`Status check failed: ${initialResponse.status}`);
      }

      const initialStatus = await initialResponse.json();
      this.log("Initial status response:", initialStatus);

      if (initialStatus.status === "not_started") {
        this.log("✅ Status check API working correctly for new user");
        this.testResults.statusCheck = true;
        return true;
      } else {
        this.log("❌ Expected 'not_started' status for new user");
        return false;
      }
    } catch (error) {
      this.log(`❌ Status check API failed: ${error.message}`);
      return false;
    }
  }

  async testResetFunctionality() {
    this.log("🔄 Testing onboarding reset functionality...");

    try {
      const resetResponse = await fetch(
        `${PYTHON_BACKEND_URL}/api/onboarding/reset/${this.userId}?thread_id=${this.threadId}`,
        { method: "POST" },
      );

      if (!resetResponse.ok) {
        throw new Error(`Reset failed: ${resetResponse.status}`);
      }

      const resetResult = await resetResponse.json();
      this.log("Reset response:", resetResult);

      if (resetResult.status === "reset") {
        this.log("✅ Reset functionality working correctly");
        this.testResults.resetFunctionality = true;
        return true;
      } else {
        this.log("❌ Reset did not return expected status");
        return false;
      }
    } catch (error) {
      this.log(`❌ Reset functionality failed: ${error.message}`);
      return false;
    }
  }

  async testResumeFlagHandling() {
    this.log("🚀 Testing resume flag handling in proxy...");

    try {
      // Start an onboarding session first
      this.log("Starting initial onboarding session...");
      const startUrl = `${FRONTEND_URL}/api/onboarding-python?message=Hi I want to start fitness&threadId=${this.threadId}&userId=${this.userId}`;

      // Simulate a brief session (let it run for 3 seconds then stop)
      const controller = new AbortController();
      setTimeout(() => controller.abort(), 3000);

      try {
        const response = await fetch(startUrl, {
          signal: controller.signal,
          headers: { Accept: "text/event-stream" },
        });
        // We expect this to be aborted, so ignore the error
      } catch (error) {
        if (error.name !== "AbortError") {
          throw error;
        }
      }

      this.log("Initial session started, now testing resume flag...");

      // Now test resume flag
      const resumeUrl = `${FRONTEND_URL}/api/onboarding-python?message=Continue where we left off&threadId=${this.threadId}&userId=${this.userId}&resume=true`;

      const resumeResponse = await fetch(resumeUrl, {
        headers: { Accept: "text/event-stream" },
      });

      if (resumeResponse.ok) {
        this.log("✅ Resume flag handled correctly by proxy");
        this.testResults.resumeFlag = true;

        // Read first few events to confirm
        const reader = resumeResponse.body?.getReader();
        if (reader) {
          const decoder = new TextDecoder();
          let eventCount = 0;

          try {
            while (eventCount < 3) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value);
              this.log(
                `Resume event ${eventCount + 1}:`,
                chunk.substring(0, 200),
              );
              eventCount++;
            }
          } catch (e) {
            // Ignore read errors
          } finally {
            reader.releaseLock();
          }
        }

        return true;
      } else {
        this.log(`❌ Resume request failed: ${resumeResponse.status}`);
        return false;
      }
    } catch (error) {
      this.log(`❌ Resume flag test failed: ${error.message}`);
      return false;
    }
  }

  async testDevelopmentControls() {
    this.log("🛠️ Testing development controls...");

    try {
      // Test URL parameters
      const testUrls = [
        `${FRONTEND_URL}/onboarding/${this.threadId}?reset=true`,
        `${FRONTEND_URL}/onboarding/${this.threadId}?skip_resume=true`,
        `${FRONTEND_URL}/onboarding/${this.threadId}?reset=true&skip_resume=true`,
      ];

      let urlTestsPassed = 0;

      for (const url of testUrls) {
        try {
          const response = await fetch(url, { method: "HEAD" });
          if (response.ok || response.status === 404) {
            // 404 is fine for HEAD request
            urlTestsPassed++;
            this.log(`✅ URL accessible: ${url}`);
          }
        } catch (e) {
          this.log(`⚠️ URL not accessible: ${url}`);
        }
      }

      if (urlTestsPassed === testUrls.length) {
        this.log("✅ Development control URLs are accessible");
        this.testResults.developmentControls = true;
        return true;
      } else {
        this.log(
          `❌ Only ${urlTestsPassed}/${testUrls.length} URLs accessible`,
        );
        return false;
      }
    } catch (error) {
      this.log(`❌ Development controls test failed: ${error.message}`);
      return false;
    }
  }

  async runAllTests() {
    this.log("🧪 Starting Onboarding Resume Functionality Tests");
    this.log(`User ID: ${this.userId}`);
    this.log(`Thread ID: ${this.threadId}`);
    this.log("=" * 60);

    const tests = [
      { name: "Status Check API", fn: () => this.testStatusCheckAPI() },
      { name: "Reset Functionality", fn: () => this.testResetFunctionality() },
      { name: "Resume Flag Handling", fn: () => this.testResumeFlagHandling() },
      {
        name: "Development Controls",
        fn: () => this.testDevelopmentControls(),
      },
    ];

    let passedTests = 0;
    const totalTests = tests.length;

    for (const test of tests) {
      this.log(`\n🔄 Running: ${test.name}`);
      try {
        const result = await test.fn();
        if (result) {
          passedTests++;
          this.log(`✅ ${test.name} - PASSED`);
        } else {
          this.log(`❌ ${test.name} - FAILED`);
        }
      } catch (error) {
        this.log(`❌ ${test.name} - ERROR: ${error.message}`);
      }
    }

    this.log("\n" + "=" * 60);
    this.log("📊 ONBOARDING RESUME TEST RESULTS");
    this.log("=" * 60);
    this.log(`Total Tests: ${totalTests}`);
    this.log(`Passed: ${passedTests}`);
    this.log(`Failed: ${totalTests - passedTests}`);
    this.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (passedTests === totalTests) {
      this.log(
        "🎉 ALL TESTS PASSED - Onboarding resume functionality working correctly!",
      );
    } else {
      this.log("⚠️ Some tests failed - please check implementation");
    }

    this.log("\nDetailed Results:", this.testResults);
    return passedTests === totalTests;
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes("--help") || args.includes("-h")) {
    console.log(`
Onboarding Resume Functionality Test

Usage: node test-onboarding-resume.js [options]

Options:
  --help, -h     Show this help message
  --verbose, -v  Enable verbose logging
  
This test verifies:
✓ Onboarding status check API
✓ Reset functionality  
✓ Resume flag handling in proxy
✓ Development control URLs

Make sure both frontend (port 3000) and Python backend (port 8000) are running.
    `);
    process.exit(0);
  }

  const test = new OnboardingResumeTest();
  test
    .runAllTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error("Test runner error:", error);
      process.exit(1);
    });
}

module.exports = OnboardingResumeTest;
