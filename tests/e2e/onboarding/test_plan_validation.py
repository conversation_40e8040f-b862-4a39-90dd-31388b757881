#!/usr/bin/env python3
"""
Plan Generation Validation Test Suite
Tests that generated plans have correct structure and content
"""

import asyncio
import logging
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage

from athlea_langgraph import (
    create_initial_onboarding_state,
    get_compiled_onboarding_graph,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PlanValidationTester:
    """Test plan generation and validation"""

    def __init__(self):
        self.graph = None
        
    async def setup_graph(self):
        """Initialize the onboarding graph for testing"""
        if not self.graph:
            self.graph = get_compiled_onboarding_graph(
                checkpointer=None,
                enable_mem0=False,
                mem0_use_api=False
            )
        return self.graph

    async def generate_plan_for_scenario(self, scenario_name: str, messages: List[str]) -> Dict[str, Any]:
        """Generate a complete plan for a given scenario"""
        print(f"\n🏗️  GENERATING PLAN FOR: {scenario_name}")
        print("=" * 70)
        
        graph = await self.setup_graph()
        config = {"configurable": {"thread_id": f"plan_test_{scenario_name.lower().replace(' ', '_')}"}}
        
        # Step 1: Complete information gathering
        message_objects = [HumanMessage(content=msg) for msg in messages]
        state = create_initial_onboarding_state("test_user")
        state["messages"] = message_objects
        state["user_input"] = message_objects[-1].content
        
        result = await graph.ainvoke(state, config)
        
        if not result.get('has_enough_info'):
            print("❌ Scenario did not trigger completion - insufficient information")
            return {"success": False, "error": "Insufficient information"}
        
        print("✅ Information gathering completed - proceeding to plan generation")
        
        # Step 2: Confirm plan summary to trigger plan generation
        confirmation_message = HumanMessage(content="Yes, this looks perfect! Please create my training plan.")
        
        # Get current state and add confirmation
        snapshot = await graph.aget_state(config)
        current_state = snapshot.values
        current_state["messages"] = current_state.get("messages", []) + [confirmation_message]
        current_state["user_input"] = confirmation_message.content
        
        # Execute plan generation
        plan_result = await graph.ainvoke(current_state, config)
        
        print(f"📝 Plan generation result:")
        print(f"   Stage: {plan_result.get('onboarding_stage', 'Unknown')}")
        print(f"   Has plan: {bool(plan_result.get('generated_plan'))}")
        
        return {
            "success": True,
            "result": plan_result,
            "plan": plan_result.get('generated_plan'),
            "sidebar_data": plan_result.get('sidebar_data')
        }

    def validate_plan_structure(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that a plan has the correct structure and content"""
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "score": 0,
            "max_score": 0
        }
        
        # Required fields validation
        required_fields = [
            "planId", "name", "description", "duration", "level", 
            "planType", "disciplines", "rationale", "phases", "exampleSessions"
        ]
        
        for field in required_fields:
            validation["max_score"] += 1
            if field in plan and plan[field]:
                validation["score"] += 1
            else:
                validation["errors"].append(f"Missing or empty required field: {field}")
                validation["valid"] = False
        
        # Calculate percentage
        validation["percentage"] = (validation["score"] / validation["max_score"] * 100) if validation["max_score"] > 0 else 0
        
        return validation


# Simple test scenario
PLAN_SCENARIO = {
    "name": "Beginner Runner Plan Validation",
    "messages": [
        "Hi! I want to start training for a 10K race in 6 months. I'm a complete beginner to running.",
        "I can train 3-4 times per week, about 30-45 minutes per session. I prefer morning workouts around 7 AM.",
        "I have access to a gym with treadmills and can run outdoors. No current injuries or health issues.",
        "My main goal is just to finish the 10K without walking. I want to build a running base gradually and safely.",
        "I'm ready to start my training plan!"
    ]
}


async def main():
    """Run plan validation test manually"""
    print("🏗️  PLAN VALIDATION TESTING")
    print("=" * 80)
    
    tester = PlanValidationTester()
    
    try:
        print(f"\n{'='*60}")
        result = await tester.generate_plan_for_scenario(
            PLAN_SCENARIO["name"],
            PLAN_SCENARIO["messages"]
        )
        
        if result["success"] and result["plan"]:
            validation = tester.validate_plan_structure(result["plan"])
            
            print(f"\n📊 PLAN VALIDATION RESULTS:")
            print(f"   📈 Score: {validation['score']}/{validation['max_score']} ({validation['percentage']:.1f}%)")
            print(f"   ✅ Valid Structure: {validation['valid']}")
            print(f"   📋 Plan Name: {result['plan'].get('name', 'N/A')}")
            print(f"   🎯 Plan Type: {result['plan'].get('planType', 'N/A')}")
            print(f"   📅 Duration: {result['plan'].get('duration', 'N/A')}")
            print(f"   📊 Level: {result['plan'].get('level', 'N/A')}")
            print(f"   🏃 Disciplines: {result['plan'].get('disciplines', [])}")
            print(f"   🔄 Phases: {len(result['plan'].get('phases', []))}")
            print(f"   💪 Example Sessions: {len(result['plan'].get('exampleSessions', []))}")
            
            if validation['errors']:
                print(f"   ❌ Errors: {validation['errors']}")
            if validation['warnings']:
                print(f"   ⚠️  Warnings: {validation['warnings']}")
            
            print(f"\n🎯 Plan Quality: {'✅ PASS' if validation['percentage'] >= 70 else '❌ FAIL'}")
            
        else:
            print(f"❌ Failed to generate plan")
            if "error" in result:
                print(f"   Error: {result['error']}")
                
    except Exception as e:
        print(f"❌ Exception during testing: {e}")


if __name__ == "__main__":
    asyncio.run(main())
