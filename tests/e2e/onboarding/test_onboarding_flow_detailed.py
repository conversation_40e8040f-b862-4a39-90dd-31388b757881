#!/usr/bin/env python3
"""
Detailed Onboarding Flow Test
Shows inputs and outputs at every step of the onboarding process
"""

import asyncio
import json
import logging
from datetime import datetime
from langchain_core.messages import HumanMessage

from athlea_langgraph import (
    create_initial_onboarding_state,
    get_compiled_onboarding_graph,
)

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class OnboardingFlowTracker:
    def __init__(self):
        self.step_count = 0
        self.flow_log = []

    def log_step(
        self, step_name, input_data, output_data, state_before=None, state_after=None
    ):
        """Log a step with detailed input/output information"""
        self.step_count += 1

        # Create a detailed log entry
        log_entry = {
            "step_number": self.step_count,
            "step_name": step_name,
            "timestamp": datetime.now().isoformat(),
            "input_summary": self._summarize_data(input_data),
            "output_summary": self._summarize_data(output_data),
            "state_changes": (
                self._compare_states(state_before, state_after)
                if state_before and state_after
                else None
            ),
        }

        self.flow_log.append(log_entry)

        # Print detailed information
        print(f"\n{'='*80}")
        print(f"STEP {self.step_count}: {step_name}")
        print(f"{'='*80}")

        print(f"\n📥 INPUT TO {step_name}:")
        print("-" * 40)
        self._print_data_summary(input_data)

        print(f"\n📤 OUTPUT FROM {step_name}:")
        print("-" * 40)
        self._print_data_summary(output_data)

        if state_before and state_after:
            changes = self._compare_states(state_before, state_after)
            if changes:
                print(f"\n🔄 STATE CHANGES:")
                print("-" * 40)
                for change in changes:
                    print(f"  • {change}")

        print(f"\n{'='*80}")

    def _summarize_data(self, data):
        """Create a summary of the data for logging"""
        if not data:
            return "No data"

        summary = {}

        if isinstance(data, dict):
            for key, value in data.items():
                if key == "messages":
                    summary[key] = f"{len(value)} messages" if value else "No messages"
                    if value:
                        summary["last_message"] = (
                            value[-1].content[:100] + "..."
                            if len(value[-1].content) > 100
                            else value[-1].content
                        )
                elif key == "sidebar_data":
                    if value:
                        summary[key] = {
                            "current_stage": getattr(
                                value,
                                "current_stage",
                                (
                                    value.get("current_stage")
                                    if isinstance(value, dict)
                                    else None
                                ),
                            ),
                            "has_goals": bool(
                                getattr(
                                    value,
                                    "goals",
                                    (
                                        value.get("goals")
                                        if isinstance(value, dict)
                                        else None
                                    ),
                                )
                            ),
                            "summary_items_count": len(
                                getattr(
                                    value,
                                    "summary_items",
                                    (
                                        value.get("summary_items", [])
                                        if isinstance(value, dict)
                                        else []
                                    ),
                                )
                            ),
                            "has_plan": bool(
                                getattr(
                                    value,
                                    "generated_plan",
                                    (
                                        value.get("generated_plan")
                                        if isinstance(value, dict)
                                        else None
                                    ),
                                )
                            ),
                        }
                    else:
                        summary[key] = "No sidebar data"
                elif key == "generated_plan":
                    summary[key] = (
                        f"Plan: {value.get('name', 'Unnamed') if isinstance(value, dict) else getattr(value, 'name', 'Unnamed')}"
                        if value
                        else "No plan"
                    )
                elif key == "completion_feedback":
                    summary[key] = (
                        f"Feedback: {value.get('reasoning', 'No reasoning') if isinstance(value, dict) else 'Invalid feedback'}"
                        if value
                        else "No feedback"
                    )
                else:
                    summary[key] = (
                        str(value)[:100] + "..."
                        if len(str(value)) > 100
                        else str(value)
                    )
        else:
            summary = str(data)[:200] + "..." if len(str(data)) > 200 else str(data)

        return summary

    def _print_data_summary(self, data):
        """Print a nicely formatted summary of the data"""
        if not data:
            print("  No data")
            return

        if isinstance(data, dict):
            for key, value in data.items():
                if key == "messages" and value:
                    print(f"  📨 {key}: {len(value)} messages")
                    for i, msg in enumerate(value[-3:]):  # Show last 3 messages
                        msg_preview = (
                            msg.content[:80] + "..."
                            if len(msg.content) > 80
                            else msg.content
                        )
                        print(f"    [{i+1}] {type(msg).__name__}: {msg_preview}")
                elif key == "sidebar_data" and value:
                    print(f"  📊 {key}:")
                    if hasattr(value, "current_stage"):
                        print(f"    Stage: {value.current_stage}")
                        print(f"    Goals: {bool(value.goals and value.goals.list)}")
                        print(
                            f"    Summary Items: {len(value.summary_items) if value.summary_items else 0}"
                        )
                        print(
                            f"    Selected Sports: {len(value.selected_sports) if value.selected_sports else 0}"
                        )
                        print(f"    Generated Plan: {bool(value.generated_plan)}")
                    elif isinstance(value, dict):
                        print(f"    Stage: {value.get('current_stage', 'Unknown')}")
                        print(f"    Goals: {bool(value.get('goals'))}")
                        print(
                            f"    Summary Items: {len(value.get('summary_items', []))}"
                        )
                        print(
                            f"    Generated Plan: {bool(value.get('generated_plan'))}"
                        )
                elif key == "generated_plan" and value:
                    plan_name = (
                        value.get("name")
                        if isinstance(value, dict)
                        else getattr(value, "name", "Unknown")
                    )
                    print(f"  📋 {key}: {plan_name}")
                elif key == "completion_feedback" and value:
                    print(f"  🔍 {key}:")
                    if isinstance(value, dict):
                        print(f"    Complete: {value.get('is_complete', 'Unknown')}")
                        print(
                            f"    Percentage: {value.get('completion_percentage', 'Unknown')}%"
                        )
                        print(
                            f"    Next Question: {value.get('next_question', 'None')[:60]}..."
                        )
                elif key in ["has_enough_info", "needs_input", "requires_input"]:
                    print(f"  🚩 {key}: {value}")
                elif value is not None:
                    value_str = str(value)
                    if len(value_str) > 100:
                        print(f"  🔸 {key}: {value_str[:100]}...")
                    else:
                        print(f"  🔸 {key}: {value_str}")
        else:
            print(f"  {data}")

    def _compare_states(self, before, after):
        """Compare two states and return a list of changes"""
        changes = []

        # Check for changes in key fields
        key_fields = [
            "onboarding_stage",
            "has_enough_info",
            "needs_input",
            "generated_plan",
            "sidebar_data",
        ]

        for field in key_fields:
            before_val = before.get(field)
            after_val = after.get(field)

            if before_val != after_val:
                changes.append(f"{field}: {before_val} → {after_val}")

        return changes

    def print_summary(self):
        """Print a summary of the entire flow"""
        print(f"\n{'🎯 ONBOARDING FLOW SUMMARY':=^80}")
        print(f"Total Steps: {self.step_count}")

        for entry in self.flow_log:
            print(f"\n{entry['step_number']}. {entry['step_name']}")
            if entry["state_changes"]:
                for change in entry["state_changes"]:
                    print(f"   → {change}")


async def test_detailed_onboarding_flow():
    """Test the complete onboarding flow with detailed logging"""
    print("🚀 DETAILED ONBOARDING FLOW TEST")
    print("=" * 80)

    tracker = OnboardingFlowTracker()

    # Create graph
    graph = get_compiled_onboarding_graph(
        checkpointer=None, enable_mem0=False, mem0_use_api=False
    )

    config = {"configurable": {"thread_id": "detailed_flow_test"}}

    # Simulate a complete onboarding conversation
    test_messages = [
        "Hi! I want to start training for a 10K race. I'm a beginner runner and can train 4 times per week for about 45 minutes each session. I have access to a gym and can run outdoors.",
        "I also want to build strength and improve my overall fitness. I've been sedentary for the past year but used to be moderately active.",
        "I have some flexibility in my schedule - I prefer morning workouts around 7 AM. I don't have any injuries or dietary restrictions.",
        "Yes, that summary looks perfect! Please create my training plan.",
        "Yes, I approve this plan. Let's start the training!",
    ]

    # Initial state
    initial_state = create_initial_onboarding_state("detailed_test_user")
    current_state = initial_state.copy()

    print(f"\n📋 INITIAL STATE:")
    print("-" * 40)
    tracker._print_data_summary(current_state)

    # Process each message
    for i, message in enumerate(test_messages):
        print(f"\n🗣️  USER MESSAGE {i+1}: {message}")

        # Add user message to state
        user_input_state = current_state.copy()
        user_input_state["messages"] = current_state.get("messages", []) + [
            HumanMessage(content=message)
        ]
        user_input_state["user_input"] = message

        # Execute graph step
        try:
            state_before = user_input_state.copy()
            result = await graph.ainvoke(user_input_state, config)
            state_after = result.copy()

            # Log this interaction
            tracker.log_step(
                f"Process Message {i+1}",
                {"user_message": message, "input_state": user_input_state},
                result,
                state_before,
                state_after,
            )

            # Update current state
            current_state = result

            # Check what stage we're in
            current_stage = result.get("onboarding_stage", "unknown")
            if current_stage == "complete":
                print(f"\n✅ ONBOARDING COMPLETED!")
                break

        except Exception as e:
            print(f"\n❌ ERROR in message {i+1}: {e}")
            break

    # Print final summary
    tracker.print_summary()

    # Show final state
    print(f"\n{'📊 FINAL STATE':=^80}")
    tracker._print_data_summary(current_state)

    return current_state


async def main():
    """Main test function"""
    try:
        final_state = await test_detailed_onboarding_flow()

        # Additional analysis
        print(f"\n{'🔍 FINAL ANALYSIS':=^80}")

        if final_state.get("generated_plan"):
            print("✅ Plan Generation: SUCCESS")
        else:
            print("❌ Plan Generation: FAILED")

        if final_state.get("onboarding_stage") == "complete":
            print("✅ Onboarding Completion: SUCCESS")
        else:
            print(
                f"⚠️  Onboarding Stage: {final_state.get('onboarding_stage', 'unknown')}"
            )

        sidebar_data = final_state.get("sidebar_data")
        if sidebar_data:
            if hasattr(sidebar_data, "summary_items"):
                items_count = (
                    len(sidebar_data.summary_items) if sidebar_data.summary_items else 0
                )
            else:
                items_count = (
                    len(sidebar_data.get("summary_items", []))
                    if isinstance(sidebar_data, dict)
                    else 0
                )
            print(f"✅ Summary Items Collected: {items_count}")

            if hasattr(sidebar_data, "goals"):
                goals_count = (
                    len(sidebar_data.goals.list)
                    if sidebar_data.goals and sidebar_data.goals.list
                    else 0
                )
            else:
                goals_data = (
                    sidebar_data.get("goals", {})
                    if isinstance(sidebar_data, dict)
                    else {}
                )
                goals_count = (
                    len(goals_data.get("list", []))
                    if isinstance(goals_data, dict)
                    else 0
                )
            print(f"✅ Goals Extracted: {goals_count}")

        print("\n🎉 DETAILED FLOW TEST COMPLETED!")

    except Exception as e:
        print(f"\n💥 TEST FAILED: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
