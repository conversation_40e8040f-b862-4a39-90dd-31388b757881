#!/usr/bin/env python3
"""
Final extraction test using the actual _get_uploaded_file_data method.
"""

import asyncio
import json
import os
import tempfile
import uuid
from datetime import datetime


async def test_file_extraction():
    """Test the actual file extraction method"""

    print("🧪 Testing File Extraction via _get_uploaded_file_data")
    print("=" * 60)

    try:
        from athlea_langgraph.agents.onboarding.information_gatherer_node import (
            InformationGathererNode,
        )

        # Create test user and directory
        test_user_id = "test_user_final"
        temp_base = tempfile.gettempdir()
        user_dir = os.path.join(temp_base, "athlea_onboarding", test_user_id)
        os.makedirs(user_dir, exist_ok=True)

        print(f"📁 Created test directory: {user_dir}")

        # Create a comprehensive test file
        test_content = """
        ATHLETE PROFILE - Advanced Runner and Strength Athlete
        
        BACKGROUND:
        - Name: Test Athlete
        - Age: 28
        - Height: 5'10"
        - Weight: 155 lbs
        - Experience: 5 years serious training
        
        CURRENT TRAINING:
        - Running: 50 miles per week
        - Strength Training: 3x per week (focusing on compound movements)
        - Cross Training: 1x per week (cycling or swimming)
        
        PERFORMANCE METRICS:
        - Best 5K: 18:45
        - Best 10K: 39:30
        - Half Marathon: 1:25:15
        - Marathon: 3:05:42
        - Bench Press: 1.5x bodyweight (235 lbs)
        - Squat: 2x bodyweight (310 lbs)
        - Deadlift: 2.5x bodyweight (385 lbs)
        
        WEEKLY SCHEDULE:
        Monday: Long run (12-16 miles)
        Tuesday: Strength (Upper body focus)
        Wednesday: Tempo run (6 miles @ 6:30 pace)
        Thursday: Strength (Lower body focus)
        Friday: Easy run (5 miles) + Strength (Accessory work)
        Saturday: Track workout (intervals/speed work)
        Sunday: Recovery run or rest
        
        GOALS FOR 2024:
        - Sub-18 minute 5K
        - Sub-3:00 marathon
        - 400 lb deadlift
        - Stay injury-free
        
        EQUIPMENT:
        - Full home gym with Olympic weights
        - Running watch: Garmin Forerunner 955
        - Heart rate monitor
        - Multiple pairs of running shoes for different workouts
        
        NOTES:
        - History of IT band issues (managed with stretching/strengthening)
        - Prefers morning workouts (5:30 AM)
        - Follows structured periodization
        - Tracks all workouts meticulously
        - Currently in base building phase
        """

        # Create file with UUID filename and metadata
        file_id = str(uuid.uuid4())
        file_path = os.path.join(user_dir, f"{file_id}.txt")
        meta_path = os.path.join(user_dir, f"{file_id}.meta")

        # Write file content
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(test_content)

        # Write metadata
        metadata = {
            "original_filename": "athlete_profile_comprehensive.txt",
            "file_id": file_id,
            "content_type": "text/plain",
            "file_size": len(test_content),
            "upload_date": datetime.now().isoformat(),
            "thread_id": "test_thread_final",
        }

        with open(meta_path, "w") as f:
            json.dump(metadata, f, indent=2)

        print(
            f"✅ Created test file: athlete_profile_comprehensive.txt ({len(test_content)} chars)"
        )

        # Initialize information gatherer and test file extraction
        gatherer = InformationGathererNode()

        print(f"\n🔍 Testing file data extraction...")
        file_data = await gatherer._get_uploaded_file_data(test_user_id)

        # Check results
        print(f"\n📊 Results:")
        print(f"   Has data: {file_data.get('has_data', False)}")

        # Check uploaded documents
        uploaded_docs = file_data.get("uploaded_documents", [])
        print(f"   Uploaded documents: {len(uploaded_docs)}")

        for doc in uploaded_docs:
            print(
                f"     - {doc.get('filename', 'Unknown')} ({doc.get('file_type', 'Unknown type')})"
            )

        # Check analysis results
        key_insights = file_data.get("key_insights", {})
        print(f"   Key insights found: {len(key_insights)}")

        for key, value in key_insights.items():
            print(f"     - {key}: {value}")

        # Check sports detection
        sports_detected = file_data.get("sports_detected", [])
        print(f"   Sports detected: {sports_detected}")

        # Check other analysis fields
        workout_routine = file_data.get("workout_routine", "")
        print(f"   Workout routine info: {len(workout_routine)} characters")

        files_summary = file_data.get("files_summary", "")
        print(f"   Files summary: {files_summary}")

        # Evaluate success
        success_criteria = [
            file_data.get("has_data", False),
            len(uploaded_docs) > 0,
            len(key_insights) > 0,
            len(sports_detected) > 0,
            len(workout_routine) > 0,
        ]

        passed_criteria = sum(success_criteria)
        total_criteria = len(success_criteria)

        print(f"\n✅ Success criteria: {passed_criteria}/{total_criteria}")
        print(f"   ✅ Has data: {file_data.get('has_data', False)}")
        print(f"   ✅ Documents found: {len(uploaded_docs) > 0}")
        print(f"   ✅ Key insights extracted: {len(key_insights) > 0}")
        print(f"   ✅ Sports detected: {len(sports_detected) > 0}")
        print(f"   ✅ Routine analyzed: {len(workout_routine) > 0}")

        if passed_criteria >= 4:
            print(f"\n🎉 EXTRACTION TEST PASSED!")
            print(f"The file extraction system is working correctly.")
            return True
        else:
            print(f"\n⚠️  EXTRACTION TEST PARTIALLY FAILED")
            print(f"Some components are not working as expected.")
            return False

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False

    finally:
        # Cleanup
        try:
            import shutil

            cleanup_dir = os.path.join(temp_base, "athlea_onboarding", test_user_id)
            if os.path.exists(cleanup_dir):
                shutil.rmtree(cleanup_dir)
            print(f"\n🧹 Cleaned up test files")
        except Exception as e:
            print(f"⚠️  Cleanup error: {e}")


async def main():
    """Run the final extraction test"""
    result = await test_file_extraction()

    print("\n" + "=" * 60)
    if result:
        print("🎉 ONBOARDING EXTRACTION SYSTEM WORKING!")
    else:
        print("🚨 ONBOARDING EXTRACTION NEEDS ATTENTION!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
