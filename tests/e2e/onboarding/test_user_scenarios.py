#!/usr/bin/env python3
"""
Comprehensive Onboarding User Scenarios Test Suite
"""

import asyncio
import logging
from typing import List, Dict, Any
from langchain_core.messages import HumanMessage

from athlea_langgraph import (
    create_initial_onboarding_state,
    get_compiled_onboarding_graph,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OnboardingScenarioTester:
    """Test different user scenarios through the complete onboarding flow"""

    def __init__(self):
        self.graph = None
        
    async def setup_graph(self):
        """Initialize the onboarding graph for testing"""
        if not self.graph:
            self.graph = get_compiled_onboarding_graph(
                checkpointer=None,
                enable_mem0=False,
                mem0_use_api=False
            )
        return self.graph

    async def test_scenario(self, scenario_name: str, messages: List[str]) -> Dict[str, Any]:
        """Test a complete onboarding scenario"""
        print(f"\n�� TESTING SCENARIO: {scenario_name}")
        print("=" * 70)
        
        graph = await self.setup_graph()
        config = {"configurable": {"thread_id": f"scenario_{scenario_name.lower().replace(' ', '_')}"}}
        
        # Convert messages to HumanMessage objects
        message_objects = [HumanMessage(content=msg) for msg in messages]
        
        # Create state and execute
        state = create_initial_onboarding_state("test_user")
        state["messages"] = message_objects
        state["user_input"] = message_objects[-1].content
        
        print(f"📝 User Messages:")
        for i, msg in enumerate(messages, 1):
            print(f"   {i}. {msg[:80]}{'...' if len(msg) > 80 else ''}")
        
        # Execute the graph
        result = await graph.ainvoke(state, config)
        
        print(f"\n📊 ANALYSIS RESULTS:")
        print(f"   ✅ Has enough info: {result.get('has_enough_info', False)}")
        print(f"   📈 Stage: {result.get('onboarding_stage', 'unknown')}")
        
        # Show extracted information
        sidebar_data = result.get('sidebar_data')
        if sidebar_data:
            if isinstance(sidebar_data, dict):
                goals = sidebar_data.get('goals', {}).get('list', [])
                sports = sidebar_data.get('selected_sports', [])
                summary_count = len(sidebar_data.get('summary_items', []))
            else:
                goals = sidebar_data.goals.list if sidebar_data.goals else []
                sports = sidebar_data.selected_sports or []
                summary_count = len(sidebar_data.summary_items or [])
                
            print(f"   🎯 Goals extracted: {len(goals)} - {goals}")
            print(f"   🏃 Sports extracted: {len(sports)} - {sports}")
            print(f"   📋 Summary items: {summary_count}")
        
        return {
            "scenario": scenario_name,
            "result": result,
            "success": result.get('has_enough_info', False)
        }


# Test Scenarios
SCENARIOS = {
    "beginner_runner": {
        "name": "Beginner Runner - 10K Goal",
        "messages": [
            "Hi! I want to start training for a 10K race in 6 months. I'm a complete beginner.",
            "I can train 3-4 times per week, about 30-45 minutes per session. I prefer morning workouts.",
            "I have access to a gym and can run outdoors. No injuries currently.",
            "My main goal is just to finish the 10K without walking. I want to build a running base gradually.",
            "I'm ready to start - let's create my training plan!"
        ]
    },
    
    "insufficient_information": {
        "name": "Insufficient Information - Should Not Trigger",
        "messages": [
            "Hi, I want to get fit.",
            "I like sports.",
        ]
    }
}


async def main():
    """Run all scenario tests manually"""
    print("🧪 COMPREHENSIVE ONBOARDING SCENARIO TESTING")
    print("=" * 80)
    
    tester = OnboardingScenarioTester()
    results = []
    
    for scenario_key, scenario_data in SCENARIOS.items():
        try:
            result = await tester.test_scenario(
                scenario_data["name"],
                scenario_data["messages"]
            )
            results.append(result)
        except Exception as e:
            print(f"❌ Error in scenario {scenario_data['name']}: {e}")
            results.append({
                "scenario": scenario_data["name"],
                "success": False,
                "error": str(e)
            })
    
    # Summary
    print(f"\n\n📊 SCENARIO TEST SUMMARY")
    print("=" * 50)
    
    successful = sum(1 for r in results if r.get("success"))
    total = len(results)
    
    print(f"✅ Successful scenarios: {successful}/{total}")
    print(f"❌ Failed scenarios: {total - successful}/{total}")
    
    for result in results:
        status = "✅" if result.get("success") else "❌"
        scenario_name = result["scenario"]
        print(f"   {status} {scenario_name}")
        if "error" in result:
            print(f"      Error: {result['error']}")
    
    print(f"\n🎯 Overall Success Rate: {(successful/total)*100:.1f}%")


if __name__ == "__main__":
    asyncio.run(main())
