#!/usr/bin/env python3
"""
Complex end-to-end test for the complete onboarding flow
Demonstrates full user journey from greeting to plan generation
"""

import asyncio
import json

from langchain_core.messages import HumanMessage

from athlea_langgraph import (
    create_initial_onboarding_state,
    get_graph_factory,
)


class OnboardingTester:
    def __init__(self):
        # Use the graph factory to create the onboarding graph
        factory = get_graph_factory()
        self.graph = factory.create_graph("onboarding")
        self.config = {"configurable": {"thread_id": "complex_test_thread"}}
        self.state = None
        self.conversation_log = []

    async def run_conversation_turn(self, user_input: str, turn_description: str):
        """Run a single conversation turn and log results"""
        print(f"\n--- {turn_description} ---")
        print(f"User: {user_input}")

        # Update state with user input
        if self.state is None:
            self.state = create_initial_onboarding_state("complex_test_user")
            self.state["messages"] = []

        # Add user message
        self.state["messages"].append(HumanMessage(content=user_input))
        self.state["user_input"] = user_input

        try:
            # Invoke the graph
            result = await self.graph.ainvoke(self.state, self.config)

            # Update state for next turn
            self.state = result

            # Get AI response
            messages = result.get("messages", [])
            if messages:
                ai_response = messages[-1].content
                print(f"AI: {ai_response}")

                # Log conversation
                self.conversation_log.append(
                    {
                        "turn": turn_description,
                        "user_input": user_input,
                        "ai_response": ai_response,
                        "stage": result.get("onboarding_stage", "unknown"),
                        "sidebar_stage": (
                            result.get("sidebar_data").current_stage
                            if result.get("sidebar_data")
                            else "unknown"
                        ),
                    }
                )

            # Print state info
            sidebar = result.get("sidebar_data")
            if sidebar:
                print(f"Stage: {sidebar.current_stage}")
                print(f"Goals extracted: {sidebar.goals.exists}")
                if sidebar.goals.exists:
                    print(f"Goals: {sidebar.goals.goal_list}")
                print(f"Selected sport: {sidebar.selected_sport}")
                print(f"Summary items: {len(sidebar.summary_items)}")

                # Print summary items
                if sidebar.summary_items:
                    print("Summary items:")
                    for item in sidebar.summary_items:
                        importance = "🔥" if item.is_important else "📝"
                        print(f"  {importance} {item.category}: {item.details}")

            print(f"Has enough info: {result.get('has_enough_info', False)}")
            print(f"Needs input: {result.get('needs_input', False)}")

            return result

        except Exception as e:
            print(f"❌ Error in turn: {e}")
            return None

    async def test_complete_onboarding_flow(self):
        """Test the complete onboarding flow from start to finish"""
        print("🚀 Starting Complex Onboarding Flow Test")
        print("=" * 60)

        # Turn 1: Initial greeting
        result1 = await self.run_conversation_turn(
            "Hi! I'm looking to improve my fitness and need some guidance.",
            "Turn 1: Initial Greeting",
        )

        if not result1:
            return False

        # Turn 2: Sport selection
        result2 = await self.run_conversation_turn("Running", "Turn 2: Sport Selection")

        # Turn 3: Provide goals and experience
        result3 = await self.run_conversation_turn(
            "I want to run a half marathon in 6 months. I'm a beginner runner but I've been doing some cardio workouts. My goal is to complete the half marathon in under 2 hours.",
            "Turn 3: Goals and Experience",
        )

        # Turn 4: Time commitment and availability
        result4 = await self.run_conversation_turn(
            "I can train 4-5 days per week, about 45-60 minutes per session. I prefer morning workouts before work, around 6-7 AM.",
            "Turn 4: Time Commitment",
        )

        # Turn 5: Equipment and location
        result5 = await self.run_conversation_turn(
            "I have access to a gym with treadmills and basic equipment. I also like running outdoors in my neighborhood and local parks. I have running shoes but that's about it for gear.",
            "Turn 5: Equipment Access",
        )

        # Turn 6: Final details and priorities
        result6 = await self.run_conversation_turn(
            "My main priority is building endurance safely without getting injured. I've never run more than 5K before. The half marathon is in April, so I have about 6 months to prepare. I'd also like to improve my overall fitness, not just running.",
            "Turn 6: Priorities and Timeline",
        )

        # Turn 7: Confirm readiness
        result7 = await self.run_conversation_turn(
            "That sounds perfect! I'm ready to get started with my training plan.",
            "Turn 7: Confirm Readiness",
        )

        # Print final summary
        print("\n" + "=" * 60)
        print("🎯 FINAL ONBOARDING RESULTS")
        print("=" * 60)

        if result7:
            final_sidebar = result7.get("sidebar_data")
            if final_sidebar:
                print(f"Final Stage: {final_sidebar.current_stage}")
                print(f"Selected Sport: {final_sidebar.selected_sport}")
                print(f"Goals Extracted: {final_sidebar.goals.exists}")
                if final_sidebar.goals.exists:
                    print(f"Goals: {final_sidebar.goals.goal_list}")

                print(f"\nSummary ({len(final_sidebar.summary_items)} items):")
                for item in final_sidebar.summary_items:
                    importance = "🔥 IMPORTANT" if item.is_important else "📝 INFO"
                    print(f"  [{importance}] {item.category}: {item.details}")

                # Check if plan was generated
                if final_sidebar.generated_plan:
                    plan = final_sidebar.generated_plan
                    print(f"\n✅ PLAN GENERATED!")
                    print(f"Plan Name: {plan.name}")
                    print(f"Duration: {plan.duration}")
                    print(f"Level: {plan.level}")
                    print(f"Type: {plan.plan_type}")
                    print(f"Disciplines: {plan.disciplines}")
                    print(f"Phases: {len(plan.phases)}")
                    print(f"Example Sessions: {len(plan.example_sessions)}")
                    print(f"Rationale: {plan.rationale[:200]}...")
                else:
                    print("\n⚠️ No plan generated yet")

            print(f"\nFinal Status:")
            print(f"Has Enough Info: {result7.get('has_enough_info', False)}")
            print(f"Onboarding Stage: {result7.get('onboarding_stage', 'unknown')}")

            # Check completion
            is_complete = (
                final_sidebar
                and final_sidebar.current_stage == "complete"
                and final_sidebar.generated_plan is not None
            )

            if is_complete:
                print("\n🎉 ONBOARDING FLOW COMPLETED SUCCESSFULLY!")
                print(
                    "✅ User journey: Greeting → Sport Selection → Info Gathering → Plan Generation"
                )
                return True
            else:
                print(
                    f"\n⚠️ Onboarding not fully complete. Stage: {final_sidebar.current_stage if final_sidebar else 'unknown'}"
                )
                return False

        return False

    def print_conversation_summary(self):
        """Print a summary of the entire conversation"""
        print("\n" + "=" * 60)
        print("💬 CONVERSATION SUMMARY")
        print("=" * 60)

        for i, log in enumerate(self.conversation_log, 1):
            print(f"\n{i}. {log['turn']}")
            print(f"   Stage: {log['sidebar_stage']}")
            print(f"   User: {log['user_input'][:100]}...")
            print(f"   AI: {log['ai_response'][:100]}...")


async def main():
    """Run the complex onboarding test"""
    tester = OnboardingTester()

    try:
        success = await tester.test_complete_onboarding_flow()
        tester.print_conversation_summary()

        if success:
            print("\n🏆 COMPLEX ONBOARDING TEST PASSED!")
            print("The complete onboarding flow is working perfectly.")
        else:
            print("\n💥 COMPLEX ONBOARDING TEST FAILED!")
            print("The onboarding flow has issues that need to be addressed.")

        return success

    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
