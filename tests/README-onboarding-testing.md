# Onboarding Frontend Data Validation Testing Guide

This guide provides comprehensive testing strategies to ensure the frontend receives all correct values from the onboarding graph. It covers testing the Python backend, proxy transformations, and frontend data reception.

## 🎯 What We're Testing

The onboarding system has three main components:
1. **Python LangGraph Backend** - Generates onboarding data in snake_case format
2. **TypeScript Proxy Route** - Transforms Python data to camelCase for frontend
3. **Frontend Components** - Consume the transformed data

We need to ensure:
- ✅ Python backend sends correct data structures
- ✅ Proxy correctly transforms snake_case to camelCase  
- ✅ Frontend receives all expected fields and values
- ✅ Data transformations preserve all information
- ✅ Real-time updates work correctly

## 🧪 Available Test Scripts

### Quick Start Commands

```bash
# Run comprehensive data validation
npm run test:onboarding-data

# Test proxy transformation logic
npm run test:onboarding-proxy

# Test integration between frontend and Python backend
npm run test:onboarding-integration

# Test sidebar data reception specifically
npm run test:onboarding-sidebar

# Debug data flow in real-time
npm run debug:onboarding

# Debug complete onboarding flow
npm run debug:onboarding-flow

# Debug specific data field
npm run debug:onboarding-field goals
```

## 📋 Test Files Overview

### 1. `test-onboarding-data-validation.js`
**Purpose**: Comprehensive end-to-end validation of the entire onboarding flow

**What it tests**:
- Complete onboarding conversation flow (8 steps)
- Data structure validation at each step
- Goals extraction and storage
- Summary items collection  
- Sport selection and storage
- Plan generation validation
- Data evolution tracking

**Key Features**:
- ✅ Validates TypeScript interface compliance
- ✅ Tests specific expectations per conversation step
- ✅ Captures complete data evolution history
- ✅ Provides detailed success/failure reporting

**Expected Results**:
```
Step 1: Sport suggestions provided ✅
Step 2: Sports selected correctly ✅ 
Step 3: Goals extracted and exist ✅
Steps 4-6: Summary items added ✅
Step 8: Plan generated ✅
```

### 2. `test-proxy-transformation.js`
**Purpose**: Unit tests for the data transformation logic in the proxy route

**What it tests**:
- Snake_case to camelCase transformation
- Data structure preservation
- Empty/null data handling
- Partial data scenarios
- SSE event transformation

**Key Validations**:
```javascript
// Python format → TypeScript format
current_stage → currentStage
summary_items → summaryItems  
generated_plan → generatedPlan
sport_suggestions → sportSuggestions
selected_sport → selectedSport
selected_sports → selectedSports
```

**Expected Results**:
```
Complete sidebar data transformation ✅
Empty data handling ✅
Partial data scenarios ✅
Sport suggestions transformation ✅
Plan structure preservation ✅
Summary items transformation ✅
Selected sports handling ✅
```

### 3. `test-onboarding-integration.cjs`
**Purpose**: Integration testing between frontend proxy and Python backend

**What it tests**:
- Frontend proxy GET/POST endpoints
- Python backend connectivity
- SSE streaming functionality
- Event count and data flow
- Timeout and error handling

**Key Metrics**:
- Response times and status codes
- Event counts and data volumes
- Stream completion rates
- Error handling effectiveness

### 4. `test-onboarding-sidebar-integration.js` 
**Purpose**: Specific testing of sidebar data updates

**What it tests**:
- Sidebar update event reception
- Goals detection and formatting
- Summary items collection
- Sports selection tracking
- Real-time data updates

**Expected Flow**:
```
Message 1: Initial greeting → Sport suggestions
Message 2: Sport selection → Updated selectedSports  
Message 3: Goals statement → Goals extracted
Message 4: Experience info → Summary items added
```

### 5. `debug-onboarding-data-flow.js`
**Purpose**: Real-time debugging and data flow visualization

**What it provides**:
- Live comparison of Python vs Proxy data
- Field-by-field transformation analysis
- Data validation issue detection
- Timeline of data evolution
- Interactive debugging sessions

**Debug Modes**:
```bash
# Single message debugging
npm run debug:onboarding message "I want to build muscle"

# Complete flow debugging  
npm run debug:onboarding-flow

# Field-specific debugging
npm run debug:onboarding-field goals
npm run debug:onboarding-field sports
npm run debug:onboarding-field summaryItems
```

## 🔍 Key Data Structures to Validate

### Sidebar Data Structure
```typescript
interface SidebarStateData {
  currentStage: string;           // "initial" | "gathering" | "complete"
  goals: {
    exists: boolean;
    list: string[];
  };
  summaryItems: SummaryItem[];    // Categorized user information
  generatedPlan: PlanDetails | null;
  sportSuggestions: SportSuggestion[] | null;
  selectedSport: string | null;
  selectedSports: string[];       // Array of selected sports
}
```

### Summary Item Structure
```typescript
interface SummaryItem {
  category: string;               // e.g., "Training Frequency"
  details: string;               // e.g., "3-4 times per week"  
  isImportant: boolean;          // Critical for plan safety
}
```

### Plan Structure
```typescript
interface PlanDetails {
  planId: string;                // UUID
  name: string;                  // Plan name
  description: string;           // Plan description
  duration: string;              // e.g., "12 weeks"
  level: string;                 // "Beginner" | "Intermediate" | "Advanced"
  planType: string;              // e.g., "Mixed", "Running"
  disciplines: string[];         // e.g., ["Running", "Strength Training"]
  rationale: string;             // Explanation
  phases: PlanPhase[];           // Training phases
  exampleSessions: ExampleSession[]; // Sample sessions
}
```

## 🚨 Common Issues to Test For

### Data Transformation Issues
- ❌ Snake_case fields not converted to camelCase
- ❌ Missing fields after transformation
- ❌ Type changes during transformation
- ❌ Array/object structure modifications

### Backend Communication Issues  
- ❌ Python backend not responding
- ❌ SSE stream interruptions
- ❌ Incomplete data transmission
- ❌ Timeout errors

### Frontend Reception Issues
- ❌ Sidebar not updating with new data
- ❌ Goals not being extracted properly
- ❌ Sports selection not working
- ❌ Plan generation not triggering

## 📊 Running Complete Test Suite

To run all onboarding tests:

```bash
# 1. Start your development servers
npm run dev                     # Frontend (port 3000)
# Start Python backend         # Backend (port 8000)

# 2. Run all tests
npm run test:onboarding-data
npm run test:onboarding-proxy  
npm run test:onboarding-integration
npm run test:onboarding-sidebar

# 3. Debug any issues
npm run debug:onboarding

# 4. Check specific fields if needed
npm run debug:onboarding-field goals
```

## 🎯 Success Criteria

A fully working onboarding system should show:

### ✅ Data Validation Tests
- All structure validations pass
- Field transformations preserve data
- No snake_case fields in frontend data
- All expected TypeScript interfaces match

### ✅ Integration Tests  
- Python backend responds correctly
- Proxy transforms data properly
- Frontend receives all sidebar updates
- Real-time streaming works

### ✅ Functional Tests
- Sport suggestions appear initially
- Sport selection updates selectedSports
- Goals extraction populates goals.list  
- Summary items accumulate over conversation
- Plan generation creates complete plan object

## 🐛 Troubleshooting

### Python Backend Issues
```bash
# Check if backend is running
curl http://localhost:8000/health

# Test direct backend endpoint
npm run debug:onboarding message "test"
```

### Proxy Transformation Issues
```bash  
# Test transformation logic
npm run test:onboarding-proxy

# Debug specific transformation
npm run debug:onboarding
```

### Frontend Data Issues
```bash
# Monitor sidebar updates
npm run test:onboarding-sidebar

# Debug specific fields
npm run debug:onboarding-field summaryItems
```

## 📈 Continuous Testing

For ongoing development:

1. **Pre-commit**: Run `npm run test:onboarding-proxy` 
2. **Development**: Use `npm run debug:onboarding` for real-time debugging
3. **Integration**: Run `npm run test:onboarding-integration` before deployments
4. **Full validation**: Run `npm run test:onboarding-data` for comprehensive checks

This testing suite ensures your frontend reliably receives all correct values from the onboarding graph with full data integrity and proper TypeScript compliance. 