import { EmotionScores } from "@humeai/voice";

type Chat = {
  avatar: string;
  name: string;
  text: string;
  time: number;
  textCount: number;
  dot: number;
};

interface Log {
  id?: string | number;
  message?: string;
  timestamp?: string;
  senderName?: string;
  type: string;
  step?: string;
  session_type?: string;
  serviceName?: string;
  logType?: string;
  text?:
    | {
        step?: string;
        session_type?: string;
        content?: string;
      }
    | string;
}

interface Ai_Voice_Message {
  type:
    | "user_message"
    | "assistant_message"
    | "chat_metadata"
    | "socket_connected"
    | "user_interruption";
  fromText: boolean;
  id?: string;
  message: { content: string; role: string };
  models?: { prosody: { scores: any } };
  receivedAt?: Date;
}

interface UpdateTrainingPlanPayload {
  jobId: string;
  data: {
    coaches?: string[];
    lastActiveCoach?: string | null;
    serviceName?: string;
    lastUpdated?: string;
    suggestion?: any;
  };
}

interface UpdateTrainingPlanResponse {
  message: string;
  updatedDocument: {
    jobId: string;
    coaches: string[];
    lastActiveCoach: string | null;
    serviceName: string;
    lastUpdated: string;
    suggestion?: any;
  };
}

// Define the ModificationAction interface to match the returned structure
interface ModificationAction {
  type: "add" | "update" | "remove";
  discipline: "cycling" | "strength" | "nutrition" | "recovery" | "mindset";
  target: string;
  from: string;
  to: string;
  rationale: string;
  importance: string;
  phaseNames: string[];
  implementation: string;
  metrics: string[];
}

interface ModificationChange {
  type: "add" | "update" | "remove";
  discipline: "cycling" | "strength" | "nutrition" | "recovery" | "mindset";
  target: string;
  from: string;
  to: Record<string, unknown>; // Change from string to a more flexible type
  rationale: string;
  importance: string;
  phaseNames: string[];
  implementation: string;
  metrics: string[];
  phases: "all" | string[];
  modifyPhaseField: boolean;
  phaseFieldName?: string;
  scope?: "topLevelPlan" | "phaseLevelField" | "domainData";
}

// Define the structure of the modifications content
interface ModificationsContent {
  actions: ModificationAction[];
}

// Define the content structure
interface ModificationResponseContent {
  rationale: string;
  modifications: ModificationsContent;
}

// Define the complete response structure
interface ModificationApiResponse {
  data: {
    content: ModificationResponseContent;
    message: string;
  };
}

// Define the TrainingModification interface for state storage
interface TrainingModification {
  id?: number;
  status?: string;
  rationale: string;
  changes: ModificationChange[];
}

interface ModificationGroup extends TrainingModification {
  id: number;
  changes: ModificationChange[];
}

interface Message {
  emotions?: EmotionScores | undefined;
  training_modifications?: TrainingModification;
  verification_sources?: (
    | {
        claim: string;
        importance: number;
        textSnippet: string;
        sources: any;
        error?: undefined;
      }
    | {
        claim: string;
        importance: number;
        textSnippet: string;
        sources: never[];
        error: string;
      }
  )[];
  type:
    | "question"
    | "answer"
    | "event"
    | "task"
    | "action"
    | "admin"
    | "system"
    | "log"
    | "file"
    | "error"
    | "plan"
    | "modification"
    | "location_request";
  text: string;
  timestamp?: string;
  id?: number;
  goalOptions?: string[];
  weekNumber?: number;
  sessionType?: string;
  senderName?: string;
  taskId?: string;
  suggestion?: any;
  workout?: any;
  keywords?: string[];
  isServiceChange?: boolean;
  planNumber?: number;
  serviceName?: string;
  hasSuggestions?: boolean;
  logType?: string;
  lastUserMessage?: string | null | undefined;
  file?: {
    name: string;
    type: string;
    id: string;
  };
  PlanID?: string;
  locations?: Array<{
    name: string;
    category: string;
    address: string;
    distance: string;
    coordinates: string;
  }>;
  data?: any;
  task?: any;
}

interface ParsedMessage {
  message: Message;
  suggestion?: any;
  task?: {
    data: any;
    id: string | number;
    timestamp: string;
  };
}

interface ChatContent {
  type: "task" | "suggestion" | "plan";
  text?: string;
  id?: string | number;
  timestamp?: string;
  senderName?: string;
  data: any;
  date?: string;
  time?: string;
  rationale?: string;
}

interface SystemMessageType extends SystemMessage {
  type: "system";
  taskId?: string;
}

interface SystemMessage {
  id: string | number;
  message: string;
  timestamp: string;
  senderName: string;
  text?: string;
  type: "system";
}

interface Task {
  date: string;
  time: any;
  session_type?: string;
  PlanID?: string;
  duration?: number;
  segments?: any[];
  data?: any;
  id?: string | number;
  timestamp?: string;
  text?: string;
}

interface VerificationSource {
  claim: string;
  importance: number;
  textSnippet: string;
  sources: any[];
  error?: string;
}

interface TaskResult {}

interface ChatState {
  chatMessages: Message[];
  chatContent: ChatContent[];
  isWebSocketReady: boolean;
  isWebSocketConnected: boolean;
  pendingResponse: boolean;
  currentQuestion: string | null;
  answer: string;
  taskResults: TaskResult[];
  coachName: string | null;
  selectedJobId: string;
  tempGoalOptions: string[];
  selectedSuggestions: string[];
  pendingQuestionQueue: any[];
  tasks: Task[];
  systemMessages: SystemMessage[];
  userInput: string;
  tempCoachName: string | null;
  currentSuggestionBubbleHeight: number;
  serviceName: string;
  suggestions: Record<string, any>;
  planSuggestions: any;
  humeAccessToken: string;
  ai_voice_messages: Ai_Voice_Message[];
  logArray: Message[];
  uploading: boolean;
  coaches: string[];
  lastActiveCoach: string | null;
  lastUserMessage?: string | null | undefined;
  hasErrorMessage: boolean;
  lastPlanNameUpdateTime: number;
  profileUpdateTrigger: number;
  newSelectedContentId: string | null;
  initializationComplete: boolean;
  trainingModifications: {
    [msgId: string]: TrainingModification;
  };
  activeModifications: TrainingModification[];
  selectedMods: string[];
  stepsLogs: Log[];
}

interface SendChatMessageOptions {
  display: boolean;
  isServiceChange?: boolean;
  type?: string;
  sendToBackend?: boolean;
  timestamp?: string;
}

interface SendChatMessagePayload {
  msg?: string;
  options?: SendChatMessageOptions;
  timestamp?: string;
}

// Define the payload for saving modifications
interface SaveTrainingModificationsPayload {
  msgId: number;
  training_modifications: TrainingModification;
}

export type {
  ChatContent,
  Chat,
  Message,
  SystemMessageType,
  SystemMessage,
  Task,
  ChatState,
  SendChatMessageOptions,
  SendChatMessagePayload,
  Ai_Voice_Message,
  UpdateTrainingPlanPayload,
  UpdateTrainingPlanResponse,
  ParsedMessage,
  TrainingModification,
  ModificationAction,
  ModificationApiResponse,
  SaveTrainingModificationsPayload,
  VerificationSource,
  ModificationChange,
  ModificationGroup,
  Log,
};
