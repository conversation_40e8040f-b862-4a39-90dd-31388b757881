{"mcpServers": {"perplexity-mcp": {"command": "uvx", "args": ["perplexity-mcp"], "env": {"PERPLEXITY_API_KEY": "$PERPLEXITY_API_KEY", "PERPLEXITY_MODEL": "sonar"}}, "athlea-strength": {"command": "python", "args": ["-m", "mcp_servers.strength_mcp.server"], "env": {"OPENAI_API_KEY": "$AZURE_OPENAI_API_KEY", "ATHLEA_DB_URL": "$ATHLEA_DB_URL"}}, "athlea-nutrition": {"command": "python", "args": ["-m", "mcp_servers.nutrition_mcp.server"], "env": {"NUTRITION_API_KEY": "$NUTRITION_API_KEY", "USDA_API_KEY": "$USDA_API_KEY"}}, "athlea-cardio": {"command": "python", "args": ["-m", "mcp_servers.cardio_mcp.server"], "env": {"AZURE_MAPS_KEY": "$AZURE_MAPS_KEY", "STRAVA_API_KEY": "$STRAVA_API_KEY"}}, "athlea-recovery": {"command": "python", "args": ["-m", "mcp_servers.recovery_mcp.server"], "env": {"SLEEP_API_KEY": "$SLEEP_API_KEY", "HRV_API_KEY": "$HRV_API_KEY"}}, "athlea-mental": {"command": "python", "args": ["-m", "mcp_servers.mental_mcp.server"], "env": {"MENTAL_HEALTH_API_KEY": "$MENTAL_HEALTH_API_KEY"}}, "athlea-external": {"command": "python", "args": ["-m", "mcp_servers.external_mcp.server"], "env": {"AZURE_MAPS_KEY": "$AZURE_MAPS_KEY", "GOOGLE_SEARCH_API_KEY": "$GOOGLE_SEARCH_API_KEY", "AIRTABLE_API_KEY": "$AIRTABLE_API_KEY", "WEATHER_API_KEY": "$WEATHER_API_KEY", "WIKIPEDIA_API_KEY": "$WIKIPEDIA_API_KEY"}}}}