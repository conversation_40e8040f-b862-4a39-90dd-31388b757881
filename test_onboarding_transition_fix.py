#!/usr/bin/env python3
"""
Test script to verify the onboarding transition fix.

This script tests that when the information gatherer determines it should transition
to plan generation, it properly sets has_enough_info=True so the router proceeds
to the checkCompletion node instead of ending.
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from athlea_langgraph.agents.onboarding.information_gatherer_node import InformationGathererNode
from athlea_langgraph.states.onboarding_state import OnboardingState, SidebarStateData, UserGoals
from langchain_core.messages import HumanMessage, AIMessage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_transition_fix():
    """Test that the information gatherer properly sets has_enough_info when transitioning"""
    
    logger.info("🧪 Testing onboarding transition fix...")
    
    # Create a mock state that should trigger transition to plan generation
    test_state = OnboardingState(
        messages=[
            HumanMessage(content="I want to improve my endurance and increase my speed. I can dedicate five days a week to it."),
            AIMessage(content="Great! What specific activities are you focusing on?"),
            HumanMessage(content="I'm looking to improve my 8k times"),
            AIMessage(content="Excellent! That's a great goal. Let me help you create a plan."),
            HumanMessage(content="Great lets do it")
        ],
        user_id="test_user_123",
        conversation_history=[],
        current_question_field=None,
        onboarding_stage="gathering",
        goal="Improve 8k times",
        experience_level=None,
        time_commitment="5 days a week",
        equipment=None,
        sidebar_data=SidebarStateData(
            current_stage="gathering",
            goals=UserGoals(
                list=["Improve endurance", "Increase speed", "Improve 8k times"],
                exists=True
            ),
            summary_items=[],
            selected_sports=["running"],
            selected_sport="running"
        ),
        has_enough_info=False,
        needs_input=False,
        requires_input=False,
        input_prompt=None,
        current_task_description=None,
        generated_plan=None,
        sport_suggestions=None,
        user_input=None,
        resume_value=None,
        user_name=None,
        error=None,
        info_gathered=False,
        pending_goal_clarification=False,
        system_prompt="",
        _langgraph_interrupt=None,
    )
    
    # Create the information gatherer node
    info_gatherer = InformationGathererNode()
    
    try:
        # Call the information gatherer node
        logger.info("📞 Calling information gatherer node...")
        result = await info_gatherer(test_state)
        
        # Check the result
        has_enough_info = result.get("has_enough_info", False)
        messages = result.get("messages", [])
        
        logger.info(f"✅ Result: has_enough_info = {has_enough_info}")
        logger.info(f"✅ Messages returned: {len(messages)}")
        
        if messages:
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                logger.info(f"✅ Last message content preview: {last_message.content[:200]}...")
        
        # Verify the fix
        if has_enough_info:
            logger.info("🎉 SUCCESS: has_enough_info is True - router will proceed to checkCompletion!")
            return True
        else:
            logger.error("❌ FAILURE: has_enough_info is False - router will END instead of proceeding")
            return False
            
    except Exception as e:
        logger.error(f"❌ ERROR during test: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🚀 Starting onboarding transition fix test...")
    
    success = await test_transition_fix()
    
    if success:
        logger.info("✅ Test PASSED - Fix is working correctly!")
        sys.exit(0)
    else:
        logger.error("❌ Test FAILED - Fix needs more work")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
