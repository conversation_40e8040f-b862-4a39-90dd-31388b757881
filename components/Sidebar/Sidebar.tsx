import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import {
  clearJobId,
  fetchTrainingPlans,
  handleCoachClick,
  handleCreateNewChatClick,
  handleJobSelect,
  handlePencilClick,
  setChangedTrainingPlanName,
  setChatOption,
  setCoaches,
  setInitialMessage,
  setIsHomeInputSubmitted,
  setIsSidebarModalOpen,
  setNeedInitialMessages,
  setPlanTitle,
  setSelectedJobId,
  setSelectedPlan,
  setTemporaryCoaches,
} from "@/store/slices/jobSlice";
import { RootState } from "@/store/store";
import { TrainingPlan } from "@/types/plan";
import { SidebarItem } from "@/types/sidebar";
import { useKindeBrowserClient } from "@kinde-oss/kinde-auth-nextjs";
import {
  LoginLink,
  RegisterLink,
} from "@kinde-oss/kinde-auth-nextjs/components";
import axios from "axios";
import { isAfter, isToday, isYesterday, subDays } from "date-fns";
import {
  CalendarIcon,
  Loader,
  MenuIcon,
  MoreHorizontal,
  PencilIcon,
  UserCircle,
  UserPlus2Icon,
  BarChart,
  LineChart,
  Menu,
  Plus,
  ChevronDown,
  Search,
  Globe,
  SparkleIcon as Sparkles2,
  Library,
  Bike,
  Dumbbell,
  MonitorIcon as Running,
  Apple,
  Heart,
  Settings,
  Calendar,
  BarChart2,
  Users2,
  Brain,
  Bot,
  Network,
  Home,
  List,
  FolderOpen,
} from "lucide-react";
import Link from "next/link";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import React, { FC, useCallback, useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom";
import Typewriter from "typewriter-effect";
import ConfirmationDialog from "../Dialogs/confirmationDialog";
import FormDialog from "../Dialogs/formDialog";
import ShareLinkModal from "../Header/ShareLinkModal";
import CoachesList from "./CoachesList";
import InviteModal from "./InviteModal";
import PopupMenu from "./PopupMenu";
import { useAnimationMessage } from "@/context/AnimationMessageContext";
import LinkItem from "./LinkItem";
import { clearContentAndChats, setJobId } from "@/store/slices/chatSlice";
import { setIsOpen } from "@/store/slices/calendarSlice";
import toast from "react-hot-toast";
import { useAuthState } from "react-firebase-hooks/auth";
import { getAuth, signOut } from "firebase/auth";
import { useTrainingPlan } from "@/context/TrainingPlanContext";
import { Button } from "../ui/button";
import { ScrollArea } from "../ui/scroll-area";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import DropdownProfile from "../Header/DropdownProfile";
import RingChart from "../Profile/RingChart";
import { ReactSVG } from "react-svg";
import {
  Sidebar as ShadcnSidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroupLabel,
  SidebarGroupContent,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import OnboardingSidebar from "../Chat/OnboardingSidebar";

const navItems = [
  { name: "Home", icon: Home, href: "/" },
  { name: "Discover", icon: Globe, href: "/discover" },
  { name: "Spaces", icon: Sparkles2, href: "/spaces" },
  { name: "Library", icon: Library, href: "/library" },
];

const uiCoaches = [
  {
    name: "Cycling",
    icon: Bike,
    color: "bg-cyan-500",
    rgba: "rgba(6, 182, 212, ",
  },
  {
    name: "Strength",
    icon: Dumbbell,
    color: "bg-blue-500",
    rgba: "rgba(59, 130, 246, ",
  },
  {
    name: "Running",
    icon: Running,
    color: "bg-pink-500",
    rgba: "rgba(236, 72, 153, ",
  },
  {
    name: "Nutrition",
    icon: Apple,
    color: "bg-green-500",
    rgba: "rgba(34, 197, 94, ",
  },
  {
    name: "Recovery",
    icon: Heart,
    color: "bg-orange-500",
    rgba: "rgba(249, 115, 22, ",
  },
];

const recentSearches = [
  "what are databases that pu...",
  "provide the page links to th...",
  "can i bring a website from t...",
  "is it possible to bring a fr...",
  "how much investment did b...",
];

const profileMenuItems = [
  {
    label: "Calendar",
    icon: Calendar,
    action: (router: any) => router.push("/calendar"),
  },
  {
    label: "Stats",
    icon: BarChart2,
    action: (router: any) => router.push("/stats"),
  },
  {
    label: "Team",
    icon: Users2,
    action: (router: any) => router.push("/team"),
  },
];

const fetchWithRetry = async (
  url: string,
  options = {},
  retries = 3,
  backoff = 300,
): Promise<any> => {
  try {
    const response = await axios.get(url, options);
    return response;
  } catch (error) {
    if (retries === 0) throw error;
    await new Promise((resolve) => setTimeout(resolve, backoff));
    return fetchWithRetry(url, options, retries - 1, backoff * 2);
  }
};

const Sidebar: FC = () => {
  const { userData } = useAppSelector((state: RootState) => state.user);
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const jobIdParam = params?.id;
  const jobId = Array.isArray(jobIdParam) ? jobIdParam[0] : jobIdParam;
  const dispatch = useAppDispatch();
  const { state: sidebarState, setOpen } = useSidebar();
  const isSidebarOpen = sidebarState === "expanded";
  const {
    selectedJobId,
    sidebarItems,
    loading,
    coaches,
    lastActiveCoach,
    changedTrainingPlanName,
    rightCalendarPanel,
  } = useAppSelector((state: RootState) => state.job);
  const { chatMessages } = useAppSelector((state: RootState) => state.chat);
  const { showTrainingPlanModal } = useTrainingPlan();
  const { setIsAnimationMessageExpanded } = useAnimationMessage();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [groupedPlans, setGroupedPlans] = useState<{
    [key: string]: SidebarItem[];
  }>({});
  const [menuPosition, setMenuPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);
  const [menuJobId, setMenuJobId] = useState<string | null>(null);
  const [menuJobDetail, setMenuJobDetail] = useState<any>({});
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const sidebarPlanContainerRef = useRef<HTMLDivElement | null>(null);
  const selectedPlanRef = useRef<HTMLLIElement | null>(null);
  const auth = getAuth();
  const [user] = useAuthState(auth);
  const { isAuthenticated, isLoading } = useKindeBrowserClient();
  const menuRef = useRef<HTMLDivElement>(null);
  const [isShareDialog, setIsShareDialog] = useState(false);
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] =
    useState(false);
  const [toDeleteJobId, setToDeleteJobId] = useState<string | null>(null);
  const [isFormDialog, setIsFormDialog] = useState(false);
  const [isInviteModalOpen, setisInviteModalOpen] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 5;
  const retryDelay = 2000;
  const [scrollHeight, setScrollHeight] = useState<number>(0);
  const [isSkipAndLimitChnaged, setIsSkipAndLimitChnaged] =
    useState<boolean>(false);
  const [isFetchingPlan, setIsFetchingPlan] = useState<boolean>(false);

  const isFirstRender = useRef(true);
  const isOnboardingPath = pathname?.startsWith("/onboarding") ?? false;

  useEffect(() => {
    if (isOnboardingPath) {
      setOpen(false);
    }
  }, [isOnboardingPath, setOpen]);

  const initializeJobSelection = useCallback(async () => {
    console.log(
      "[initializeJobSelection] Started, isFirstRender:",
      isFirstRender.current,
      "jobId:",
      jobId,
      "pathname:",
      pathname,
      "isOnboardingPath:",
      isOnboardingPath,
    );

    if (isFirstRender.current) {
      isFirstRender.current = false;

      // PREVENT AUTO-NAVIGATION: If we're already on the correct onboarding path, don't redirect
      if (isOnboardingPath && pathname === `/onboarding/${jobId}`) {
        console.log(
          "[initializeJobSelection] Already on correct onboarding path, skipping auto-navigation to prevent redirect loop.",
        );
        return;
      }

      if (jobId) {
        console.log("[initializeJobSelection] Fetching plan for jobId:", jobId);
        try {
          const response = await axios.get(`/api/trainingPlans/${jobId}`);
          const selectedPlan = response.data;
          console.log(
            "[initializeJobSelection] API response for jobId:",
            selectedPlan,
          );

          if (selectedPlan) {
            console.log(
              "[initializeJobSelection] Plan found, dispatching handleJobSelect",
            );
            dispatch(
              handleJobSelect({
                item: {
                  id: jobId,
                  name: selectedPlan.planTitle || "New Chat",
                  type: "plan",
                  lastUpdated: new Date(
                    selectedPlan.lastUpdated || selectedPlan.dateAdded,
                  ).toISOString(),
                  isOnboarding: selectedPlan.isOnboarding || false,
                },
                navigate: router.push,
              }),
            );
          } else {
            console.log(
              "[initializeJobSelection] No plan found, creating generic item",
            );
            dispatch(
              handleJobSelect({
                item: {
                  id: jobId,
                  name: "New Chat",
                  type: "plan",
                  lastUpdated: new Date().toISOString(),
                },
                navigate: router.push,
              }),
            );
          }
        } catch (error) {
          console.error("[initializeJobSelection] Error fetching plan:", error);
        }
      } else {
        console.log(
          "[initializeJobSelection] No jobId provided, skipping initialization",
        );
      }
    } else {
      console.log("[initializeJobSelection] Not first render, skipping");
    }
  }, [jobId, dispatch, router, pathname, isOnboardingPath]);

  useEffect(() => {
    initializeJobSelection();
  }, [initializeJobSelection]);

  useEffect(() => {
    if (selectedPlanRef.current) {
      selectedPlanRef.current.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [selectedJobId]);

  useEffect(() => {
    const fetchPlans = () => {
      if (userData?.user_id) {
        dispatch(
          fetchTrainingPlans({ userId: userData.user_id, limit: 20, skip: 0 }),
        );
      } else if (retryCount < maxRetries) {
        const timerId = setTimeout(() => {
          setRetryCount((prevCount) => prevCount + 1);
        }, retryDelay);

        return () => clearTimeout(timerId);
      }
    };

    fetchPlans();
  }, [dispatch, userData?.user_id, retryCount, chatMessages]);

  useEffect(() => {
    (async () => {
      if (isSkipAndLimitChnaged) {
        setIsFetchingPlan(true);
        const loadData = await dispatch(
          fetchTrainingPlans({
            userId: userData.user_id,
            limit: 20,
            skip: sidebarItems.length,
          }),
        );
        console.log("loadData", loadData);
        setIsSkipAndLimitChnaged(false);
        setIsFetchingPlan(false);
        if (loadData.meta.requestStatus !== "fulfilled") {
          toast.error("Fetch plan error");
        }
      }
    })();
  }, [dispatch, userData?.user_id, isSkipAndLimitChnaged]);

  const decodedSelectedJobId = selectedJobId
    ? decodeURIComponent(selectedJobId)
    : null;

  useEffect(() => {
    console.log("Current selectedJobId:", selectedJobId);
    console.log("Decoded selectedJobId:", decodedSelectedJobId);
    const matchingPlan = sidebarItems.find(
      (plan) => plan.id === decodedSelectedJobId,
    );
    console.log("Matching plan:", matchingPlan);
  }, [selectedJobId, decodedSelectedJobId, sidebarItems]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMenuOpen]);

  const handleDelete = async () => {
    try {
      dispatch(setIsSidebarModalOpen(false));
      setIsConfirmationDialogOpen(false);
      const response = await axios.delete(
        `/api/trainingPlans/${toDeleteJobId}`,
      );
      if (response.status === 200) {
        dispatch(
          fetchTrainingPlans({
            userId: userData?.user_id || "",
            limit: 100,
            skip: 0,
          }),
        );
        setMenuPosition(null);
        setIsMenuOpen(false);
      } else {
        console.error("Failed to delete training plan:", response.data);
      }
    } catch (error) {
      console.error("Error deleting training plan:", error);
    }
  };

  const handleUpdateName = async (plan_name: any) => {
    dispatch(setIsSidebarModalOpen(false));
    setIsFormDialog(false);
    let tempObj: any = {
      jobId: plan_name,
      coachName: menuJobDetail?.coachName,
      dateAdded: menuJobDetail?.dateAdded,
    };
    console.log("tempOBJ++++>", tempObj);
    try {
      const response = await axios.put(
        `/api/trainingPlans/${menuJobDetail?._id}`,
        tempObj,
      );
      if (response.status === 200) {
        dispatch(
          fetchTrainingPlans({
            userId: userData?.user_id || "",
            limit: 100,
            skip: 0,
          }),
        );
        setMenuPosition(null);
        setIsMenuOpen(false);
      } else {
        console.error("Failed to update training plan:", response.data);
      }
    } catch (error) {
      console.error("Error updating training plan:", error);
    }
  };

  const handleShare = async (jobId: string) => {
    setIsShareDialog(true);
    setIsMenuOpen(false);
  };

  const handleForm = () => {
    dispatch(setIsSidebarModalOpen(true));
    setIsFormDialog(true);
    setIsMenuOpen(false);
  };

  const handleCloseShareModal = async () => {
    setIsShareDialog(false);
  };

  useEffect(() => {
    const groupPlansByDate = (plans: SidebarItem[]) => {
      const grouped: { [key: string]: SidebarItem[] } = {
        Onboarding: [],
        Today: [],
        Yesterday: [],
        "Previous 30 Days": [],
      };

      const today = new Date();
      const aWeekAgo = subDays(today, 30);

      [...plans]
        .sort(
          (a: SidebarItem, b: SidebarItem) =>
            new Date(b.lastUpdated).valueOf() -
            new Date(a.lastUpdated).valueOf(),
        )
        .forEach((plan: SidebarItem) => {
          const isOnboarding = (plan as any).isOnboarding;
          if (isOnboarding) {
            grouped["Onboarding"].push(plan);
          } else {
            try {
              const dateAdded = new Date(plan.lastUpdated);
              if (!isNaN(dateAdded.getTime()) && dateAdded <= today) {
                if (isToday(dateAdded)) {
                  grouped["Today"].push(plan);
                } else if (isYesterday(dateAdded)) {
                  grouped["Yesterday"].push(plan);
                } else if (isAfter(dateAdded, aWeekAgo)) {
                  grouped["Previous 30 Days"].push(plan);
                }
              }
            } catch (error) {
              console.log("[groupPlansByDate] Error parsing date:", error);
            }
          }
        });

      return grouped;
    };

    if (sidebarItems.length > 0) {
      setGroupedPlans(groupPlansByDate(sidebarItems));
      setTimeout(() => {
        if (sidebarPlanContainerRef && sidebarPlanContainerRef.current) {
          setScrollHeight(sidebarPlanContainerRef?.current?.scrollHeight);
        }
      }, 200);
    }
  }, [sidebarItems]);

  const handleIconClick = (event: React.MouseEvent, job: any) => {
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    if (menuJobId === jobId && isMenuOpen) {
      setMenuJobId(null);
      setMenuPosition(null);
      setIsMenuOpen(false);
    } else {
      setMenuPosition({
        top: rect.top + window.scrollY - 22,
        left: rect.left + window.scrollX + 40,
      });
      setMenuJobId(job?.jobId);
      setMenuJobDetail(job);
      setIsMenuOpen(true);
    }
  };

  const handleLinkClick = async (jobId: string) => {
    try {
      console.log("[handleLinkClick] Started with jobId:", jobId);
      dispatch(
        setChangedTrainingPlanName({
          id: "",
          name: "",
        }),
      );
      let sidebarItem = sidebarItems.find((item) => item.id === jobId);
      console.log("[handleLinkClick] Found sidebar item:", sidebarItem);
      const response = await axios.get(`/api/trainingPlans/${jobId}`);
      const planDetails = response.data;
      console.log("[handleLinkClick] Fetched plan from API:", planDetails);

      if (planDetails) {
        console.log(
          "[handleLinkClick] Plan found, isOnboarding:",
          planDetails.isOnboarding,
        );
        dispatch(setPlanTitle(planDetails.planTitle));

        if (planDetails.isOnboarding) {
          console.log("[handleLinkClick] Routing to onboarding");
          dispatch(clearContentAndChats());
          dispatch(clearJobId());
          dispatch(setJobId(""));
          setTimeout(() => {
            router.push(`/onboarding/${jobId}`);
          }, 50);
        } else {
          console.log("[handleLinkClick] Handling job select");
          dispatch(setSelectedJobId(jobId));
          dispatch(setSelectedPlan(planDetails));
          await dispatch(
            handleJobSelect({
              item: {
                id: jobId,
                name: sidebarItem?.name || planDetails.planTitle || "Chat",
                type: "plan",
                lastUpdated:
                  sidebarItem?.lastUpdated ||
                  new Date(
                    planDetails.lastUpdated || planDetails.dateAdded,
                  ).toISOString(),
                isOnboarding: planDetails.isOnboarding,
              },
              navigate: router.push,
            }),
          );
        }
        dispatch(setIsOpen(false));
      } else {
        console.error(`[handleLinkClick] No plan found for jobId: ${jobId}`);
      }
    } catch (error) {
      console.error("[handleLinkClick] Error:", error);
    }
  };

  const handleCloseConfirmationDialog = () => {
    setIsConfirmationDialogOpen(false);
  };

  const handleCloseFormDialog = () => {
    dispatch(setIsSidebarModalOpen(false));
    setIsFormDialog(false);
  };

  const handleOpenConfirmationDialog = async (jobId: string) => {
    setToDeleteJobId(jobId);
    dispatch(setIsSidebarModalOpen(true));
    setIsConfirmationDialogOpen(true);
    setIsMenuOpen(false);
  };

  useEffect(() => {
    if (pathname === "/" || pathname === "/calendar") {
      console.log(
        "[Sidebar useEffect] Setting selectedJobId to null because pathname is:",
        pathname,
      );
      dispatch(setSelectedJobId(null));
    }
  }, [pathname, dispatch]);

  useEffect(() => {
    if (selectedJobId && userData?.user_id) {
      console.log(
        "[Sidebar] Refreshing sidebar items after job selection change",
      );
      dispatch(
        fetchTrainingPlans({
          userId: userData.user_id,
          limit: 20,
          skip: 0,
        }),
      );
    }
  }, [selectedJobId, userData?.user_id, dispatch]);

  const workspaces = [
    { id: "ws1", name: "Athlea", type: "Personal" },
    { id: "ws2", name: "Athlea Personal", type: "Personal" },
  ];
  const currentWorkspace = workspaces[0];

  return (
    <ShadcnSidebar collapsible="icon">
      <SidebarHeader>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="h-auto w-full justify-start gap-2 px-2 text-left"
            >
              <div className="bg-gray-200 dark:bg-gray-700 flex h-8 w-8 items-center justify-center rounded">
                <FolderOpen className="h-5 w-5" />
              </div>
              <div className="flex flex-col items-start group-data-[state=collapsed]:hidden">
                <span className="truncate text-sm font-medium">
                  {currentWorkspace.name}
                </span>
                <span className="text-xs text-muted-foreground">
                  {currentWorkspace.type}
                </span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="start">
            <DropdownMenuLabel>Workspaces</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {workspaces.map((ws) => (
              <DropdownMenuItem
                key={ws.id}
                onSelect={() => console.log(`Selected workspace ${ws.id}`)}
              >
                {ws.name}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem>Create Workspace</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="ghost"
          className="mt-2 w-full justify-start gap-3 px-3 group-data-[state=collapsed]:h-8 group-data-[state=collapsed]:w-8 group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:p-0 group-data-[state=collapsed]:px-2"
          onClick={() => {
            dispatch(setNeedInitialMessages(false));
            dispatch(setInitialMessage(null));
            dispatch(setIsHomeInputSubmitted(false));

            if (userData?.user_id) {
              dispatch(
                handleCreateNewChatClick({
                  user_id: userData.user_id,
                  navigate: router.push,
                }),
              );
            } else {
              dispatch(
                handlePencilClick({
                  navigate: router.push,
                  setIsAnimationMessageExpanded,
                }),
              );
            }

            dispatch(setChatOption(""));
          }}
        >
          <div className="p-1 group-data-[state=collapsed]:p-0">
            <Plus className="h-5 w-5" />
          </div>
          <span className="truncate group-data-[state=collapsed]:hidden">
            New Chat
          </span>
        </Button>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="group-data-[state=collapsed]:hidden">
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navItems.map((item) => {
                const Icon = item.icon;
                return (
                  <SidebarMenuItem
                    key={item.name}
                    data-active={pathname === item.href}
                    isActive={pathname === item.href}
                  >
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.href}
                    >
                      <Link href={item.href}>
                        <Icon className="h-5 w-5" />
                        <span className="group-data-[state=collapsed]:hidden">
                          {item.name}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <div className="px-3 py-2 group-data-[state=collapsed]:hidden">
          {Object.entries(groupedPlans).map(
            ([group, plans]) =>
              plans.length > 0 && (
                <div key={group} className="mb-3">
                  <div className="text-gray-500 dark:text-gray-400 mb-1 text-xs font-medium uppercase">
                    {group}
                  </div>
                  <SidebarMenu>
                    {plans
                      .slice(0, group === "Today" ? 10 : 5)
                      .map((plan: SidebarItem, idx: number) => {
                        const isSelected = selectedJobId === plan.id;
                        return (
                          <SidebarMenuItem
                            key={`${plan.id}-${idx}`}
                            data-active={isSelected}
                            isActive={isSelected}
                          >
                            <SidebarMenuButton
                              className="h-8 justify-start"
                              onClick={() => handleLinkClick(plan.id)}
                              isActive={isSelected}
                            >
                              <span className="truncate text-sm">
                                {plan.name?.slice(0, 25) || plan.id}
                                {plan.name && plan.name?.length > 24 && "..."}
                              </span>
                            </SidebarMenuButton>
                          </SidebarMenuItem>
                        );
                      })}
                  </SidebarMenu>
                </div>
              ),
          )}
          {isFetchingPlan && <Loader size="sm" className="mx-auto my-4" />}
        </div>

        <SidebarGroup>
          <SidebarGroupLabel className="group-data-[state=collapsed]:hidden">
            Coaches
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {uiCoaches.map((coach) => (
                <SidebarMenuItem key={coach.name}>
                  <SidebarMenuButton
                    className="justify-start"
                    onClick={() => {
                      if (userData?.user_id) {
                        dispatch(
                          handleCoachClick({
                            coachName: coach.name,
                            user_id: userData.user_id,
                            navigate: router.push,
                          }),
                        );
                      } else {
                        console.warn(
                          "User not logged in, cannot start coach chat.",
                        );
                      }
                    }}
                  >
                    <div
                      className={`flex h-5 w-5 items-center justify-center rounded-sm p-0.5 ${coach.color} group-data-[state=collapsed]:h-6 group-data-[state=collapsed]:w-6`}
                    >
                      <ReactSVG
                        src={`/images/icon/${coach.name}-Single.svg`}
                        className="h-5 w-5"
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                        beforeInjection={(svg) => {
                          svg.setAttribute("width", "100%");
                          svg.setAttribute("height", "100%");
                          svg.querySelectorAll("path").forEach((path) => {
                            path.setAttribute("fill", "#ffffff");
                          });
                        }}
                      />
                    </div>
                    <span className="truncate group-data-[state=collapsed]:hidden">
                      {coach.name}
                    </span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-start gap-2 group-data-[state=collapsed]:h-8 group-data-[state=collapsed]:w-8 group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:p-0 group-data-[state=collapsed]:px-2"
            >
              <List className="h-5 w-5" />
              <span className="truncate group-data-[state=collapsed]:hidden">
                Tools
              </span>
              <ChevronDown className="ml-auto h-4 w-4 group-data-[state=collapsed]:hidden" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="mb-2 w-56 p-1" side="top" align="start">
            <div className="space-y-1">
              {profileMenuItems.map((item) => (
                <Button
                  key={item.label}
                  variant="ghost"
                  className="h-8 w-full justify-start gap-2 text-sm"
                  onClick={() => item.action(router)}
                >
                  <item.icon className="h-4 w-4" />
                  {item.label}
                </Button>
              ))}
            </div>
          </PopoverContent>
        </Popover>

        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              className="h-12 w-full justify-start gap-2 group-data-[state=collapsed]:h-8 group-data-[state=collapsed]:w-8 group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:p-0 group-data-[state=collapsed]:px-2"
            >
              <div className="flex h-6 w-6 items-center justify-center rounded bg-purple-500">
                <span className="text-xs font-medium text-white">
                  {userData.given_name
                    ? userData.given_name[0]
                    : userData.family_name
                      ? userData.family_name[0]
                      : "A"}
                </span>
              </div>
              <div className="flex flex-col items-start group-data-[state=collapsed]:hidden">
                <span className="truncate text-sm font-medium">
                  {userData.given_name} {userData.family_name}
                </span>
                <span className="max-w-[160px] truncate text-xs text-muted-foreground">
                  {userData.email}
                </span>
              </div>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="ml-4 w-60 p-0" align={"start"} side="top">
            <div className="p-2">
              <div
                className="flex cursor-pointer flex-col px-2 py-2"
                onClick={() => router.push("/profile")}
              >
                <div className="flex flex-row text-sm font-medium text-black dark:text-white">
                  {userData.given_name} {userData.family_name}
                </div>
                <div className="max-w-[180px] truncate text-xs text-muted-foreground">
                  {userData.email}
                </div>
              </div>
            </div>
            <hr className="border-border" />
            <div className="p-1">
              <Button
                variant="ghost"
                className="h-8 w-full justify-start gap-2 text-sm"
                onClick={() => router.push("/profile")}
              >
                <UserCircle className="h-4 w-4" /> Profile
              </Button>
            </div>
            <hr className="border-border" />
            <div className="p-1">
              <Button
                variant="ghost"
                className="h-8 w-full justify-start gap-2 text-sm"
                onClick={() => {
                  (async () => {
                    await signOut(auth);
                    localStorage.removeItem("homeUSer");
                    setTimeout(() => {
                      router.push("/auth/home");
                    }, 50);
                  })();
                }}
              >
                Log Out
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </SidebarFooter>

      {isConfirmationDialogOpen && (
        <ConfirmationDialog
          open={isConfirmationDialogOpen}
          onClose={handleCloseConfirmationDialog}
          onConfirm={handleDelete}
          title="Confirm Deletion"
          description="Are you sure you want to delete this training plan? This action cannot be undone."
        />
      )}
      {isFormDialog && (
        <FormDialog
          open={isFormDialog}
          onClose={handleCloseFormDialog}
          onConfirm={handleUpdateName}
          title="Rename Plan"
          description="Enter a new name for the training plan."
        />
      )}
      {isShareDialog && (
        <ShareLinkModal
          isOpenModal={isShareDialog}
          isHeaderAccess={false}
          handleIsCloseModal={handleCloseShareModal}
          jobId={toDeleteJobId || ""}
        />
      )}
      {isInviteModalOpen && (
        <InviteModal
          isInviteModalOpen={isInviteModalOpen}
          setisInviteModalOpen={setisInviteModalOpen}
        />
      )}
    </ShadcnSidebar>
  );
};

export default Sidebar;
