"use client";
import {
  clearTempNoteText,
  clearTempTaskText,
  deleteNote,
  deleteTaskFromServer,
  handleAddTask,
  handleMergePreviewtask,
  loadTasks,
  selectDate,
  setDrawerOpen,
  setExpanded,
  setHasUnsavedChanges,
} from "@/store/slices/calendarSlice";
import dayjs, { Dayjs } from "dayjs";
import weekday from "dayjs/plugin/weekday";
import weekOfYear from "dayjs/plugin/weekOfYear"; // ✅ Required for `.week()`
import isoWeek from "dayjs/plugin/isoWeek";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import RunningWorkoutOverview from "@/components/Overviews/RunningWorkoutOverview";
import StrengthWorkoutOverview from "@/components/Overviews/StrengthWorkoutOverview";
import { AppDispatch, RootState } from "@/store/store";
import {
  ChevronLeft,
  ChevronRight,
  Loader,
  Clock,
  MessageSquare,
  Trash2,
  ChevronLeftIcon,
  ChevronRightIcon,
  Apple,
  X,
  ChevronUp,
  ChevronDown,
  MessageCircle,
} from "lucide-react";
import NutritionTask from "../Chat/Sessions/NutritionTask";
import CyclingGraph from "../Graphs/CyclingGraph";
import RunningGraph from "../Graphs/RunningGraph";
import StrengthGraph from "../Graphs/StrengthGraph";
import WorkoutOverview from "../Overviews/CyclingWorkoutOverview";
import axios from "axios";
import CyclingTask from "../Chat/Sessions/CyclingTask";
import RunningTask from "../Chat/Sessions/RunningTask";
import StrengthTask from "../Chat/Sessions/StrengthTask";
import NutritionText from "../Chat/TaskText/NutritionText";
import { useViewMode } from "@/hooks/useViewMode";
import CyclingText from "../Chat/TaskText/CyclingText";
import RunningText, { RunningData } from "../Chat/TaskText/RunningText";
import StrengthText, { StrengthData } from "../Chat/TaskText/StrengthText";
import RecoveryText from "../Chat/TaskText/RecoveryText";
import PhaseGraph from "../Graphs/PhaseGraph";
import { renderTitle } from "@/utils/renderTitle";
import {
  Draggable,
  DraggableProvided,
  DraggableStateSnapshot,
  Droppable,
  DroppableProvided,
  DroppableStateSnapshot,
} from "react-beautiful-dnd";
import Image, { StaticImageData } from "next/image";
import CyclingLogoSingle from "@/public/images/icon/Cycling-Single.svg";
import StrengthLogoSingle from "@/public/images/icon/Strength-Single.svg";
import RunningLogoSingle from "@/public/images/icon/Running-Single.svg";
import NutritionLogoSingle from "@/public/images/icon/Nutrition-Single.svg";
import RecoveryLogoSingle from "@/public/images/icon/Recovery-Single.svg";
import {
  InputBase,
  Popper,
  styled,
  Tooltip,
  tooltipClasses,
  TooltipProps,
  Button as MuiButton,
  Menu,
  MenuItem,
  Modal,
} from "@mui/material";
import DomainFilter from "./Items/DomainFilter";
import toast from "react-hot-toast";
import { handleCommentOnTask } from "@/store/slices/jobSlice";
import { v4 as uuidv4 } from "uuid";
import { addWeeklyPlanToProfile } from "@/store/slices/userSlice";
import RenderDays from "./Items/RenderDays";
import RenderWeeks from "./Items/RenderWeeks";
import { AnimatePresence, motion } from "framer-motion";
import { useMediaQuery } from "react-responsive";
import OverViewModal from "../Plans/OverViewModal";
import { Button } from "../ui/button";
import CalenderDetailsSidebar from "./CalenderDetailsSidebar";
import SessionLibrary from "../Modals/SessionLibrary";

dayjs.extend(weekday);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
interface CalendarProps {
  loading?: boolean;
}

interface Task {
  id: string;
  text: string | ParsedTask;
}

interface Macronutrients {
  protein?: string;
  carbohydrates?: string;
  fat?: string;
}

export interface ParsedTask {
  details: string;
  intensity: string;
  Phases: never[];
  total_duration: any;
  rationale: string;
  day: string;
  sessions: any;
  meals: {
    breakfast: {
      items: Array<{
        description: string;
        ingredients: Array<{
          name: string;
          quantity: string;
          unit?: string;
          calories: number;
        }>;
        preparation?: string;
        total_calories: number;
      }>;
      meal_calories: number;
    };
    morning_snack: {
      items: Array<{
        description: string;
        ingredients: Array<{
          name: string;
          quantity: string;
          unit?: string;
          calories: number;
        }>;
        preparation?: string;
        total_calories: number;
      }>;
      meal_calories: number;
    };
    lunch: {
      items: Array<{
        description: string;
        ingredients: Array<{
          name: string;
          quantity: string;
          unit?: string;
          calories: number;
        }>;
        preparation?: string;
        total_calories: number;
      }>;
      meal_calories: number;
    };
    afternoon_snack: {
      items: Array<{
        description: string;
        ingredients: Array<{
          name: string;
          quantity: string;
          unit?: string;
          calories: number;
        }>;
        preparation?: string;
        total_calories: number;
      }>;
      meal_calories: number;
    };
    dinner: {
      items: Array<{
        description: string;
        ingredients: Array<{
          name: string;
          quantity: string;
          unit?: string;
          calories: number;
        }>;
        preparation?: string;
        total_calories: number;
      }>;
      meal_calories: number;
    };
    daily_total_calories: number;
  };
  id: any;
  session_type?: string;
  activity_name?: string;
  segments?: any[];
  distance?: string;
  duration?: string;
  caloric_intake?: string;
  macronutrients?: Macronutrients;
  notes?: string;
}

const domainColors: { [key: string]: string } = {
  cycling: "#37C6C4",
  general: "#6580F4",
  strength: "#D343DB",
  running: "rgba(247, 181, 0, 0.1)",
  // "#F7B500",
  nutrition: "#63D571",
  recovery: "#FD8C5B",
  phase: "#A1A1AA", // Example color for phase
};

const domainBgColors: { [key: string]: string } = {
  cycling: "rgba(55, 198, 196, 0.1)",
  general: "rgba(101, 128, 244, 0.1)",
  strength: "rgba(211, 67, 219, 0.1)",
  running: "rgba(247, 181, 0, 0.1)",
  nutrition: "rgba(99, 213, 113, 0.1)",
  recovery: "rgba(253, 140, 91, 0.1)",
  phase: "rgba(161, 161, 170, 0.1)", // Example color for phase
};

export const domain_STYLES: any = {
  nutrition:
    "bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300",
  strength: "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300",
  recovery:
    "bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300",
  running:
    "bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300",
  general:
    "bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300",
  cycling: "bg-cyan-100 dark:bg-cyan-900 text-cyan-700 dark:text-cyan-300",
};

export const INTENSITY_STYLES = {
  low: "bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300",
  moderate: "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300",
  high: "bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300",
};

export interface WorkoutData {
  type: string;
  duration: string;
  tss: number;
  notes: string[];
  intensity: "low" | "moderate" | "high";
}

export interface Role {
  name: string;
  logo: StaticImageData;
}
const iconMapping: { [key: string]: Role } = {
  cycling: { name: "Cycling Coach", logo: CyclingLogoSingle },
  strength: { name: "Strength Coach", logo: StrengthLogoSingle },
  running: { name: "Running Coach", logo: RunningLogoSingle },
  nutrition: { name: "Nutrition Coach", logo: NutritionLogoSingle },
  recovery: { name: "Recovery Coach", logo: RecoveryLogoSingle },
  phase: { name: "Phase Graph", logo: RecoveryLogoSingle }, // Assuming you have a PhaseLogo
  general: { name: "General Coach", logo: NutritionLogoSingle },
};

const HtmlTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "white",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 320,
    fontSize: theme.typography.pxToRem(12),
    border: "1px solid #dadde9",
  },
}));

const sessionTypeMapping: { [key: string]: string } = {
  run: "running",
  running: "running",
  bike: "cycling",
  cycling: "cycling",
  strength_coach: "strength",
  strength: "strength",
  nutrition: "nutrition",
  recovery: "recovery",
  phase: "phase",
};

const sessionCoach = [
  "Cycling",
  "Strength",
  "Nutrition",
  "Recovery",
  "Running",
  // "run",
  // "cycling",
  // "bike",
  // "strength",
  // "nutrition",
  // "recovery",
  // "phase",
];

const getSessionsForLiter = (sessionType: string | undefined) => {
  return sessionTypeMapping[sessionType?.toLowerCase() ?? ""] || "general";
};

const DayDetailModal: React.FC<{
  day: any;
  onClose: () => void;
  buttonClass: string;
  modalfilteredTasks: any[];
  modalIndex: number;
  taskDate: string | null;
  setModalIndex: React.Dispatch<React.SetStateAction<number>>;
}> = ({
  day,
  onClose,
  buttonClass,
  modalfilteredTasks,
  modalIndex,
  taskDate,
  setModalIndex,
}) => {
  let parsedTask: any = null;
  if (
    modalfilteredTasks[modalIndex] &&
    typeof modalfilteredTasks[modalIndex].text === "string"
  ) {
    try {
      parsedTask = JSON.parse(modalfilteredTasks[modalIndex].text);
    } catch (error) {
      console.error("Failed to parse task.text:", error);
      return <div key={day.id}>Invalid task data</div>;
    }
  } else if (
    modalfilteredTasks[modalIndex] &&
    typeof modalfilteredTasks[modalIndex].text === "object" &&
    modalfilteredTasks[modalIndex].text !== null
  ) {
    parsedTask = modalfilteredTasks[modalIndex].text;
  }
  let duration: any = "";
  let sessionType: string = "";
  let newTime: string = "";

  if (parsedTask) {
    sessionType = getSessionsForLiter(parsedTask.session_type?.toLowerCase());
    duration = parsedTask.duration
      ? parsedTask.duration
      : parsedTask.segments !== undefined
        ? parsedTask.segments
            .filter((it: { duration: any }) => it.duration)
            .map((it: { duration: any }) => it.duration)
            .reduce((a: any, b: any) => a + b, 0)
        : "";
    newTime = modalfilteredTasks[modalIndex]
      ? duration
        ? `${modalfilteredTasks[modalIndex].time.split(":")[0]}:${duration}`
        : `${parseInt(modalfilteredTasks[modalIndex].time.split(":")[0]) + 1}:00`
      : "";
    // console.log('newtasksForDate', tasksForDate, dateString, day, parsedTask, sessionType, newTime);
  }
  console.log(
    "currentIndex",
    modalIndex,
    modalfilteredTasks[modalIndex],
    sessionType,
    parsedTask,
  );

  // if (modalfilteredTasks[modalIndex]) {
  //   <OverViewModal foundContent={modalfilteredTasks[modalIndex]} task={null} />
  //   return
  // }
  // return null
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className={`relative h-[80vh] max-h-[80vh] w-[80%] overflow-auto rounded-lg`}
      >
        <button
          style={{
            position: "absolute",
            left: "10px",
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 1000,
            background: "white",
            border: "1px solid #ddd",
            borderRadius: "50%",
            width: "30px",
            height: "30px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
          }}
          onClick={() => {
            if (modalIndex !== 0) {
              setModalIndex(modalIndex - 1);
            }
          }}
        >
          ←
        </button>
        {/* <div className="p-4 border-b flex items-center justify-between">
          <h2 className="font-semibold">{modalfilteredTasks[modalIndex] !== undefined ? modalfilteredTasks[modalIndex].date : taskDate}</h2>
          <button
            onClick={onClose}
            className={`${buttonClass} hover:bg-accent hover:text-accent-foreground`}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        <div className="px-10 py-4 space-y-4">
          {modalfilteredTasks[modalIndex] && (
            <>
              <div className="space-y-2">
                <h3 className="font-medium ml-3">Training Schedule</h3>
                <div className="bg-slate-100 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`text-xs px-2 py-1 rounded-full ${domain_STYLES[sessionType]}`}>
                      {sessionType}
                    </div>
                    <span className="text-sm text-muted-foreground">{duration}</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Test note</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium ml-3">{sessionType} Timeline</h3>
                <div className="space-y-3">
                  <div className="bg-slate-100 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Apple className="h-4 w-4" />
                      <span className="font-medium">Pre-workout Meal</span>
                      <span className="text-xs bg-blue-500/10 text-blue-600 dark:text-blue-400 px-2 py-0.5 rounded-full">
                        {sessionType === 'strength' ? '05:00' : '06:00'}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Balanced meal with complex carbs and moderate protein to fuel your {sessionType.toLowerCase()}.
                    </p>
                  </div>

                  <div className="bg-slate-100 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Apple className="h-4 w-4" />
                      <span className="font-medium">Post-workout Meal</span>
                      <span className="text-xs bg-blue-500/10 text-blue-600 dark:text-blue-400 px-2 py-0.5 rounded-full">
                        {sessionType === 'strength' ? '07:30' : '08:30'}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      High protein meal with fast-acting carbs for optimal recovery after your {sessionType.toLowerCase()}.
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div> */}
        {modalfilteredTasks[modalIndex] && (
          <OverViewModal
            foundContent={modalfilteredTasks[modalIndex]}
            task={null}
            onClose={onClose}
          />
        )}
        <button
          style={{
            position: "absolute",
            right: "10px",
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 1000,
            background: "white",
            border: "1px solid #ddd",
            borderRadius: "50%",
            width: "30px",
            height: "30px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
          }}
          onClick={() => {
            if (modalIndex < modalfilteredTasks.length - 1) {
              setModalIndex(modalIndex + 1);
            }
          }}
        >
          →
        </button>
      </div>
    </div>
  );
};

const CalendarView: React.FC<{
  // drawerOpen: string;
  // setDrawerOpen: React.Dispatch<React.SetStateAction<string>>
  currentWeek?: number;
}> = ({ currentWeek }) => {
  const { viewMode } = useViewMode();
  const {
    tasks,
    selectedDates,
    isSelected,
    expanded,
    isExpanded,
    draggingDate,
    draggingTIme,
    isDragging,
    drawerOpen,
  } = useSelector((state: RootState) => state.calendar);
  console.log("draggingDate", draggingDate, draggingTIme);
  const { userData } = useSelector((state: RootState) => state.user);
  const [isShow, setIsShow] = useState(false);
  const [loading, setLoading] = useState(true);
  const [expandedTaskId, setExpandedTaskId] = useState<string | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const [selectedCalendarType, setSelectedCalendarType] =
    useState<string>("Week");
  const [startDate, setStartDate] = useState(dayjs());
  const [selectedTask, setSelectedTasks] = useState<string | null>(null);
  const [fullCalendatDate, setFullCalendarDate] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const timeMenuRef = useRef<HTMLDivElement>(null);

  const [dates, setDates] = useState<Dayjs[]>(() => {
    const initialDates: Dayjs[] = [];
    const startOfMonth = dayjs().startOf("month");
    const endOfMonth = dayjs().endOf("month");

    for (
      let date = startOfMonth;
      date.isBefore(endOfMonth) || date.isSame(endOfMonth, "day");
      date = date.add(1, "day")
    ) {
      initialDates.push(date);
    }
    return initialDates;
  });
  const [modalWeekDays, setModalWeekDays] = useState<string[]>([]);
  const [modalfilteredTasks, setModalfilteredTasks] = useState<any[]>([]);
  const [selectedDate, setSelectedDate] = useState<any[]>([]);
  const [dateDropdown, setSetDateDropdown] = useState<string | null>(null);

  const [timeAnchorEl, settimeAnchorEl] = useState<null | HTMLElement>(null);
  const isTimeMenuopen = Boolean(timeAnchorEl);

  const handleTimeClose = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    settimeAnchorEl(null);
  };

  const handleTimeClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    settimeAnchorEl(event.currentTarget);
  };

  const handleOnClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const handleOpen = useCallback(() => {
    setIsOpen(true);
  }, []);

  const timeArr = [
    "0:00",
    "1:00",
    "2:00",
    "3:00",
    "4:00",
    "5:00",
    "6:00",
    "7:00",
    "8:00",
    "9:00",
    "10:00",
    "11:00",
    "12:00",
    "13:00",
    "14:00",
    "15:00",
    "16:00",
    "17:00",
    "18:00",
    "19:00",
    "20:00",
    "21:00",
    "22:00",
    "23:00",
  ];

  console.log("calendar selectedDate", selectedDate);

  const daysToShow = 7;
  const noteWeekDaysdates = Array.from({ length: daysToShow }, (_, i) =>
    startDate.clone().add(i, "day"),
  );
  const [noteText, setNoteText] = useState<any>(
    noteWeekDaysdates.reduce(
      (obj, date) => {
        obj[date.format("YYYY-MM-DD")] = "";
        return obj;
      },
      {} as Record<string, string>,
    ),
  );
  console.log("calendar dates", selectedDate);
  const isMediumScreen = useMediaQuery({ query: "(min-height: 850px)" });

  useEffect(() => {
    if (modalWeekDays.length > 0 && tasks) {
      let modalfilteredTasks: any[] = [];
      modalWeekDays.forEach((date) => {
        if (tasks[date] !== undefined && tasks[date].tasks.length > 0) {
          tasks[date].tasks.forEach((task) => modalfilteredTasks.push(task));
        }
      });
      modalfilteredTasks = modalfilteredTasks.filter((task) => task);
      console.log("modalfilteredTasks", modalfilteredTasks);
      setModalfilteredTasks(modalfilteredTasks);
    }
  }, [modalWeekDays]);

  const generateFullCalendar = (startDate: string, numWeeks = 52) => {
    let calendar = [];
    let start = dayjs(startDate).startOf("week"); // Align to week start

    for (let weekIndex = 0; weekIndex < numWeeks; weekIndex++) {
      let week = [];

      for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
        let day = start.add(dayIndex, "day");
        week.push({
          date: day.format("YYYY-MM-DD"),
          dayName: day.format("dddd"),
          month: day.format("MMMM"),
          weekNumber: day.week(),
          monthNumber: day.month() + 1, // Get month number (1-12)
          year: day.year(),
          isCurrentMonth: day.month() === dayjs(startDate).month(),
        });
      }

      calendar.push(week);
      start = start.add(1, "week"); // Move to next week
    }

    return calendar;
  };

  const getWeeksInMonth = (year: String, month: string) => {
    let startDate = dayjs(`${year}-${month}-01`);
    let endDate = startDate.endOf("month");
    let weeks = [];

    let currentWeekStart = startDate.startOf("week");

    while (currentWeekStart.isBefore(endDate)) {
      let currentWeekEnd = currentWeekStart.endOf("week");
      weeks.push({
        start: currentWeekStart.format("YYYY-MM-DD"),
        end: currentWeekEnd.format("YYYY-MM-DD"),
      });
      currentWeekStart = currentWeekStart.add(1, "week");
    }

    return weeks;
  };

  const groupCalendarByMonth = (numWeeks = 52) => {
    const calendarData = generateFullCalendar("2025-01-01", numWeeks);
    let groupedData: any = {};

    calendarData.forEach((week) => {
      week.forEach((day) => {
        const monthKey = `${day.year}-${day.monthNumber}-${day.month}`; // Format: "2024-January"

        if (!groupedData[monthKey]) {
          groupedData[monthKey] = [];
        }

        if (
          !groupedData[monthKey].some(
            (w: any) => w.weekNumber === day.weekNumber,
          )
        ) {
          groupedData[monthKey].push({
            weekNumber: day.weekNumber,
            days: week.filter((d) => d.month === day.month),
          });
        }
      });
    });
    delete groupedData["2024-12-December"];
    setFullCalendarDate(groupedData);
  };

  useEffect(() => {
    if (fullCalendatDate === null) {
      groupCalendarByMonth();
    }
  }, [fullCalendatDate]);
  console.log("getYearlyWeeks", fullCalendatDate);

  const buttonClass =
    "h-9 rounded-md px-3 inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0";

  console.log("calendar tasks", tasks);
  const [filterSession, setFilterSession] = useState<string>("");
  const [loadedFirstTime, setLoadedFirstTime] = useState<boolean>(false);
  const [weekDates, setWeekDates] = useState<Dayjs[]>(() => {
    const initialDates: Dayjs[] = [];

    const startOfWeek = startDate.startOf("week");
    // console.log('---startDate---',dayjs().week(14));

    for (let i = 0; i < 7; i++) {
      initialDates.push(startOfWeek.add(i, "day"));
    }
    return initialDates;
  });
  // console.log('---weekDates---',weekDates);

  const [newParsedTask, setNewParsedTask] = useState<any>(null);
  const [day, setDay] = useState<Dayjs>(startDate);
  const [modalIndex, setModalIndex] = useState<number>(0);

  function getStartDateOfWeek(year = 2025, weekNumber = 1) {
    return dayjs().year(year).isoWeek(weekNumber).startOf("isoWeek");
  }

  function getDatesOfWeek(year = 2025, weekNumber = 1) {
    const initialDates: Dayjs[] = [];
    let startOfWeek = dayjs().year(year).week(weekNumber).startOf("week");
    for (let i = 0; i < 7; i++) {
      initialDates.push(startOfWeek.add(i, "day"));
    }
    return initialDates;
    // return Array.from({ length: 7 }, (_, i) => startOfWeek.add(i, 'day').format('YYYY-MM-DD'));
  }
  useEffect(() => {
    if (currentWeek) {
      const weekdates = getDatesOfWeek(2025, currentWeek);
      const mstartDate = getStartDateOfWeek(2025, currentWeek);
      // console.log('---weekdates---',weekdates,mstartDate);
      setWeekDates(weekdates);
      setStartDate(mstartDate);
    }
  }, [currentWeek]);

  useEffect(() => {
    if (selectedTask && modalfilteredTasks.length > 0) {
      // alert(`${modalfilteredTasks.length}, ${selectedTask}`)
      const modalIndex = modalfilteredTasks.findIndex(
        (task) => task.date === selectedTask,
      );
      // console.log('currentIndex', modalfilteredTasks, modalIndex, modalfilteredTasks[modalIndex], newParsedTask);
      setModalIndex(modalIndex);
    }
  }, [selectedTask, modalfilteredTasks]);

  const [clockTime, setclockTime] = useState(() => {
    let clockTimesHourly = [];

    for (let hour = 0; hour < 24; hour++) {
      let formattedTime = `${hour.toString().padStart(2, "0")}:00`;
      clockTimesHourly.push(formattedTime);
    }

    return clockTimesHourly;
  });

  const scrollRef = useRef<HTMLDivElement>(null);
  const [selectedSessionType, setSelectedSessionType] = useState("all");
  const filterArray: any = [
    "All Sessions",
    "Run",
    "Bike",
    "Strength and Conditioning",
    "Nutrition",
  ];
  const [tooltipId, setTooltipId] = useState<any>(null);
  console.log("tooltipId", tooltipId);

  const handleClose = () => {
    setNewParsedTask(null);
    setModalWeekDays([]);
    setModalfilteredTasks([]);
    setModalIndex(0);
  };

  const handleEscape = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    },
    [handleClose],
  );

  useEffect(() => {
    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [handleEscape]);

  useEffect(() => {
    const loadTasksData = async () => {
      if (userData?.user_id && !loadedFirstTime) {
        setLoading(true);
        try {
          await dispatch(loadTasks(userData.user_id)).unwrap();
          setLoadedFirstTime(true);
        } catch (error) {
          console.error("Failed to load tasks:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadTasksData();
  }, [dispatch, userData, loadedFirstTime]);

  useEffect(() => {
    setTimeout(() => {
      setIsShow(true);
    }, 500);
  }, []);

  const loadMoreDays = (direction: "up" | "down") => {
    const newStartDate =
      direction === "down"
        ? startDate.add(1, "month").startOf("month")
        : startDate.subtract(1, "month").startOf("month");

    setStartDate(newStartDate);

    const newDates: Dayjs[] = [];
    const endOfMonth = newStartDate.endOf("month");

    for (
      let date = newStartDate;
      date.isBefore(endOfMonth) || date.isSame(endOfMonth, "day");
      date = date.add(1, "day")
    ) {
      newDates.push(date);
    }
    setDates(newDates);
  };

  const loadMoreWeeks = (direction: "up" | "down") => {
    const newStartDate =
      direction === "down"
        ? startDate.add(1, "week").startOf("week")
        : startDate.subtract(1, "week").startOf("week");

    setStartDate(newStartDate);

    const newWeekDates: Dayjs[] = [];

    for (let i = 0; i < 7; i++) {
      newWeekDates.push(newStartDate.add(i, "day"));
    }
    setWeekDates(newWeekDates);
  };

  const loadMoreDay = (direction: "up" | "down") => {
    const newDay =
      direction === "down"
        ? startDate.add(1, "day")
        : startDate.subtract(1, "day");

    setStartDate(newDay);
    setDay(newDay);
  };

  const handleDateClick = (
    date: Dayjs,
    slot: "AM" | "PM",
    taskId: string | null,
  ) => {
    const dateString = date.format("YYYY-MM-DD");
    if (
      expanded &&
      expanded.date === dateString &&
      expanded.slot === slot &&
      expanded.taskId === taskId
    ) {
      dispatch(setExpanded(null));
    } else {
      dispatch(
        setExpanded({ date: dateString, slot, taskId: taskId ?? undefined }),
      );
    }
    dispatch(selectDate({ date: dateString, slot }));
  };

  const handleCalendarDisplayType = (type: string) => {
    setSelectedCalendarType(type);

    if (type === "Day") {
      setDay(startDate);
    } else if (type === "Week") {
      const newWeekDates: Dayjs[] = [];
      const startOfWeek = startDate.startOf("week");

      for (let i = 0; i < 7; i++) {
        newWeekDates.push(startOfWeek.add(i, "day"));
      }
      setWeekDates(newWeekDates);
    } else if (type === "Month") {
      const newDates: Dayjs[] = [];
      const startOfMonth = startDate.startOf("month");
      const endOfMonth = startDate.endOf("month");

      for (
        let date = startOfMonth;
        date.isBefore(endOfMonth) || date.isSame(endOfMonth, "day");
        date = date.add(1, "day")
      ) {
        newDates.push(date);
      }
      setDates(newDates);
    }
  };

  const handleTodayClick = () => {
    const today = dayjs();
    setStartDate(today);

    if (selectedCalendarType === "Week") {
      const startOfWeek = today.startOf("week");
      const newWeekDates: Dayjs[] = [];
      for (let i = 0; i < 7; i++) {
        newWeekDates.push(startOfWeek.add(i, "day"));
      }
      setWeekDates(newWeekDates);
    } else if (selectedCalendarType === "Month") {
      const startOfMonth = today.startOf("month");
      const endOfMonth = today.endOf("month");
      const newDates: Dayjs[] = [];
      for (
        let date = startOfMonth;
        date.isBefore(endOfMonth) || date.isSame(endOfMonth, "day");
        date = date.add(1, "day")
      ) {
        newDates.push(date);
      }
      setDates(newDates);
    }
  };
  const [filteredSessionTasks, setFilteredSessionTasks] = useState<any[]>([]);
  let [filterCount, setFilterCount] = useState<number>(0);
  const handleFilterSession = (sessionType: string, date: string) => {
    if (filterCount < 1) {
      setLoading(true);
    }
    const getSessionType = (session_type: any) => {
      switch (session_type) {
        case "run":
        case "running":
          return "running";
        case "bike":
        case "cycling":
          return "cycling";
        case "nutrition":
          return "nutrition";
        case "strength":
        case "strength_coach":
          return "strength";
        case "recovery":
          return "recovery";
        default:
          return "general";
      }
    };
    setFilterSession(sessionType);
    const filteredTasks: any[] = Object.entries(tasks).map(
      ([date, calendarData]) => {
        let newData = JSON.parse(JSON.stringify(calendarData));
        newData.tasks = calendarData.tasks.filter((task: any) => {
          let parsedTask;
          if (typeof task.text === "string") {
            try {
              parsedTask = JSON.parse(task.text);
            } catch (error) {
              console.error("Failed to parse task.text:", error);
              return null;
            }
          } else if (typeof task.text === "object" && task.text !== null) {
            parsedTask = task.text;
          } else {
            console.error("Invalid task.text format:", task.text);
            return null;
          }
          let session_type = getSessionType(parsedTask.session_type);
          // parsedTask.session_type === 'run' ? 'running' :

          console.log(
            "calendarsession_type",
            session_type,
            sessionType.toLowerCase(),
          );

          return session_type === sessionType.toLowerCase();
        });
        return { [date]: newData };
      },
    );
    // console.log('filteredTasks', filteredTasks);
    setFilteredSessionTasks(filteredTasks);
    setFilterCount((filterCount += 1));
    setTimeout(() => {
      setLoading(false);
    }, 10);
  };

  const clearFilter = () => {
    setFilterSession("");
    setFilteredSessionTasks([]);
  };

  const renderGraph = (task: Task | ParsedTask | any, isExpanded: boolean) => {
    let parsedTask: ParsedTask;
    let index = 0;
    // console.log("task", task);

    if (typeof task === "string") {
      try {
        parsedTask = JSON.parse(task);
      } catch (error) {
        console.error("Failed to parse task string:", error);
        return <div>Invalid task data</div>;
      }
    } else if ("text" in task && typeof task.text === "string") {
      try {
        parsedTask = JSON.parse(task.text);
      } catch (error) {
        console.error("Failed to parse task.text:", error);
        return <div>Invalid task data</div>;
      }
    } else if (
      "text" in task &&
      typeof task.text === "object" &&
      task.text !== null
    ) {
      parsedTask = task.text;
    } else if (typeof task === "object" && task !== null) {
      parsedTask = task as ParsedTask;
    } else {
      return <div>Invalid task data</div>;
    }

    // console.log("parsedTask", parsedTask);

    if (parsedTask.session_type) {
      return renderSessionType(parsedTask, isExpanded, index);
    } else if (parsedTask.activity_name) {
      return renderGenericAction(parsedTask);
    } else {
      return <div>{JSON.stringify(parsedTask)}</div>;
    }
  };

  const renderSessionType = (
    parsedTask: ParsedTask,
    isExpanded: boolean,
    index: number,
  ) => {
    // console.log("parsed task", parsedTask);
    switch (parsedTask.session_type?.toLowerCase()) {
      case "bike":
      case "cycling":
        if (selectedCalendarType == "Day") {
          return (
            <div className="mx-2">
              <div>
                <h3 className="text-xl font-semibold dark:text-white">
                  Cycling
                </h3>
              </div>
              <CyclingGraph segments={parsedTask.segments || []} />
              {isExpanded && (
                <WorkoutOverview
                  key={`${parsedTask.id}_workout_overview`}
                  segments={parsedTask.segments || []}
                  duration={parsedTask.distance}
                />
              )}
            </div>
          );
        } else {
          return (
            <CyclingText
              data={{
                id: parsedTask.id,
                session_type: parsedTask.session_type,
                duration: parsedTask.duration,
                segments: parsedTask.segments,
              }}
              index={index}
            />
          );
        }
      case "run":
      case "running":
        if (selectedCalendarType == "Day") {
          return (
            <div className="mx-2">
              <div>
                <h3 className="text-xl font-semibold dark:text-white">
                  Running
                </h3>
              </div>
              <RunningGraph segments={parsedTask.segments || []} />
              {isExpanded && (
                <RunningWorkoutOverview
                  key={`${parsedTask.id}_running_workout_overview`}
                  segments={parsedTask.segments || []}
                  distance={parsedTask.distance}
                />
              )}
            </div>
          );
        } else {
          return (
            <RunningText
              data={
                {
                  id: parsedTask.id ?? "",
                  session_type: parsedTask.session_type ?? "",
                  day: parsedTask.day ?? "",
                  distance: parsedTask.distance,
                  segments: parsedTask.segments ?? [],
                  rationale: parsedTask.rationale ?? "",
                } as RunningData
              }
              index={index}
            />
          );
        }
      case "strength":
        if (selectedCalendarType == "Day") {
          return (
            <div className="mx-2">
              <div className="flex flex-row justify-between align-bottom">
                <h3 className="text-xl font-semibold">
                  Strength and Conditioning
                </h3>
                <p>{parsedTask.duration}</p>
              </div>
              <StrengthGraph
                key={`${parsedTask.id}_strength_graph`}
                segments={parsedTask.segments || []}
                totalDuration={parsedTask.duration}
              />
              {isExpanded && (
                <StrengthWorkoutOverview
                  key={`${parsedTask.id}_strength_workout_overview`}
                  segments={parsedTask.segments || []}
                  duration={parsedTask.duration || "0"}
                />
              )}
            </div>
          );
        } else {
          const strengthData: StrengthData = {
            id: parsedTask.id ?? "",
            session_type: parsedTask.session_type ?? "strength", // Provide a default value
            day: parsedTask.day,
            duration: parsedTask.duration,
            intensity: parsedTask.intensity,
            segments: parsedTask.segments,
            notes: parsedTask.notes,
            rationale: parsedTask.rationale,
          };
          return <StrengthText data={strengthData} index={index} />;
        }
      case "nutrition":
        if (parsedTask.sessions && parsedTask.sessions.length > 0) {
          const session = parsedTask.sessions[0]; // Assuming we want the first session
          const nutritionData = {
            session_type: parsedTask.session_type,
            day: parsedTask.day || "",
            rationale: parsedTask.rationale || "",
            meals: {
              day: parsedTask.day || "",
              breakfast: session.breakfast,
              morning_snack: session.morning_snack,
              lunch: session.lunch,
              afternoon_snack: session.afternoon_snack,
              dinner: session.dinner,
              daily_total_calories: session.daily_total_calories,
            },
          };
          return (
            <div className="mx-2">
              <NutritionText task={nutritionData} index={index} />
            </div>
          );
        } else {
          // Assuming parsedTask.meals already has the correct structure
          const nutritionData = {
            session_type: parsedTask.session_type,
            day: parsedTask.day || "",
            rationale: parsedTask.rationale || "",
            meals: parsedTask.meals,
          };
          return (
            <div className="mx-2">
              <NutritionText task={nutritionData} index={index} />;
            </div>
          );
        }
      case "recovery":
        const recoveryData = {
          session_type: parsedTask.session_type,
          day: parsedTask.day || "",
          rationale: parsedTask.rationale || "",
          duration: parsedTask.total_duration,
          segments: parsedTask.segments || [],
        };
        return (
          <div className="mx-2">
            <RecoveryText task={recoveryData} index={index} />;
          </div>
        );
      case "phase":
        return <PhaseGraph phases={parsedTask.Phases || []} />;
      default:
        return renderGenericAction(parsedTask);
    }
  };

  const renderGenericAction = (action: any) => {
    console.log("action", action);
    return (
      <div className="mb-2 flex w-full cursor-pointer overflow-hidden rounded-md">
        <div className="w-full px-3 py-3">
          <h4 className="font-bold">
            {action.activity_name || "Unnamed Activity"}
          </h4>
          {action.activity_details ? (
            <div className="mb-2 mr-4">
              <span className="font-semibold">Description: </span>
              {action.activity_details.description || "No description provided"}
            </div>
          ) : action.description ? (
            <div className="mb-2 mr-4">
              <span className="font-semibold">Description: </span>
              {action.description}
            </div>
          ) : null}
          <p className="mt-2">
            {action.activity_rationale ||
              action.rationale ||
              action.activity_details?.description ||
              "No rationale provided"}
          </p>
          {action.source && (
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              Source:{" "}
              {action.source.url && action.source.url !== "N/A" ? (
                <a
                  href={action.source.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  {action.source.name}
                </a>
              ) : (
                <span>{action.source.name}</span>
              )}
            </p>
          )}
          <p className="mt-2 italic text-slate-800">
            * {action.tip || "No tip provided"}
          </p>
        </div>
      </div>
    );
  };

  const handleDeleteTask = async (date: string, taskId: string) => {
    try {
      const resultAction = await dispatch(
        deleteTaskFromServer({ userId: userData.user_id, date, taskId }),
      );
      if (deleteTaskFromServer.fulfilled.match(resultAction)) {
        console.log("Task deleted successfully:", taskId);
        // Task is already removed from Redux state in the thunk
      } else if (deleteTaskFromServer.rejected.match(resultAction)) {
        console.error("Failed to delete task:", resultAction.payload);
        // You can add a toast notification here to inform the user
        toast.error(`Failed to delete task: ${resultAction.payload}`);
      }
    } catch (error) {
      console.error("Error in handleDeleteTask:", error);
      toast.error("An unexpected error occurred while deleting the task");
    }
  };

  const handleComment = (
    task: any,
    inputText: string,
    dateString: string,
    index: number,
    sessionType: string,
    time: string,
  ) => {
    const parsedTask = JSON.parse(JSON.stringify(tasks));
    let foundCalendarTask = parsedTask[dateString];
    console.log("foundCalendarTask", foundCalendarTask, task);
    let newTasks: any[] =
      foundCalendarTask !== undefined ? foundCalendarTask["tasks"] : [];
    const itemToadd = {
      id: uuidv4(),
      text: {
        activity_name: inputText,
        session_type: sessionType,
        isTemp: true,
      },
      date: dateString,
      time,
    };
    console.log("foundCalendarTask before", foundCalendarTask);
    newTasks.splice(index + 1, 0, itemToadd);
    if (foundCalendarTask !== undefined) {
      foundCalendarTask["tasks"] = newTasks;
    }
    console.log("foundCalendarTask after", foundCalendarTask);

    dispatch(handleAddTask(parsedTask));
    const previewTask = { [dateString]: parsedTask[dateString] };
    dispatch(handleMergePreviewtask(previewTask));
    setTaskText("");
    dispatch(
      handleCommentOnTask({
        task: task || itemToadd,
        inputText,
        user_id: userData.user_id,
      }),
    );
  };

  const handleSubmitNote = useCallback(
    (dateString: string, value: string) => {
      const newObj = JSON.parse(JSON.stringify(tasks));
      if (newObj[dateString] !== undefined) {
        newObj[dateString]["notes"] = newObj[dateString]["notes"]
          ? [...newObj[dateString]["notes"], { noteText: value, time: "" }]
          : [{ noteText: value, time: "" }];
      } else {
        newObj[dateString] = {
          tasks: [],
          notes: [{ noteText: value, time: "" }],
        };
      }
      dispatch(handleAddTask(newObj));
      dispatch(setHasUnsavedChanges(true)); // Set unsaved changes flag
      setNoteText({
        ...noteText,
        [dateString]: "",
      });
      dispatch(clearTempTaskText()); // Clear tempTaskText here
      dispatch(clearTempNoteText()); // Clear tempNoteText here if applicable
    },
    [tasks, noteText, dispatch],
  );

  const handleChooseTime = useCallback(
    (
      event: React.MouseEvent<HTMLLIElement, MouseEvent>,
      dateString: string,
      time: string,
      noteIndex: number,
    ) => {
      event.stopPropagation();
      const newObj = JSON.parse(JSON.stringify(tasks));
      if (newObj[dateString] !== undefined) {
        newObj[dateString]["notes"] = newObj[dateString]["notes"]
          ? newObj[dateString]["notes"].map((nt: any, idx: number) => {
              if (idx === noteIndex) {
                nt["time"] = time;
                return nt;
              } else return nt;
            })
          : [{ noteText: "", time }];
      } else {
        newObj[dateString] = { tasks: [], notes: [{ noteText: "", time }] };
      }
      dispatch(handleAddTask(newObj));
      dispatch(setHasUnsavedChanges(true)); // Set unsaved changes flag
      settimeAnchorEl(null);
    },
    [tasks, dispatch],
  );

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [taskText, setTaskText] = useState<string>("");
  const popperRef = useRef<HTMLDivElement>(null);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popper" : undefined;

  const [clickedtaskDetails, setClickedTaskDetails] = useState<any>(null);
  console.log("clickedtaskDetails", clickedtaskDetails);

  const handlePopperClick = (
    event: React.MouseEvent<HTMLElement>,
    task: any,
    dateString: string,
    index: number,
    sessionType: string,
    time: string,
  ) => {
    event.stopPropagation();
    setClickedTaskDetails({
      task,
      dateString,
      index,
      sessionType,
      time,
    });
    // setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleCommentClick = useCallback(
    (e: React.MouseEvent | React.KeyboardEvent) => {
      console.log(
        "handleCommentClick date",
        noteText[clickedtaskDetails.dateString],
        clickedtaskDetails.dateString,
        clickedtaskDetails.index,
        clickedtaskDetails.sessionType,
        clickedtaskDetails.time,
      );
      e.stopPropagation();
      setAnchorEl(null);
      handleComment(
        clickedtaskDetails.task,
        noteText[clickedtaskDetails.dateString],
        clickedtaskDetails.dateString,
        clickedtaskDetails.index,
        clickedtaskDetails.sessionType,
        clickedtaskDetails.time,
      );
    },
    [clickedtaskDetails, noteText],
  );

  const handleSelectDomain = (domain: string, date: string) => {
    // const newData = [...selectedDate].filter(it => it.date !== date)
    setSelectedDate(
      selectedDate.map((it) => {
        if (it.date === date) {
          it.domain = domain;
          return it;
        } else return it;
      }),
    );
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popperRef.current &&
        !popperRef.current.contains(event.target as Node)
      ) {
        setAnchorEl(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const dragableTasks = (
    dateString: string,
    idx: number,
    task: any,
    time: string,
  ) => {
    let parsedTask: any;
    if (typeof task.text === "string") {
      try {
        parsedTask = JSON.parse(task.text);
      } catch (error) {
        console.error("Failed to parse task.text:", error);
        return <div key={task.id}>Invalid task data</div>;
      }
    } else if (typeof task.text === "object" && task.text !== null) {
      parsedTask = task.text;
    } else {
      console.error("Invalid task.text format:", task.text);
      return <div key={task.id}>Invalid task data</div>;
    }
    const sessionType = getSessionsForLiter(
      parsedTask.session_type?.toLowerCase(),
    );
    console.log("calendar parsedTask.id", task.id, parsedTask);
    let duration = parsedTask.duration
      ? parsedTask.duration
      : parsedTask.segments !== undefined
        ? parsedTask.segments
            .filter((it: { duration: any }) => it.duration)
            .map((it: { duration: any }) => it.duration)
            .reduce((a: any, b: any) => a + b, 0)
        : "";
    const newTime = duration
      ? `${time.split(":")[0]}:${duration}`
      : `${parseInt(time.split(":")[0]) + 1}:00`;
    // const draggableId = `draggable-task_${dateString}-${idx}`;
    const draggableId = `draggable-task_${task.id || idx}_${sessionType}#${dateString}#${idx}`;

    return (
      <Draggable
        key={idx}
        draggableId={draggableId}
        index={idx}
        isDragDisabled={selectedCalendarType !== "Week"}
      >
        {(
          providedDraggable: DraggableProvided,
          snapshotDraggable: DraggableStateSnapshot,
        ) => (
          <div
            ref={providedDraggable.innerRef}
            {...providedDraggable.draggableProps}
            {...providedDraggable.dragHandleProps}
          >
            <HtmlTooltip
              // onOpen={() => setTooltipId(idx)}
              open={tooltipId ? tooltipId === draggableId : false}
              onClose={() => setTooltipId(null)}
              title={
                <React.Fragment>
                  <div
                    className="thin-scrollbar flex-grow overflow-auto"
                    style={{ height: 360, width: 320 }}
                  >
                    {renderGraph(parsedTask, false)}
                  </div>
                </React.Fragment>
              }
            >
              <div
                key={idx}
                className="mb-2 flex w-30 cursor-pointer flex-col p-2 transition-all duration-300"
                style={{
                  // height: 80,
                  borderRadius: "5px",
                  textAlign: "center",
                  borderWidth: 3,
                  borderColor: domainColors[sessionType || "general"],
                  backgroundColor: domainBgColors[sessionType || "general"],
                }}
                onMouseEnter={() => setTooltipId(draggableId)}
                onMouseLeave={() => {
                  // setTooltipId(null)
                }}
                onClick={() => {
                  setNewParsedTask(parsedTask);
                  if (
                    sessionType != "nutrition" &&
                    sessionType != "recovery" &&
                    sessionType != "phase"
                  ) {
                    setSelectedTasks(`${dateString}`);
                  }
                }}
              >
                <div className="flex">
                  <div
                    className="mr-2 flex items-center justify-center p-1"
                    style={{
                      backgroundColor: domainColors[sessionType || "general"],
                      textAlign: "center",
                      fontSize: 12,
                      borderRadius: 5,
                      width: 50,
                      fontWeight: "700",
                    }}
                  >
                    <p className="text-white">{time}</p>
                  </div>
                  <div
                    className="flex items-center justify-center p-1"
                    style={{
                      backgroundColor: domainColors[sessionType || "general"],
                      textAlign: "center",
                      fontSize: 12,
                      borderRadius: 5,
                      width: 50,
                      fontWeight: "700",
                    }}
                  >
                    <p className="text-white">{newTime}</p>
                  </div>
                </div>
                <span
                  className={`mt-2 font-normal`}
                  style={{
                    fontSize: 12,
                    color: domainColors[sessionType || "general"],
                    textAlign: "left",
                    fontWeight: 600,
                  }}
                >
                  {renderTitle(parsedTask, sessionType)}
                </span>
                <div className="flex items-center justify-between">
                  <div
                    style={{
                      marginRight: "10px",
                      fontSize: "16px",
                      marginTop: 10,
                    }}
                  >
                    <Image
                      src={iconMapping[sessionType || "general"]?.logo}
                      alt={`${task.id}-logo`}
                      width={24}
                      height={24}
                    />
                  </div>
                  {!parsedTask.isTemp && (
                    <div className="flex" style={{ marginTop: 10 }}>
                      <button
                        onClick={(e) =>
                          handlePopperClick(
                            e,
                            task,
                            dateString,
                            idx,
                            sessionType,
                            time,
                          )
                        }
                        className="mr-2 text-slate-400 hover:text-blue-700"
                      >
                        <MessageSquare size={20} />
                      </button>
                      <Popper
                        ref={popperRef}
                        id={id}
                        open={open}
                        anchorEl={anchorEl}
                        disablePortal
                        placement="top-end"
                        onMouseEnter={() => setTooltipId(null)}
                      >
                        <div
                          className="mb-3 w-full rounded-lg border border-[#a1a1aa] bg-[#fafafa]"
                          style={{ height: 100 }}
                        >
                          <InputBase
                            onChange={(e) => setTaskText(e.target.value)}
                            onMouseEnter={() => setTooltipId(null)}
                            value={taskText}
                            placeholder="Type here..."
                            className="text-gray-900 block w-full rounded-lg bg-[#fafafa] p-2.5 text-sm focus:bg-[#f8fafc] focus:outline-none focus:ring-2 focus:ring-[#e2e8f0]"
                            required
                            multiline={false}
                            sx={{ p: 2, fontSize: 14 }}
                            onKeyDown={(e) =>
                              e.key === "Enter" &&
                              !e.shiftKey &&
                              handleCommentClick(e)
                            }
                          />
                        </div>
                      </Popper>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTask(dateString, task.id);
                        }}
                        className="text-slate-400 hover:text-red-700"
                      >
                        <Trash2 size={20} />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </HtmlTooltip>
          </div>
        )}
      </Draggable>
    );
  };

  const renderSingleDay = () => {
    const dateString = startDate.format("YYYY-MM-DD");
    const tasksForDate = tasks ? tasks[dateString] : { tasks: [], notes: [] };
    return (
      <div className="col-span-10 my-2">
        {[
          0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
          21, 22, 24, 24,
        ].map((j, i) => {
          return (
            <div
              key={i}
              className="borderColor scrollbar-hide flex h-28 w-full overflow-x-auto border-y p-1"
              // style={{ scrollbarWidth: "thin" }}
            >
              {j === 2 &&
                tasksForDate &&
                tasksForDate.tasks &&
                tasksForDate.tasks.map((task: any, idx: number) => {
                  let parsedTask: any;
                  if (typeof task.text === "string") {
                    try {
                      parsedTask = JSON.parse(task.text);
                    } catch (error) {
                      console.error("Failed to parse task.text:", error);
                      return <div key={task.id}>Invalid task data</div>;
                    }
                  } else if (
                    typeof task.text === "object" &&
                    task.text !== null
                  ) {
                    parsedTask = task.text;
                  } else {
                    console.error("Invalid task.text format:", task.text);
                    return <div key={task.id}>Invalid task data</div>;
                  }
                  const sessionType = getSessionsForLiter(
                    parsedTask.session_type?.toLowerCase(),
                  );
                  return renderGraph(parsedTask, false);
                })}
            </div>
          );
        })}
      </div>
    );
  };

  // Sample workout data
  const workoutData: Record<number, WorkoutData> = {
    10: {
      type: "Foundation Run",
      duration: "1:00",
      tss: 59,
      notes: ["60 min at a steady pace"],
      intensity: "low",
    },
    11: {
      type: "Base Maintenance 50r",
      duration: "0:50",
      tss: 45,
      notes: ["Ride up to 50% of last max heart rate"],
      intensity: "moderate",
    },
    12: {
      type: "Recovery Ride",
      duration: "0:45",
      tss: 35,
      notes: ["Easy spin to aid recovery"],
      intensity: "low",
    },
    13: {
      type: "Hill Repetitions Run",
      duration: "1:00",
      tss: 75,
      notes: ["6x3min @ Zone 2", "2:00 rest between", "Total: 6 sets"],
      intensity: "high",
    },
    14: {
      type: "Continuous Swim",
      duration: "1:15",
      tss: 59,
      notes: [
        "WU: 20 min above last pace",
        "MS: 40 min steady swim",
        "CD: 15 min very easy",
      ],
      intensity: "moderate",
    },
    15: {
      type: "Cruise Intervals 8x1m @ LT Pace",
      duration: "1:00",
      tss: 65,
      notes: [
        "8x1min intervals at LT pace",
        "2min easy jog between",
        "Total: 8 sets",
      ],
      intensity: "high",
    },
  };

  const handleDrawerClose = () => {
    dispatch(setDrawerOpen("false"));
  };

  return (
    <div className="flex h-[600px] flex-col border-l-slate-400 dark:border-l-slate-600 dark:bg-boxdark">
      <AnimatePresence mode="wait">
        {newParsedTask && (
          <motion.div
            key="modal-content"
            className="fixed inset-0 z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0, pointerEvents: "none" }}
            transition={{ duration: 0.2 }}
            style={{ marginLeft: "5%", height: "100vh" }}
          >
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center gap-8 px-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              style={{ marginLeft: "5%", height: "100vh" }}
            >
              <div
                className="fixed inset-0 mt-20 bg-white/20 backdrop-blur-sm"
                style={{ height: "100vh" }}
                onClick={handleClose}
              />
              <motion.div
                className="relative z-10 max-w-7xl rounded-lg shadow-xl"
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                transition={{ duration: 0.2 }}
                // style={{ marginLeft: !isMediumScreen ? '10%' : '40%' }}
              >
                <div
                  className="thin-scrollbar flex-grow overflow-auto"
                  style={{ height: "80vh" }}
                >
                  {/* {renderGraph(newParsedTask, false)} */}
                  <DayDetailModal
                    day={newParsedTask}
                    onClose={handleClose}
                    buttonClass={buttonClass}
                    modalfilteredTasks={modalfilteredTasks}
                    modalIndex={modalIndex}
                    setModalIndex={setModalIndex}
                    taskDate={selectedTask}
                  />
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      {loading ? (
        <div className="flex flex-grow items-center justify-center">
          <Loader />
        </div>
      ) : (
        <>
          {/* <div className="flex items-center justify-between p-1 pb-6 pt-6">
            <div className="flex w-full items-center pl-6">
              <div onClick={handleTodayClick}>
                <div className="borderShadowBlurr calenderHeaderTodaybox flex cursor-pointer hover:bg-gray">
                  <p style={{ fontSize: "0.7rem" }} className="font-semibold">
                    Today
                  </p>
                </div>
              </div>
              <DomainFilter
                sessionTypes={[
                  "Cycling",
                  "Strength",
                  "Nutrition",
                  "Recovery",
                  "Running",
                  // "run",
                  // "cycling",
                  // "bike",
                  // "strength",
                  // "nutrition",
                  // "recovery",
                  // "phase",
                ]}
                onFilterSession={handleFilterSession}
                dateString={dayjs().format("YYYY-MM-DD")}
                filterSession={filterSession}
                clearFilter={clearFilter}
              />
            </div>

            <div className="flex w-full">
              <div className="borderShadowBlurr expandedCalendarArrow flex hover:bg-gray">
                <button
                  onClick={() =>
                    selectedCalendarType === "Day"
                      ? loadMoreDay("up")
                      : selectedCalendarType === "Week"
                        ? loadMoreWeeks("up")
                        : loadMoreDays("up")
                  }
                  className=""
                >
                  <ChevronLeft />
                </button>
              </div>

              <div className="flex w-full items-center justify-center">
                <div className="">
                  {selectedCalendarType == "Day"
                    ? startDate.format("DD MMMM YYYY")
                    : startDate.format("MMMM YYYY")}
                </div>
              </div>
              <div className="borderShadowBlurr expandedCalendarArrow flex hover:bg-gray">
                <button
                  onClick={() =>
                    selectedCalendarType === "Day"
                      ? loadMoreDay("down")
                      : selectedCalendarType === "Week"
                        ? loadMoreWeeks("down")
                        : loadMoreDays("down")
                  }
                  className=""
                >
                  <ChevronRight />
                </button>
              </div>
            </div>

            <div className="flex w-full items-center justify-end pr-6">
              <div className="borderShadowBlurr expandedCalendarheaderightbutton flex cursor-pointer pl-1 pr-1">
                <div
                  onClick={() => {
                    setSelectedCalendarType("Year");
                  }}
                  className="rightshowBluredBorder p-2 hover:bg-gray"
                >
                  <p
                    style={{ fontSize: "0.7rem" }}
                    className={`${selectedCalendarType == "Year" ? "" : "textGrey"} font-semibold`}
                  >
                    Year
                  </p>
                </div>
                <div
                  onClick={() => {
                    handleCalendarDisplayType("Week");
                  }}
                  className="rightshowBluredBorder p-2 hover:bg-gray"
                >
                  <p
                    style={{ fontSize: "0.7rem" }}
                    className={`${selectedCalendarType == "Week" ? "" : "textGrey"} font-semibold`}
                  >
                    Week
                  </p>
                </div>
                <div
                  onClick={() => {
                    handleCalendarDisplayType("Month");
                  }}
                  className="p-2 hover:bg-gray"
                >
                  <p
                    style={{ fontSize: "0.7rem" }}
                    className={`${selectedCalendarType == "Month" ? "" : "textGrey"} font-semibold`}
                  >
                    Month
                  </p>
                </div>
                <div
                  onClick={() => {
                    handleCalendarDisplayType("Day");
                  }}
                  className="p-2 hover:bg-gray"
                >
                  <p
                    style={{ fontSize: "0.7rem" }}
                    className={`${selectedCalendarType == "Day" ? "" : "textGrey"} font-semibold`}
                  >
                    Day
                  </p>
                </div>
              </div>
            </div>
          </div> */}
          <div className="flex h-14 items-center justify-between border-2 border-slate-300 bg-slate-100 p-4">
            <div className="flex items-center gap-2">
              <span className="font-medium">Training Plan</span>
              <div className="h-4 w-px bg-slate-300" />
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleCalendarDisplayType("Year")}
                  className={`${buttonClass} ${selectedCalendarType === "Year" ? "bg-slate-700 text-primary-foreground hover:bg-slate-900" : "hover:text-slate-100-foreground border border-slate-200 bg-slate-50 hover:bg-slate-300"} ${selectedCalendarType === "Year" ? "text-white" : ""}`}
                >
                  Year
                </button>
                <button
                  onClick={() => handleCalendarDisplayType("Month")}
                  className={`${buttonClass} ${selectedCalendarType === "Month" ? "bg-slate-700 text-primary-foreground hover:bg-slate-900" : "hover:text-slate-100-foreground border border-slate-200 bg-slate-50 hover:bg-slate-300"} ${selectedCalendarType === "Month" ? "text-white" : ""}`}
                >
                  Month
                </button>
                <button
                  onClick={() => handleCalendarDisplayType("Week")}
                  className={`${buttonClass} ${selectedCalendarType === "Week" ? "bg-slate-700 text-primary-foreground hover:bg-slate-900" : "hover:text-slate-100-foreground border border-slate-200 bg-slate-50 hover:bg-slate-300"} ${selectedCalendarType === "Week" ? "text-white" : ""}`}
                >
                  Week
                </button>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <span className="font-medium">Development Phase - Week 3</span>
              <div className="h-4 w-px bg-border" />
              <span className="text-sm text-muted-foreground">
                Nutrition Planning
              </span>
            </div>
          </div>
          <div className="flex-1">
            {/* Calendar Grid */}
            <div className="overflow-auto">
              <div className="">
                {/* Month Navigation */}
                <div className="mb-6 mt-2 flex items-center justify-between">
                  <button
                    onClick={() =>
                      selectedCalendarType === "Day"
                        ? loadMoreDay("up")
                        : selectedCalendarType === "Week"
                          ? loadMoreWeeks("up")
                          : loadMoreDays("up")
                    }
                    className={`${buttonClass} hover:bg-accent hover:text-accent-foreground`}
                  >
                    <ChevronLeftIcon className="mr-2 h-4 w-4" />
                    {selectedCalendarType === "Week"
                      ? "Previous Week"
                      : selectedCalendarType === "Year"
                        ? "Previous Year"
                        : "Previous Month"}
                  </button>
                  <div className="flex items-center gap-4">
                    <span className="font-medium">
                      {selectedCalendarType == "Day"
                        ? startDate.format("DD MMMM YYYY")
                        : startDate.format("MMMM YYYY")}
                    </span>
                  </div>
                  <button
                    onClick={() =>
                      selectedCalendarType === "Day"
                        ? loadMoreDay("down")
                        : selectedCalendarType === "Week"
                          ? loadMoreWeeks("down")
                          : loadMoreDays("down")
                    }
                    className={`${buttonClass} hover:bg-accent hover:text-accent-foreground`}
                  >
                    {selectedCalendarType === "Week"
                      ? "Next Week"
                      : selectedCalendarType === "Year"
                        ? "Next Year"
                        : "Next Month"}
                    <ChevronRightIcon className="ml-2 h-4 w-4" />
                  </button>
                </div>

                {selectedCalendarType == "Week" ? (
                  // <div className="">
                  //   <RenderWeeks
                  //     clockTime={clockTime}
                  //     dragableTasks={dragableTasks}
                  //     filteredSessionTasks={filteredSessionTasks}
                  //     weekDates={weekDates}
                  //     getSessionsForLiter={getSessionsForLiter}
                  //     selectedCalendarType={selectedCalendarType}
                  //     tooltipId={tooltipId}
                  //     domainColors={domainColors}
                  //     setTooltipId={setTooltipId}
                  //     renderGraph={renderGraph}
                  //     domainBgColors={domainBgColors}
                  //     setSelectedTasks={setSelectedTasks}
                  //     iconMapping={iconMapping}
                  //     popperRef={popperRef}
                  //   />
                  // </div>
                  // Weekly View
                  <div
                    className={`grid grid-cols-7 gap-px bg-border ${!isMediumScreen ? "max-h-[300px]" : "max-h-[500px]"} overflow-hidden rounded-lg`}
                  >
                    {/* Day Headers */}
                    {weekDates.map((day, idx) => {
                      const isToday = day.isSame(dayjs(), "day");
                      return (
                        <div
                          key={idx}
                          className={`bg-slate-300 p-2 text-center text-sm ${
                            isToday
                              ? "font-bold text-purple-500"
                              : "font-medium"
                          } sticky top-0`}
                        >
                          {day.format("dddd")}
                        </div>
                      );
                    })}

                    {/* Week Days */}
                    {weekDates.map((day, i) => {
                      // const day = i + 10; // Starting from day 10 for this example
                      const date = parseInt(day.format("D"));
                      const dateString = day.format("YYYY-MM-DD");
                      const workout = workoutData[10];
                      const tasksForDate = tasks
                        ? tasks[dateString]
                        : { tasks: [], notes: [] };

                      const foundworkoutData =
                        tasksForDate !== undefined &&
                        tasksForDate.tasks.length > 0 &&
                        tasksForDate.tasks.find(
                          (item) => item.date === dateString,
                        );

                      let parsedTask: any;
                      if (
                        foundworkoutData &&
                        typeof foundworkoutData.text === "string"
                      ) {
                        try {
                          parsedTask = JSON.parse(foundworkoutData.text);
                        } catch (error) {
                          console.error("Failed to parse task.text:", error);
                          return (
                            <div key={foundworkoutData.id}>
                              Invalid task data
                            </div>
                          );
                        }
                      } else if (
                        foundworkoutData &&
                        typeof foundworkoutData.text === "object" &&
                        foundworkoutData.text !== null
                      ) {
                        parsedTask = foundworkoutData.text;
                      }
                      let duration: any;
                      let sessionType: string;
                      let newTime: string;

                      if (parsedTask) {
                        const foundDomain = selectedDate
                          .find((it) => it.date === `${dateString}-${i}`)
                          ?.domain.toLowerCase();
                        // iconMapping
                        const bgColor = `bg-[${domainColors[foundDomain]}]`;

                        console.log("bgColor", bgColor, foundDomain);

                        sessionType = getSessionsForLiter(
                          parsedTask.session_type?.toLowerCase(),
                        );
                        duration = parsedTask.duration
                          ? parsedTask.duration
                          : parsedTask.segments !== undefined
                            ? parsedTask.segments
                                .filter((it: { duration: any }) => it.duration)
                                .map((it: { duration: any }) => it.duration)
                                .reduce((a: any, b: any) => a + b, 0)
                            : "";
                        newTime = foundworkoutData
                          ? duration
                            ? `${foundworkoutData.time.split(":")[0]}:${duration}`
                            : `${parseInt(foundworkoutData.time.split(":")[0])}:00`
                          : "";

                        return (
                          <div
                            key={`${dateString}-${i}`}
                            className="min-h-[200px] cursor-pointer border-b-2 border-r-2 border-slate-300 p-4 transition-colors hover:bg-slate-100"
                            onClick={(e) => {
                              if (parsedTask) {
                                setModalWeekDays(
                                  weekDates.map((day: any) =>
                                    day.format("YYYY-MM-DD"),
                                  ),
                                );
                                setNewParsedTask(parsedTask);
                              }
                              setSelectedTasks(`${dateString}`);
                              setSelectedDate([
                                {
                                  date: `${dateString}-${i}`,
                                  domain:
                                    selectedDate.length > 0 &&
                                    selectedDate[0]?.domain
                                      ? selectedDate[0].domain
                                      : "",
                                },
                              ]);
                              e.stopPropagation();
                              dispatch(setDrawerOpen(`${dateString}-${i}`));
                            }}
                          >
                            <div className="mb-2 font-medium">
                              {day.format("D")}
                            </div>
                            {parsedTask && (
                              <div className="space-y-2">
                                <div
                                  className={`rounded-full px-2 py-1 text-xs ${domain_STYLES[sessionType]}`}
                                >
                                  {sessionType}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {newTime ? newTime : "0:00"}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {workout.tss} TSS
                                </div>
                                {workout.notes.map((note, index) => (
                                  <div
                                    key={index}
                                    className="text-xs text-muted-foreground"
                                  >
                                    {note}
                                  </div>
                                ))}
                              </div>
                            )}

                            <CalenderDetailsSidebar
                              day={day}
                              open={drawerOpen === `${dateString}-${i}`}
                              handleDrawerClose={handleDrawerClose}
                              timeArr={timeArr}
                              parsedTask={{
                                sessionType,
                                newTime,
                                tss: workout.tss,
                                notes: workout.notes,
                              }}
                              tasks={tasks}
                              i={i}
                              dateString={dateString}
                              handleOpen={handleOpen}
                              domainColors={domainColors}
                              foundDomain={foundDomain}
                              handleTimeClick={handleTimeClick}
                              isTimeMenuopen={isTimeMenuopen}
                              timeMenuRef={timeMenuRef}
                              timeAnchorEl={timeAnchorEl}
                              handleTimeClose={handleTimeClose}
                              handleChooseTime={handleChooseTime}
                              selectedDate={selectedDate}
                              setSelectedDate={setSelectedDate}
                              bgColor={bgColor}
                              dropdownRef={dropdownRef}
                              setSetDateDropdown={setSetDateDropdown}
                              setIsDropdownOpen={setIsDropdownOpen}
                              isDropdownOpen={isDropdownOpen}
                              iconMapping={iconMapping}
                              dateDropdown={dateDropdown}
                              sessionCoach={sessionCoach}
                              handleSelectDomain={handleSelectDomain}
                              setNoteText={setNoteText}
                              noteText={noteText}
                              handleSubmitNote={handleSubmitNote}
                            />
                          </div>
                        );
                      }

                      const foundDomain = selectedDate
                        .find((it) => it.date === `${dateString}-${i}`)
                        ?.domain.toLowerCase();
                      const bgColor = `bg-[${domainColors[foundDomain]}]`;
                      console.log("bgColor", bgColor, foundDomain);

                      return (
                        <div
                          key={`${dateString}-${i}`}
                          className="flex min-h-[200px] cursor-pointer flex-col border-b-2 border-r-2 border-slate-300 p-4 transition-colors hover:bg-slate-100"
                          onClick={(e) => {
                            // e.stopPropagation()
                            setSelectedDate([
                              {
                                date: `${dateString}-${i}`,
                                domain:
                                  selectedDate.length > 0 &&
                                  selectedDate[0]?.domain
                                    ? selectedDate[0].domain
                                    : "",
                              },
                            ]);
                            dispatch(setDrawerOpen(`${dateString}-${i}`));
                          }}
                        >
                          <div className="mb-2 font-medium">
                            {day.format("D")}
                          </div>

                          <CalenderDetailsSidebar
                            day={day}
                            open={drawerOpen === `${dateString}-${i}`}
                            handleDrawerClose={handleDrawerClose}
                            timeArr={timeArr}
                            parsedTask={null}
                            tasks={tasks}
                            i={i}
                            dateString={dateString}
                            domainColors={domainColors}
                            foundDomain={foundDomain}
                            handleOpen={() => {}}
                            handleTimeClick={handleTimeClick}
                            isTimeMenuopen={isTimeMenuopen}
                            timeMenuRef={timeMenuRef}
                            timeAnchorEl={timeAnchorEl}
                            handleTimeClose={handleTimeClose}
                            handleChooseTime={handleChooseTime}
                            selectedDate={selectedDate}
                            setSelectedDate={setSelectedDate}
                            bgColor={bgColor}
                            dropdownRef={dropdownRef}
                            setSetDateDropdown={setSetDateDropdown}
                            setIsDropdownOpen={setIsDropdownOpen}
                            isDropdownOpen={isDropdownOpen}
                            iconMapping={iconMapping}
                            dateDropdown={dateDropdown}
                            sessionCoach={sessionCoach}
                            handleSelectDomain={handleSelectDomain}
                            setNoteText={setNoteText}
                            noteText={noteText}
                            handleSubmitNote={handleSubmitNote}
                          />
                        </div>
                      );
                    })}
                    {/* <X size={'20px'} onClick={handleDrawerClose} /> */}
                  </div>
                ) : selectedCalendarType == "Day" ? (
                  <div className="overflow-y-auto p-4">
                    <div className="grid w-full grid-cols-11 border border-gray-3 text-center font-semibold">
                      <div className="col-span-1">
                        {clockTime.map((j, i) => {
                          return (
                            <div
                              key={i}
                              className="textGrey flex h-28 items-start justify-center"
                            >
                              {j}
                            </div>
                          );
                        })}
                      </div>
                      {renderSingleDay()}
                    </div>
                  </div>
                ) : selectedCalendarType == "Month" ? (
                  <div
                    className={`scrollbar-hide ${!isMediumScreen ? "max-h-[300px]" : "max-h-[500px]"} overflow-y-auto`}
                  >
                    <div className="grid grid-cols-7 gap-2 text-center font-semibold">
                      <div
                        className={`sticky top-0 bg-slate-300 p-2 text-center text-sm font-medium`}
                      >
                        Sun
                      </div>
                      <div
                        className={`sticky top-0 bg-slate-300 p-2 text-center text-sm font-medium`}
                      >
                        Mon
                      </div>
                      <div
                        className={`sticky top-0 bg-slate-300 p-2 text-center text-sm font-medium`}
                      >
                        Tue
                      </div>
                      <div
                        className={`sticky top-0 bg-slate-300 p-2 text-center text-sm font-medium`}
                      >
                        Wed
                      </div>
                      <div
                        className={`sticky top-0 bg-slate-300 p-2 text-center text-sm font-medium`}
                      >
                        Thu
                      </div>
                      <div
                        className={`sticky top-0 bg-slate-300 p-2 text-center text-sm font-medium`}
                      >
                        Fri
                      </div>
                      <div
                        className={`sticky top-0 bg-slate-300 p-2 text-center text-sm font-medium`}
                      >
                        Sat
                      </div>
                    </div>
                    {tasks && (
                      <RenderDays
                        dates={dates}
                        domainBgColors={domainBgColors}
                        domainColors={domainColors}
                        getSessionsForLiter={getSessionsForLiter}
                        iconMapping={iconMapping}
                        renderGraph={renderGraph}
                        setSelectedTasks={setSelectedTasks}
                        startDate={startDate}
                        workoutData={workoutData}
                        setNewParsedTask={setNewParsedTask}
                        setModalWeekDays={setModalWeekDays}
                        selectedDate={selectedDate}
                        setSelectedDate={setSelectedDate}
                        setNoteText={setNoteText}
                        noteText={noteText}
                        dropdownRef={dropdownRef}
                        setSetDateDropdown={setSetDateDropdown}
                        setIsDropdownOpen={setIsDropdownOpen}
                        isDropdownOpen={isDropdownOpen}
                        dateDropdown={dateDropdown}
                        handleSelectDomain={handleSelectDomain}
                        handleSubmitNote={handleSubmitNote}
                        handleTimeClick={handleTimeClick}
                        isTimeMenuopen={isTimeMenuopen}
                        timeMenuRef={timeMenuRef}
                        timeAnchorEl={timeAnchorEl}
                        handleTimeClose={handleTimeClose}
                        timeArr={timeArr}
                        handleChooseTime={handleChooseTime}
                      />
                    )}
                  </div>
                ) : (
                  <div
                    className={`scrollbar-hide grid ${!isMediumScreen ? "max-h-[300px]" : "max-h-[500px]"} grid-cols-4 gap-4 overflow-y-auto`}
                  >
                    {fullCalendatDate &&
                      Object.keys(fullCalendatDate).map(
                        (monthKey, monthIndex) => (
                          <div
                            key={monthIndex}
                            className="rounded-lg bg-slate-100 p-4"
                          >
                            <div className="mb-3 font-medium">
                              {monthKey.split("-")[2]}
                              {/* {new Date(2024, monthIndex).toLocaleString('default', { month: 'long' })} */}
                            </div>
                            <div className="grid grid-cols-7 gap-1">
                              {[
                                "Sun",
                                "Mon",
                                "Tue",
                                "Wed",
                                "Thu",
                                "Fri",
                                "Sat",
                              ].map((day) => (
                                <div
                                  key={day}
                                  className="bg-muted/50 p-2 text-center text-sm font-medium"
                                >
                                  {day}
                                </div>
                              ))}
                              <div
                                className="ml-1.5 flex flex-col"
                                style={{ width: "725%" }}
                              >
                                {fullCalendatDate[monthKey]
                                  .slice(0, 1)
                                  .map((week: any, idx: number) => (
                                    <div
                                      key={week.weekNumber}
                                      className={`mb-2`}
                                      style={{
                                        display: "flex",
                                        flexDirection: "row",
                                        justifyContent: "flex-end",
                                        gap: "3px",
                                      }}
                                    >
                                      {week.days.map(
                                        (day: any, idx: number) => {
                                          const dateString = day.date;
                                          const tasksForDate = tasks
                                            ? tasks[dateString]
                                            : { tasks: [], notes: [] };

                                          const foundworkoutData =
                                            tasksForDate !== undefined &&
                                            tasksForDate.tasks.length > 0 &&
                                            tasksForDate.tasks.find(
                                              (item) =>
                                                item.date === dateString,
                                            );

                                          let parsedTask: any;
                                          if (
                                            foundworkoutData &&
                                            typeof foundworkoutData.text ===
                                              "string"
                                          ) {
                                            try {
                                              parsedTask = JSON.parse(
                                                foundworkoutData.text,
                                              );
                                            } catch (error) {
                                              console.error(
                                                "Failed to parse task.text:",
                                                error,
                                              );
                                              return (
                                                <div key={foundworkoutData.id}>
                                                  Invalid task data
                                                </div>
                                              );
                                            }
                                          } else if (
                                            foundworkoutData &&
                                            typeof foundworkoutData.text ===
                                              "object" &&
                                            foundworkoutData.text !== null
                                          ) {
                                            parsedTask = foundworkoutData.text;
                                          }
                                          let sessionType: string = "";

                                          if (parsedTask) {
                                            sessionType = getSessionsForLiter(
                                              parsedTask.session_type?.toLowerCase(),
                                            );
                                          }

                                          return (
                                            <div
                                              key={idx}
                                              className={`rounded-sm p-1 text-center text-xs font-medium ${parsedTask ? `${domain_STYLES[sessionType]}` : ""} w-[48px] cursor-pointer`}
                                              onClick={() => {
                                                if (parsedTask) {
                                                  setModalWeekDays(
                                                    week.days.map(
                                                      (day: any) => day.date,
                                                    ),
                                                  );
                                                  setNewParsedTask(parsedTask);
                                                }
                                                // if (
                                                //   sessionType != "nutrition" &&
                                                //   sessionType != "recovery" &&
                                                //   sessionType != "phase"
                                                // ) {
                                                // }
                                                setSelectedTasks(
                                                  `${dateString}`,
                                                );
                                              }}
                                            >
                                              {dayjs(day.date).format("D")}
                                            </div>
                                          );
                                        },
                                      )}
                                    </div>
                                  ))}
                                {fullCalendatDate[monthKey]
                                  .slice(1, fullCalendatDate[monthKey].length)
                                  .map((week: any, i: number) => (
                                    <div
                                      key={week.weekNumber}
                                      className={`mb-2 grid grid-cols-7 gap-1`}
                                      style={{
                                        // display: "flex", flexDirection: "row",
                                        justifyContent:
                                          i !== 0 ? "flex-start" : "flex-end",
                                        // gap: "10px"
                                      }}
                                    >
                                      {week.days.map(
                                        (day: any, idx: number) => {
                                          const dateString = day.date;
                                          const tasksForDate = tasks
                                            ? tasks[dateString]
                                            : { tasks: [], notes: [] };

                                          const foundworkoutData =
                                            tasksForDate !== undefined &&
                                            tasksForDate.tasks.length > 0 &&
                                            tasksForDate.tasks.find(
                                              (item) =>
                                                item.date === dateString,
                                            );

                                          let parsedTask: any;
                                          if (
                                            foundworkoutData &&
                                            typeof foundworkoutData.text ===
                                              "string"
                                          ) {
                                            try {
                                              parsedTask = JSON.parse(
                                                foundworkoutData.text,
                                              );
                                            } catch (error) {
                                              console.error(
                                                "Failed to parse task.text:",
                                                error,
                                              );
                                              return (
                                                <div key={foundworkoutData.id}>
                                                  Invalid task data
                                                </div>
                                              );
                                            }
                                          } else if (
                                            foundworkoutData &&
                                            typeof foundworkoutData.text ===
                                              "object" &&
                                            foundworkoutData.text !== null
                                          ) {
                                            parsedTask = foundworkoutData.text;
                                          }
                                          let sessionType: string = "";

                                          if (parsedTask) {
                                            sessionType = getSessionsForLiter(
                                              parsedTask.session_type?.toLowerCase(),
                                            );
                                          }

                                          return (
                                            <div
                                              key={idx}
                                              className={`rounded-sm p-1 text-center text-xs font-medium ${parsedTask ? `${domain_STYLES[sessionType]}` : ""} cursor-pointer`}
                                              onClick={() => {
                                                if (parsedTask) {
                                                  setModalWeekDays(
                                                    week.days.map(
                                                      (day: any) => day.date,
                                                    ),
                                                  );
                                                  setNewParsedTask(parsedTask);
                                                }
                                                // if (
                                                //   sessionType != "nutrition" &&
                                                //   sessionType != "recovery" &&
                                                //   sessionType != "phase"
                                                // ) {
                                                // }
                                                setSelectedTasks(
                                                  `${dateString}`,
                                                );
                                              }}
                                            >
                                              {dayjs(day.date).format("D")}
                                            </div>
                                          );
                                        },
                                      )}
                                    </div>
                                  ))}
                              </div>
                              {/* {dates.map((day, i) => {
                            // const day = i + 1;
                            const date = parseInt(day.format("D"))
                            const hasWorkout = workoutData[date];
                            return (
                              <div
                                key={i}
                                className={`text-xs text-center p-1 rounded-sm ${hasWorkout ? `${INTENSITY_STYLES[hasWorkout.intensity]} font-medium` : ''
                                  }`}
                              >
                                {day.format("D")}
                              </div>
                            );
                          })} */}
                            </div>
                          </div>
                        ),
                      )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}

      <Modal
        open={isOpen}
        onClose={handleOnClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <SessionLibrary isOpen={isOpen} onClose={handleOnClose} />
      </Modal>
    </div>
  );
};

export default CalendarView;
