import {
  <PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@mui/material";
import dayjs from "dayjs";
import {
  ChevronDown,
  ChevronUp,
  Loader2,
  MessageCircle,
  Plus,
  RotateCw,
  Save,
  Trash,
  Trash2,
  X,
} from "lucide-react";
import React, { useMemo, useState } from "react";
import { domain_STYLES } from "./CalendarView";
import { But<PERSON> } from "../ui/button";
import Image, { StaticImageData } from "next/image";
import { deleteNote, setChangeTaskTime } from "@/store/slices/calendarSlice";
import { useDispatch } from "react-redux";
import {
  Draggable,
  DraggableProvided,
  DraggableStateSnapshot,
  Droppable,
} from "react-beautiful-dnd";
import { RootState } from "@/store/store";
import { useAppSelector } from "@/store/jobHook";

interface CalendarTask {
  id?: string | number | undefined;
  date?: string;
  time?: string;
  text: string | any;
}

interface CalendarDate {
  date: string;
  domain: string;
}

interface CalendarNote {
  noteText: string;
  time: string;
}

interface Role {
  name: string;
  logo: StaticImageData;
}

interface CalenderDetailsSidebarProps {
  open: boolean;
  handleDrawerClose: () => void;
  day: dayjs.Dayjs;
  timeArr: string[];
  parsedTask: {
    sessionType: string;
    newTime: string;
    tss: number;
    notes: string[];
  } | null;
  tasks: {
    [key: string]: {
      tasks: any[];
      notes: any[];
      domainTasks?: { [domain: string]: any[] };
    };
  };
  dateString: string;
  i: number;
  domainColors: { [key: string]: string };
  foundDomain: string | undefined;
  handleTimeClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
  isTimeMenuopen: boolean;
  timeMenuRef: React.RefObject<HTMLDivElement | null>;
  timeAnchorEl: HTMLElement | null;
  handleTimeClose: (event: React.MouseEvent<HTMLButtonElement>) => void;
  handleChooseTime: (
    event: React.MouseEvent<HTMLLIElement, MouseEvent>,
    dateString: string,
    time: string,
    noteIndex: number,
  ) => void;
  selectedDate: CalendarDate[];
  setSelectedDate: React.Dispatch<React.SetStateAction<CalendarDate[]>>;
  bgColor: string;
  dropdownRef: React.RefObject<HTMLDivElement | null>;
  setSetDateDropdown: React.Dispatch<React.SetStateAction<string | null>>;
  setIsDropdownOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isDropdownOpen: boolean;
  iconMapping: { [key: string]: Role };
  dateDropdown: string | null;
  sessionCoach: string[];
  handleSelectDomain: (domain: string, date: string) => void;
  setNoteText: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  noteText: Record<string, string>;
  handleSubmitNote: (dateString: string, value: string) => void;
  handleOpen: () => void;
}

const CalenderDetailsSidebar: React.FC<CalenderDetailsSidebarProps> = ({
  open,
  handleDrawerClose,
  day,
  timeArr,
  parsedTask,
  tasks,
  dateString,
  domainColors,
  foundDomain,
  handleTimeClick,
  isTimeMenuopen,
  timeMenuRef,
  timeAnchorEl,
  handleTimeClose,
  handleChooseTime,
  selectedDate,
  setSelectedDate,
  bgColor,
  dropdownRef,
  setSetDateDropdown,
  setIsDropdownOpen,
  isDropdownOpen,
  iconMapping,
  dateDropdown,
  sessionCoach,
  handleSelectDomain,
  setNoteText,
  noteText,
  handleSubmitNote,
  i,
  handleOpen,
}) => {
  const dispatch = useDispatch();
  const { isChangeTaskTime } = useAppSelector(
    (state: RootState) => state.calendar,
  );
  // console.log("---parsedTask---", parsedTask);
  // console.log("---tasks---", tasks);
  // console.log("---dateString---", dateString);
  // console.log("---selectedDate---", selectedDate);
  // console.log("---notes---", tasks[dateString]?.notes);
  const [step, setstep] = useState(1);
  const sessionTypes = [
    "Strength Session",
    "Running Session",
    "Cycling Session",
    "Nutrition Session",
    "Recovery Session",
  ];
  const currentMonth = useMemo(() => {
    const month = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const d = new Date();
    let name = month[d.getMonth()];
    return name;
  }, []);

  const parsedTaskNewTime = useMemo(() => {
    if (parsedTask) {
      const tm = parsedTask.newTime.split(":")[0] as string;
      console.log(tm.includes("0") ? tm[0] : tm);

      return tm.includes("0") ? tm[1] : tm;
    } else {
      return null;
    }
  }, [parsedTask?.newTime]);
  return (
    <Drawer
      anchor="right"
      variant="persistent"
      open={open}
      onClose={handleDrawerClose}
      sx={{
        position: "relative",
        width: 340,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: 340,
          maxHeight: "100vh",
          // overflow: "hidden"
        },
      }}
    >
      <Box
        padding={"15px"}
        display={"flex"}
        justifyContent={"space-between"}
        alignItems={"center"}
        borderBottom={"1px solid #ddd"}
      >
        {day?.format("dddd")}, {currentMonth} {day?.format("D")}
        <X size={"20px"} onClick={handleDrawerClose} />
      </Box>
      <Box
        display={"flex"}
        justifyContent={"space-between"}
        alignItems={"center"}
      >
        <Box padding={"15px"} color={"#bbb"} fontSize={"10px"}>
          Development Phase - week 3
        </Box>
        <button
          className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500 p-2 text-white transition-colors hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          onClick={() => {
            // if (isOpenModal) {
            //   handleOpen()
            // }else{
            //   // dispatch(setOverviewContent(foundContent));
            //   // dispatch(setOverviewModal(true));

            // }
            if (parsedTask) {
              handleOpen();
            } else {
              setIsDropdownOpen(!isDropdownOpen);
            }
          }}
          // disabled={isLoading}
        >
          {/* {isLoading ? (
          <Loader size={16} className="animate-spin" />
        ) : (
        )} */}
          <Plus size={16} />
        </button>
        {isDropdownOpen && (
          <div
            className="absolute right-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5"
            style={{
              zIndex: 1000,
              top: "12%",
              maxHeight: "200px",
              overflowY: "auto",
              // position:'absolute',
            }}
          >
            <div
              className="py-1"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="options-menu"
            >
              {sessionTypes.map((coach) => (
                <button
                  key={coach}
                  className="text-gray-700 hover:bg-gray-100 hover:text-gray-900 block w-full px-4 py-2 text-left text-sm"
                  role="menuitem"
                  // onClick={() => handleAddSession(coach)}
                >
                  {coach}
                </button>
              ))}
            </div>
          </div>
        )}
      </Box>
      <Box
        paddingInline={"15px"}
        display={"flex"}
        justifyContent={"space-between"}
      >
        <Box
          sx={{
            borderRadius: "5px",
            backgroundColor: "#eee",
            padding: "8px",
            fontSize: "12px",
          }}
          onClick={() => setstep(1)}
        >
          Add Workout
        </Box>
        <Box
          sx={{
            borderRadius: "5px",
            backgroundColor: "#eee",
            padding: "8px",
            fontSize: "12px",
          }}
          onClick={() => setstep(2)}
        >
          Add Note
        </Box>
        <Box
          sx={{
            borderRadius: "5px",
            backgroundColor: "#eee",
            padding: "8px",
            fontSize: "12px",
          }}
          onClick={() => setstep(3)}
        >
          Log Metrics
        </Box>
      </Box>

      {step === 1 ? (
        <div className="scrollbar-hide flex flex-col  p-3">
          {timeArr.map((time: string, idx: number) => {
            // console.log(
            //   "---cel side color---",
            //   domain_STYLES[parsedTask?.sessionType],
            // );

            return (
              <Droppable
                key={`droppable-${time}`}
                droppableId={`droppable-${time}-${dateString}`}
              >
                {(provided, snapshot) => (
                  <div
                    key={idx}
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    onClick={() => {
                      if (
                        parsedTaskNewTime &&
                        !time.includes(parsedTaskNewTime)
                      ) {
                        handleOpen();
                      }
                    }}
                  >
                    <div className="relative ml-12 flex min-h-[30px] w-[80%] border-l border-slate-300">
                      <Draggable
                        key={idx}
                        draggableId={`draggable-${time}-${dateString}`}
                        index={idx}
                      >
                        {(
                          providedDraggable: DraggableProvided,
                          snapshotDraggable: DraggableStateSnapshot,
                        ) => (
                          <div
                            ref={providedDraggable.innerRef}
                            {...providedDraggable.draggableProps}
                            {...providedDraggable.dragHandleProps}
                            className="mb-3 mt-1 w-full"
                          >
                            {parsedTask &&
                              parsedTaskNewTime &&
                              time.includes(parsedTaskNewTime) && (
                                <Box
                                  margin={"10px"}
                                  padding={"5px"}
                                  marginLeft={"20px"}
                                  borderLeft={`5px solid`}
                                  borderRadius={"5px"}
                                  borderBottom={`1px solid #ccc`}
                                  borderRight={`1px solid #ccc`}
                                  borderTop={`1px solid #ccc`}
                                  className={`border-${domain_STYLES[parsedTask.sessionType]}`}
                                >
                                  <div className="space-y-2">
                                    <div
                                      className={`rounded-full px-2 py-1 text-xs ${domain_STYLES[parsedTask.sessionType]}`}
                                    >
                                      {parsedTask.sessionType}
                                    </div>
                                    <div className="text-muted-foreground text-sm">
                                      {parsedTask.newTime
                                        ? parsedTask.newTime
                                        : "0:00"}
                                    </div>
                                    <div className="text-muted-foreground text-sm">
                                      {parsedTask.tss} TSS
                                    </div>
                                    {parsedTask.notes.map((note, index) => (
                                      <div
                                        key={index}
                                        className="text-muted-foreground text-xs"
                                      >
                                        {note}
                                      </div>
                                    ))}
                                  </div>
                                </Box>
                              )}
                          </div>
                        )}
                      </Draggable>
                      <div
                        className="absolute bottom-0 bg-slate-300"
                        style={{
                          left: -3,
                          height: 8,
                          width: 8,
                          borderRadius: 10,
                        }}
                      ></div>
                      <p
                        className="font-small absolute text-slate-400"
                        style={{ left: -45, bottom: -5 }}
                      >
                        {time}
                      </p>
                    </div>
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            );
          })}
        </div>
      ) : step === 2 ? (
        <Box padding={"15px"}>
          {tasks && tasks[dateString] && tasks[dateString].notes.length > 0 && (
            <div className="mt-3">
              {tasks[dateString].notes.map((note: any, idx: number) => {
                const noteText =
                  typeof note === "string" ? note : note.noteText;
                const noteTime = typeof note === "string" ? "" : note.time;

                return (
                  <div key={idx} className="flex flex-col">
                    <div
                      className="mb-2 flex w-full cursor-pointer items-center rounded-md bg-[#f5f5f5]"
                      style={{
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      }}
                    >
                      <div
                        style={{
                          width: "6px",
                          backgroundColor:
                            domainColors[foundDomain || "general"],
                          borderTopLeftRadius: "5px",
                          borderBottomLeftRadius: "5px",
                          marginRight: "10px",
                          height: 40,
                        }}
                      ></div>
                      <div
                        style={{
                          marginRight: "10px",
                          fontSize: "16px",
                        }}
                      >
                        <MessageCircle
                          className="h-6 w-6"
                          color={domainColors[foundDomain || "general"]}
                        />
                      </div>
                      <div
                        style={{
                          fontSize: "14px",
                          fontWeight: "400",
                          width: "50%",
                        }}
                      >
                        {`${noteText.slice(0, 20)}...` || "Note"}
                      </div>
                      {/* time slot  */}

                      <div>
                        <MuiButton
                          id="basic-button"
                          aria-haspopup="true"
                          onClick={handleTimeClick}
                          variant="outlined"
                          size="small"
                          style={{ paddingLeft: "8px" }}
                          sx={{
                            marginRight: 1,
                            marginLeft: 1,
                            borderRadius: 2,
                            color: "black",
                            borderColor: "black",
                            backgroundColor: "#ffffff",
                            fontSize: 12,
                            textTransform: "none",
                          }}
                          endIcon={
                            isTimeMenuopen ? (
                              <ChevronUp className="h-4 w-4" color="#bdbdbd" />
                            ) : (
                              <ChevronDown
                                className="h-4 w-4"
                                color="#bdbdbd"
                              />
                            )
                          }
                        >
                          {noteTime || "Time"}
                        </MuiButton>
                        <Menu
                          ref={timeMenuRef}
                          id="basic-menu"
                          anchorEl={timeAnchorEl}
                          open={isTimeMenuopen}
                          onClose={handleTimeClose}
                          slotProps={{
                            paper: {
                              style: { scrollbarWidth: "none" },
                            },
                          }}
                          MenuListProps={{
                            "aria-labelledby": "basic-button",
                          }}
                          style={{ maxHeight: 500 }}
                        >
                          {timeArr.map((time: string) => (
                            <MenuItem
                              key={time}
                              sx={{
                                width: 100,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                              onClick={(e) =>
                                handleChooseTime(e, dateString, time, idx)
                              }
                            >
                              {time}
                            </MenuItem>
                          ))}
                        </Menu>
                      </div>

                      {/* time slot */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          dispatch(
                            deleteNote({ date: dateString, noteIndex: idx }),
                          );
                        }}
                        className="mr-4 text-slate-500 hover:text-red-700"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
          {selectedDate.map((it) => it.date).includes(`${dateString}-${i}`) && (
            <div className="mt-2 flex min-h-[120px] w-full flex-col rounded-md border border-sky-500 shadow-md">
              <div
                className={`flex h-8 w-full flex-row items-center justify-between px-3 ${selectedDate.find((it) => it.date === `${dateString}-${i}`)?.domain ? bgColor : "bg-sky-200"} rounded-t-md`}
              >
                <p className="font-medium text-blue-900">Add Note</p>
                <X
                  className={`h-4 w-4 transition-transform duration-200`}
                  onClick={(e) => {
                    {
                      e.stopPropagation();
                      setSelectedDate(
                        selectedDate.filter(
                          (it) => it.date !== `${dateString}-${i}`,
                        ),
                      );
                    }
                  }}
                />
              </div>
              <div
                className="relative flex cursor-pointer items-center gap-2"
                ref={dropdownRef}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="flex w-full items-center justify-start"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSetDateDropdown(`${dateString}-${i}`);
                    setIsDropdownOpen(!isDropdownOpen);
                  }}
                >
                  <div
                    style={{
                      marginLeft: "10px",
                      fontSize: "10px",
                      marginTop: 2,
                    }}
                  >
                    <Image
                      src={iconMapping[foundDomain || "general"]?.logo}
                      alt={`${dateString}-${i}-logo`}
                      width={16}
                      height={16}
                    />
                  </div>
                  <span className="font-semibold">
                    {selectedDate.find((it) => it.date === `${dateString}-${i}`)
                      ?.domain || "New Note"}
                  </span>
                  {isDropdownOpen &&
                  dateDropdown &&
                  dateDropdown === `${dateString}-${i}` ? (
                    <ChevronUp
                      className={`h-4 w-4 transition-transform duration-200`}
                    />
                  ) : (
                    <ChevronDown
                      className={`h-4 w-4 transition-transform duration-200`}
                    />
                  )}
                </Button>
                {isDropdownOpen &&
                  dateDropdown &&
                  dateDropdown === `${dateString}-${i}` && (
                    <div
                      className="scrollbar-hide absolute left-20 w-30 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5"
                      style={{
                        zIndex: 1000,
                        top: "35%",
                        maxHeight: "80px",
                        overflowY: "auto",
                      }}
                    >
                      <div
                        className="py-1"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="options-menu"
                      >
                        {sessionCoach.map((item, idx) => (
                          <div
                            key={idx}
                            className="mb-2 flex items-center justify-between hover:bg-[#eeeeee]"
                          >
                            <button
                              className="text-gray-700 hover:bg-gray-700 hover:text-gray-900 block flex w-full items-center justify-between px-4 py-2 text-left text-sm"
                              role="menuitem"
                              onClick={(e) => {
                                handleSelectDomain(item, `${dateString}-${i}`);
                                setIsDropdownOpen(!isDropdownOpen);
                                e.stopPropagation();
                              }}
                            >
                              {item}
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </div>
              <InputBase
                className="md:placeholder-trimmed mx-auto my-2 h-10 w-[90%] rounded-md border border-slate-400 px-2"
                onChange={(e) =>
                  setNoteText({ ...noteText, [dateString]: e.target.value })
                }
                value={noteText[dateString] || ""}
                placeholder={"Type here..."}
                onClick={(e) => e.stopPropagation()}
                // disabled={pendingResponse}
                inputProps={{
                  "aria-label": "type here",
                  className:
                    "placeholder-trimmed md:placeholder-trimmed border",
                }}
                multiline={false}
                onKeyDown={(e) =>
                  e.key === "Enter" &&
                  !e.shiftKey &&
                  // handleCommentClick(e)
                  handleSubmitNote(dateString, noteText[dateString])
                }
              />
            </div>
          )}
        </Box>
      ) : (
        step === 3 && <div>3</div>
      )}

      {isChangeTaskTime && (
        <div className="fixed bottom-2 right-2 flex justify-end space-x-2 rounded-md bg-transparent p-4">
          {/* Save Button */}
          <Tooltip title={"Save"} placement="bottom" arrow>
            <button
              onClick={() => dispatch(setChangeTaskTime(false))}
              className={`flex items-center rounded-md border border-slate-300 bg-[#ffffff] px-2 py-1 text-slate-700 hover:bg-blue-50 dark:border-slate-600 dark:text-white dark:hover:bg-slate-600`}
              title={"Save your changes"}
            >
              {false ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save
            </button>
          </Tooltip>

          {/* Reset Button */}
          <button
            className={`flex items-center rounded-md border border-slate-300 bg-[#ffffff] px-2 py-1 text-slate-700 hover:bg-blue-50 dark:border-slate-600 dark:text-white dark:hover:bg-slate-600`}
            title={"Reset your changes"}
          >
            <RotateCw className="mr-2 h-4 w-4" /> Refresh
          </button>
        </div>
      )}
    </Drawer>
  );
};

export default CalenderDetailsSidebar;
