import React, {
  useState,
  useEffect,
  useRef,
  use<PERSON>allback,
  useMemo,
} from "react";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { RootState } from "@/store/store";
import { useGlossary } from "@/context/GlossaryContext";
import { useTrainingProfile } from "@/context/TrainingProfileContext";
import { toggleCalendar, setIsOpen } from "@/store/slices/calendarSlice";
import {
  setIsHomeInputSubmitted,
  handleCreateNewChatClick,
  setInitialMessage,
  setSelectedArtifact,
  setChatOption,
  handleCoachClick,
  initializeOnboarding,
  handleOnboardingMessage,
  setNeedInitialMessages,
} from "@/store/slices/jobSlice";
import {
  PlusIcon,
  Activity,
  Calendar as CalendarIcon,
  Book,
  Brain,
  Search,
  Loader2,
  Send,
  Airplay,
  Pocket,
  TentTree,
  FileImage,
  Vegan,
  <PERSON><PERSON>ef<PERSON>,
} from "lucide-react";
import { ReactSVG } from "react-svg";
import { useStats } from "@/context/StatsContext";
import { useEmotional } from "@/context/EmotionalContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SuggestionPills } from "@/components/ui/suggestion-pills";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { cn } from "@/app/lib/utlis";
import { motion, AnimatePresence } from "framer-motion";
import UserInput from "@/components/Chat/UserInput";
import { useDispatch, useSelector } from "react-redux";
import toast from "react-hot-toast";
import axios from "axios";

// shadcn UI components
import { Card, CardContent, CardHeader } from "@/components/ui/card";

// Updated Agents section using DropdownCoach icons/colors/descriptions
const agents = [
  {
    id: "strength",
    title: "Strength Coach",
    description: "Specialises in strength and conditioning.",
    icon: (
      <ReactSVG
        src="/images/icon/Strength-Single.svg"
        className="h-3.5 w-3.5"
        beforeInjection={(svg) => {
          svg.setAttribute("width", "100%");
          svg.setAttribute("height", "100%");
          svg.querySelectorAll("path").forEach((path) => {
            path.setAttribute("fill", "#ffffff"); // Set fill to white for contrast
          });
        }}
      />
    ),
    iconBg: "bg-[#6580F4]", // from domainColors
    artifact: "Training",
  },
  {
    id: "cycling",
    title: "Cycling Coach",
    description: "Specialises in everything cycling related.",
    icon: (
      <ReactSVG
        src="/images/icon/Cycling-Single.svg"
        className="h-3.5 w-3.5"
        beforeInjection={(svg) => {
          svg.setAttribute("width", "100%");
          svg.setAttribute("height", "100%");
          svg.querySelectorAll("path").forEach((path) => {
            path.setAttribute("fill", "#ffffff");
          });
        }}
      />
    ),
    iconBg: "bg-[#37C6C4]", // from domainColors
    artifact: "Training", // Assigning Training artifact
  },
  {
    id: "running",
    title: "Running Coach",
    description: "Specialises in everything running related.",
    icon: (
      <ReactSVG
        src="/images/icon/Running-Single.svg"
        className="h-3.5 w-3.5"
        beforeInjection={(svg) => {
          svg.setAttribute("width", "100%");
          svg.setAttribute("height", "100%");
          svg.querySelectorAll("path").forEach((path) => {
            path.setAttribute("fill", "#ffffff");
          });
        }}
      />
    ),
    iconBg: "bg-[#D343DB]", // from domainColors
    artifact: "Training", // Assigning Training artifact
  },
  {
    id: "nutrition",
    title: "Nutrition Coach",
    description: "Specialises in dietary planning and nutrition optimization.",
    icon: (
      <ReactSVG
        src="/images/icon/Nutrition-Single.svg"
        className="h-3.5 w-3.5"
        beforeInjection={(svg) => {
          svg.setAttribute("width", "100%");
          svg.setAttribute("height", "100%");
          svg.querySelectorAll("path").forEach((path) => {
            path.setAttribute("fill", "#ffffff");
          });
        }}
      />
    ),
    iconBg: "bg-[#4CAF50]", // Green color for nutrition
    artifact: "Training", // Assigning Training artifact
  },
  {
    id: "recovery",
    title: "Recovery Coach",
    description: "Specialises in recovery techniques and injury prevention.",
    icon: (
      <ReactSVG
        src="/images/icon/Recovery-Single.svg"
        className="h-3.5 w-3.5"
        beforeInjection={(svg) => {
          svg.setAttribute("width", "100%");
          svg.setAttribute("height", "100%");
          svg.querySelectorAll("path").forEach((path) => {
            path.setAttribute("fill", "#ffffff");
          });
        }}
      />
    ),
    iconBg: "bg-[#FD8C5B]", // from domainColors
    artifact: "Stats", // Assigning Stats artifact
  },
  {
    id: "mental",
    title: "Mental Coach",
    description:
      "Enhance performance through mental techniques and strategies.", // Reusing original description
    icon: <Brain className="h-3.5 w-3.5 text-white" />, // Changed size and color
    iconBg: "bg-purple-500", // Changed to solid purple background for better contrast
    artifact: "Emotion",
  },
];

// Define panel cards structure for the bottom section (replacing Announcements)
const panels = [
  {
    id: "calendar",
    title: "Calendar",
    date: "Today",
    icon: <CalendarIcon className="h-5 w-5 text-blue-500" />,
    artifact: "Calendar",
  },
  {
    id: "glossary",
    title: "Glossary",
    date: "Updated",
    icon: <Book className="h-5 w-5 text-purple-500" />,
    artifact: "Glossary",
  },
  {
    id: "trainingplan",
    title: "Current Plan",
    date: "Today",
    icon: <Activity className="h-5 w-5 text-green-500" />,
    artifact: "NewPlan",
  },
  {
    id: "stats",
    title: "Training Stats",
    date: "Last Week",
    icon: <Activity className="h-5 w-5 text-orange-500" />,
    artifact: "Stats",
  },
];

// Re-introduce chatOptions map
const chatOptions: { [key: string]: string[] } = {
  "Make a plan": [
    "for cycling training",
    "for running training",
    "for strength training",
    "for recovery days",
    "for my nutrition",
  ],
  "How can I": [
    "find nearby gyms",
    "start cycling training",
    "improve my running",
    "build more strength",
    "eat better for training",
  ],
  "Create a": [
    "weekly workout schedule",
    "cycling program",
    "running plan",
    "strength routine",
    "meal plan",
  ],
  "Show me": [
    "my current plan",
    "nearby training spots",
    "my training history",
    "local running routes",
    "my progress",
  ],
  Design: [
    "a workout routine",
    "a training schedule",
    "a recovery plan",
    "a nutrition program",
    "a fitness plan",
  ],
};

// Icons for primary suggestions
const suggestionIcons: { [key: string]: React.JSX.Element } = {
  "Make a plan": <Airplay className="mr-2 h-4 w-4" />,
  "How can I": <Pocket className="mr-2 h-4 w-4" />,
  "Create a": <TentTree className="mr-2 h-4 w-4" />,
  "Show me": <FileImage className="mr-2 h-4 w-4" />,
  Design: <Vegan className="mr-2 h-4 w-4" />,
};

// Add interface for coach messages
interface CoachMessage {
  id: string;
  message: string;
  loading: boolean;
}

// Removed props from interface
interface HomeViewProps {}

const HomeView: React.FC<HomeViewProps> = () => {
  const dispatch = useAppDispatch();
  const router = useRouter(); // Need router for navigate
  const searchParams = useSearchParams();
  const user_id = useAppSelector(
    (state: RootState) => state.user.userData?.user_id,
  ); // Get userId
  const firstName = useAppSelector(
    (state: RootState) => state.user.userData?.given_name,
  );
  const { showGlossary, setShowGlossary } = useGlossary();
  const { showTrainingProfile, setShowTrainingProfile } = useTrainingProfile();
  const { showStats } = useStats();
  const { showEmotional, setShowEmotional } = useEmotional();

  // Add this line to get the current selected chat option from Redux
  const chatOption = useAppSelector((state: RootState) => state.job.chatOption);
  const isOnboardingInitializingFromStore = useAppSelector(
    (state: RootState) => state.job.isOnboardingInitializing,
  );

  // --- Input State and Logic ---
  const [searchInput, setSearchInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);
  const chatOptionRef = useRef<HTMLDivElement>(null); // Ref for the chat options list

  // --- Restore State for Chat Options List ---
  const [isChatListOpen, setIsChatListOpen] = useState<boolean>(false);
  const [chatOptionsList, setChatOptionsList] = useState<string[]>([]);
  // -----------------------------------------

  // Add a local state for tracking onboarding status
  const [showOnboarding, setShowOnboarding] = useState(true);

  // Add loading state for onboarding button
  const [isOnboardingLoading, setIsOnboardingLoading] = useState(false);
  const userDataLoaded = useAppSelector(
    (state: RootState) => !!state.user.userData,
  );

  // Add state for coach messages
  const [coachMessages, setCoachMessages] = useState<{
    [key: string]: CoachMessage;
  }>({});
  const userData = useAppSelector((state: RootState) => state.user.userData);

  // Add state for onboarding status
  const [onboardingStatus, setOnboardingStatus] = useState<{
    hasExistingData: boolean;
    threadId: string | null;
    loading: boolean;
  }>({
    hasExistingData: false,
    threadId: null,
    loading: true,
  });

  // Function to check for existing onboarding data
  const checkOnboardingStatus = useCallback(async () => {
    if (!user_id) {
      setOnboardingStatus({
        hasExistingData: false,
        threadId: null,
        loading: false,
      });
      return;
    }

    try {
      console.log(
        `[HomeView] Checking existing onboarding data for user: ${user_id}`,
      );
      const response = await fetch(
        `/api/users/${user_id}?onboarding_data=true`,
      );

      if (response.ok) {
        const result = await response.json();
        console.log(`[HomeView] Onboarding status check result:`, result);

        // Check if user has onboarding data - look for thread_id and sidebar_data
        const hasOnboardingData = !!(
          result.onboarding_thread_id &&
          result.onboarding_sidebar_data &&
          result.onboarding_last_updated
        );

        if (hasOnboardingData) {
          setOnboardingStatus({
            hasExistingData: true,
            threadId: result.onboarding_thread_id,
            loading: false,
          });
          console.log(
            `[HomeView] Found existing onboarding data with thread: ${result.onboarding_thread_id}`,
          );
          console.log(
            `[HomeView] Onboarding stage: ${result.onboarding_sidebar_data?.current_stage}`,
          );
          console.log(
            `[HomeView] Last updated: ${result.onboarding_last_updated}`,
          );
        } else {
          setOnboardingStatus({
            hasExistingData: false,
            threadId: null,
            loading: false,
          });
          console.log(`[HomeView] No existing onboarding data found`);
        }
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error(`[HomeView] Error checking onboarding status:`, error);
      setOnboardingStatus({
        hasExistingData: false,
        threadId: null,
        loading: false,
      });
    }
  }, [user_id]);

  // Search submit handler (remains largely the same)
  const handleHomeSearchSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchInput.trim() || !user_id) {
      if (!user_id) toast.error("Could not start chat. Please log in again.");
      return;
    }
    setIsLoading(true);
    try {
      dispatch(setInitialMessage(searchInput.trim()));
      dispatch(setIsHomeInputSubmitted(true));
      await dispatch(
        handleCreateNewChatClick({
          user_id: user_id,
          navigate: router.push,
        }),
      );
      setSearchInput("");
      setIsChatListOpen(false); // Close list on submit
      dispatch(setChatOption("")); // Clear chat option
      setIsInputFocused(false);
    } catch (error) {
      toast.error("Failed to start new chat.");
    } finally {
      setIsLoading(false);
    }
  };

  // --- Restore Suggestion Pill Click Handler ---
  const handleSuggestionPillClick = (suggestion: string) => {
    console.log("---suggestion---pills---", suggestion);
    // alert(suggestion)
    dispatch(setChatOption(suggestion));
    setChatOptionsList(chatOptions[suggestion]);
    setIsChatListOpen(true);
    setIsInputFocused(true); // Keep focus when list opens

    // Prevent focus from being lost after suggestion is clicked
    setTimeout(() => {
      const inputElement = document.querySelector('input[type="text"]');
      if (inputElement instanceof HTMLInputElement) {
        inputElement.focus();
      }
    }, 0);
  };
  // -------------------------------------------
  // Click Outside Handler for the *Chat Options List*
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check click is outside the chatOptionRef (the list)
      if (
        chatOptionRef.current &&
        !chatOptionRef.current.contains(event.target as Node)
      ) {
        // Also check if click is not on the input itself
        if (
          !(
            searchInputRef.current &&
            searchInputRef.current.contains(event.target as Node)
          )
        ) {
          setIsChatListOpen(false);
          dispatch(setChatOption(""));
          setIsInputFocused(false); // Remove focus when clicking outside list & input
        }
      }
    };
    // Only add listener when the list is open
    if (isChatListOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
    // Depend on isChatListOpen to add/remove listener correctly
  }, [isChatListOpen, dispatch]);

  // Handle agent card click (remains)
  const handleAgentClick = useCallback(
    (agentId: string, artifact: string) => {
      dispatch(setSelectedArtifact(artifact));
      if (artifact === "Emotion") setShowEmotional(true);
      else if (artifact === "Calendar") dispatch(setIsOpen(true));
      else if (artifact === "Glossary") setShowGlossary(true);
      else if (artifact === "Training") {
        // For coaches that should initiate a coach conversation
        if (
          ["strength", "cycling", "running", "nutrition", "recovery"].includes(
            agentId,
          )
        ) {
          // Convert id to properly formatted coach name
          const coachName = agentId.charAt(0).toUpperCase() + agentId.slice(1);

          if (user_id) {
            dispatch(
              handleCoachClick({
                coachName,
                user_id,
                navigate: router.push,
              }),
            );
          } else {
            console.error("User ID not available");
          }
        } else {
          setShowTrainingProfile(true);
        }
      }
    },
    [
      dispatch,
      setShowEmotional,
      setIsOpen,
      setShowGlossary,
      user_id,
      router.push,
      setShowTrainingProfile,
      handleCoachClick, // Assuming handleCoachClick is stable or memoized itself
      setSelectedArtifact,
    ],
  );

  // Handle panel card click (remains)
  const handlePanelClick = (panelId: string, artifact: string) => {
    dispatch(setSelectedArtifact(artifact));
    if (artifact === "Emotion") setShowEmotional(true);
    else if (artifact === "Calendar") dispatch(setIsOpen(true));
    else if (artifact === "Glossary") setShowGlossary(true);
    else if (artifact === "Training") setShowTrainingProfile(true);
  };

  // Create a new custom agent (remains)
  const handleCreateAgent = () => {
    console.log("Create new agent clicked");
  };

  // Add a function to handle the onboarding flow
  const startOnboarding = async () => {
    if (isOnboardingLoading || isOnboardingInitializingFromStore) {
      console.log("[HomeView] Onboarding already in progress, ignoring click.");
      return;
    }

    // Guard against missing user ID
    if (!user_id) {
      console.error(
        "[HomeView] Cannot initialize onboarding - user_id is missing",
      );
      toast.error("Cannot start onboarding. Please log in again.");
      return;
    }

    console.log("[HomeView] Starting onboarding flow for user:", user_id);
    setIsOnboardingLoading(true);

    try {
      // If we have existing data, navigate to the existing thread
      if (onboardingStatus.hasExistingData && onboardingStatus.threadId) {
        console.log(
          `[HomeView] Resuming existing onboarding with thread: ${onboardingStatus.threadId}`,
        );
        router.push(`/onboarding/${onboardingStatus.threadId}?onboarding=true`);
        return;
      }

      // Otherwise, create a new onboarding session
      console.log("[HomeView] Creating new onboarding session");

      // Step 1: Initialize onboarding to get a job ID
      const jobId = await dispatch(initializeOnboarding({ user_id })).unwrap();

      if (!jobId || typeof jobId !== "string" || jobId.trim() === "") {
        throw new Error("Failed to get a valid job ID for onboarding");
      }

      console.log(
        "[HomeView] Successfully initialized onboarding with jobId:",
        jobId,
      );

      // Step 2: IMMEDIATELY mark onboarding as started in frontend database
      console.log("[HomeView] Marking onboarding as started in database");
      try {
        const markStartedResponse = await fetch("/api/users", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: user_id,
            action: "start_onboarding",
            thread_id: jobId,
          }),
        });

        if (!markStartedResponse.ok) {
          console.warn(
            "[HomeView] Failed to mark onboarding as started, continuing anyway",
          );
        } else {
          console.log("[HomeView] Successfully marked onboarding as started");
          // Update local state immediately
          setOnboardingStatus({
            hasExistingData: true,
            threadId: jobId,
            loading: false,
          });
        }
      } catch (error) {
        console.warn("[HomeView] Error marking onboarding as started:", error);
        // Continue with onboarding even if this fails
      }

      // Step 3: Navigate to the onboarding page with direct_onboarding=true parameter
      console.log(
        "[HomeView] Navigating to onboarding chat page with direct onboarding route",
      );
      router.push(`/onboarding/${jobId}?direct_onboarding=true`);

      // Set the flag to trigger the initial message/graph start on the chat page
      dispatch(setNeedInitialMessages(true));

      // Set the specific initial message for onboarding
      dispatch(setInitialMessage("Hi, how can you help me today?"));

      console.log("[HomeView] Navigation initiated to direct onboarding route");
    } catch (error) {
      console.error("[HomeView] Error during onboarding process:", error);
      toast.error("Failed to start onboarding. Please try again.");
      setShowOnboarding(true); // Show the panel again if there was an error
    } finally {
      setIsOnboardingLoading(false);
    }
  };

  // Fetch coach messages when userData is available
  useEffect(() => {
    const fetchCoachMessages = async () => {
      // Only fetch messages if userData is available
      if (!userData || !userData.user_id) return;

      // Define coach IDs to get messages for
      const coachIds = ["mental", "recovery", "strength", "nutrition"];

      // Initialize all coaches with loading state
      const initialMessages: { [key: string]: CoachMessage } = {};
      coachIds.forEach((id) => {
        initialMessages[id] = {
          id,
          message: `Welcome${firstName ? `, ${firstName}` : ""}!`,
          loading: true,
        };
      });
      setCoachMessages(initialMessages);

      // Fetch messages for each coach
      coachIds.forEach(async (coachId) => {
        try {
          const response = await fetch("/api/coach-messages", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              coachId,
              userData,
            }),
          });

          if (!response.ok) {
            throw new Error(`Error fetching message for ${coachId}`);
          }

          const data = await response.json();

          setCoachMessages((prev) => ({
            ...prev,
            [coachId]: {
              id: coachId,
              message: data.message,
              loading: false,
            },
          }));
        } catch (error) {
          console.error(`Error fetching message for ${coachId}:`, error);

          // Set error state but keep showing default message
          setCoachMessages((prev) => ({
            ...prev,
            [coachId]: {
              id: coachId,
              message:
                prev[coachId]?.message ||
                `Welcome${firstName ? `, ${firstName}` : ""}! Let&#39;s focus on ${coachId}.`,
              loading: false,
            },
          }));
        }
      });
    };

    fetchCoachMessages();
    handleCronofyCallback(userData.user_id);
  }, [userData, firstName]);

  // Check onboarding status when user data is available
  useEffect(() => {
    if (userData && userData.user_id) {
      checkOnboardingStatus();
    }
  }, [userData, checkOnboardingStatus]);

  const handleCronofyCallback = async (userId: any) => {
    const code = searchParams.get("code");

    if (code) {
      try {
        // Construct the callback URL with only the code parameter
        const callbackUrl = `/api/auth/cronofy/callback?code=${code}&userId=${userId}`;

        // Make the API call
        const response = await fetch(callbackUrl);
        console.log("---response---", response);

        if (!response.ok) {
          throw new Error("Failed to process Cronofy callback");
        }

        // If successful, the API will handle the redirect
        console.log("Cronofy callback processed successfully");
      } catch (error) {
        console.error("Error processing Cronofy callback:", error);
        toast.error("Failed to connect calendar. Please try again.");
      }
    }
  };

  return (
    <div className="flex h-full w-full flex-col px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16">
      {/* Input Container with responsive spacing */}
      <div
        ref={inputContainerRef}
        className={cn(
          "flex w-full flex-col items-center pt-8 sm:pt-12 md:pt-16 lg:pt-24",
          "mb-8 sm:mb-12 lg:mb-16",
        )}
      >
        {/* Greeting Text - responsive sizing */}
        <div className="mb-4 w-full max-w-3xl text-left">
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl">
            Hello{firstName ? `, ${firstName}` : ""}.
          </h1>
          <h2 className="mt-2 text-2xl font-semibold tracking-tight sm:text-3xl lg:text-4xl">
            how can I help?
          </h2>
        </div>

        {/* Use the UserInput component instead of the custom form */}
        <div className="w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-3xl">
          <UserInput
            isHomePage={true}
            isCreatingChat={isLoading}
            onFocusChange={(focused) => setIsInputFocused(focused)}
            onSubmitHomeMessage={(searchInput) => {
              if (!searchInput.trim() || !user_id) {
                if (!user_id)
                  toast.error("Could not start chat. Please log in again.");
                return;
              }
              dispatch(setInitialMessage(searchInput.trim()));
              dispatch(setIsHomeInputSubmitted(true));
              dispatch(
                handleCreateNewChatClick({
                  user_id: user_id,
                  navigate: router.push,
                }),
              );
              setIsChatListOpen(false); // Close list on submit
              dispatch(setChatOption("")); // Clear chat option
              setIsInputFocused(false);
            }}
          />
        </div>

        {/* Container for Suggestions - responsive width */}
        <div className="relative w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-3xl">
          <AnimatePresence>
            {/* Container for motion divs - Add absolute positioning */}
            <div className="absolute left-0 right-0 top-full z-10 mt-2 w-full">
              {isChatListOpen ? (
                <motion.div
                  key="chat-options-list"
                  initial={{ opacity: 0, y: -10 }} // Adjust y for coming from below
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10, transition: { duration: 0.2 } }} // Adjust y
                  transition={{
                    duration: 0.3,
                    type: "spring",
                    stiffness: 120,
                    damping: 15,
                  }}
                  className="suggestion-pills-container w-full rounded-md border bg-white p-2 shadow-lg"
                  ref={chatOptionRef}
                >
                  <ScrollArea
                    className="suggestion-pills-container scrollbar-hide w-full"
                    style={{ maxHeight: "150px" }}
                  >
                    {chatOptionsList.map((item: string, index: number) => (
                      <p
                        key={index}
                        className={`text-md text-gray-700 hover:bg-gray-100 flex h-10 cursor-pointer items-center px-4 font-normal hover:rounded-lg ${index !== chatOptionsList.length - 1 ? "border-gray-100 border-b" : ""} hover:border-0`}
                        onClick={() => {
                          const fullMessage = [chatOption, item].join(" ");
                          console.log("[HomeView] Chat option selected:", {
                            chatOption,
                            item,
                            fullMessage,
                          });
                          dispatch(setInitialMessage(fullMessage));
                          dispatch(setIsHomeInputSubmitted(true));
                          dispatch(
                            handleCreateNewChatClick({
                              user_id: user_id,
                              navigate: router.push,
                            }),
                          );
                          setIsChatListOpen(false);
                          dispatch(setChatOption(""));
                        }}
                      >
                        <span className="text-gray-400 mr-1">{chatOption}</span>
                        {item}
                      </p>
                    ))}
                  </ScrollArea>
                </motion.div>
              ) : (
                // Show pills only when input is focused and list is NOT open
                isInputFocused && (
                  <motion.div
                    key="suggestion-pills"
                    initial={{ opacity: 0, y: -10 }} // Adjust y
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10, transition: { duration: 0.2 } }} // Adjust y
                    transition={{
                      duration: 0.3,
                      type: "spring",
                      stiffness: 120,
                      damping: 15,
                    }}
                  >
                    <ScrollArea className="scrollbar-hide w-full rounded-md">
                      <SuggestionPills
                        onCompareClick={() => {}}
                        onItemClick={handleSuggestionPillClick} // Use restored handler
                      />
                    </ScrollArea>
                  </motion.div>
                )
              )}
            </div>
          </AnimatePresence>
        </div>
        {/* ---------------------------------------------------------- */}
      </div>

      {/* Onboarding Panel */}
      {(showOnboarding || true) && (
        <div className="dark:bg-gray-800 dark:border-gray-700 mb-8 w-full rounded-lg border bg-white p-6 shadow-sm sm:mb-12">
          {onboardingStatus.loading ? (
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-purple-600" />
              <div>
                <h2 className="text-xl font-bold tracking-tight dark:text-white">
                  Loading...
                </h2>
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Checking your onboarding status
                </p>
              </div>
            </div>
          ) : onboardingStatus.hasExistingData ? (
            <>
              <h2 className="mb-3 text-2xl font-bold tracking-tight dark:text-white">
                Resume Your Onboarding
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mb-6 text-lg">
                We found your previous onboarding session! Continue where you
                left off to complete your personalized training setup with
                Athlea.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button
                  className="bg-purple-600 text-white hover:bg-purple-700"
                  disabled={isOnboardingLoading || !userDataLoaded}
                  onClick={startOnboarding}
                >
                  {isOnboardingLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Loading...</span>
                    </div>
                  ) : (
                    "Resume Onboarding"
                  )}
                </Button>
                <Button
                  variant="outline"
                  disabled={isOnboardingLoading}
                  onClick={() => {
                    // Close the panel
                    setShowOnboarding(false);
                    console.log("Close onboarding panel");
                  }}
                >
                  Maybe Later
                </Button>
              </div>
            </>
          ) : (
            <>
              <h2 className="mb-3 text-2xl font-bold tracking-tight dark:text-white">
                Welcome to Athlea Training
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mb-6 text-lg">
                Get started with a quick tour of our platform and discover how
                it can help optimize your training. Learn about our specialized
                coaches and personalized training plans.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button
                  className="bg-purple-600 text-white hover:bg-purple-700"
                  disabled={isOnboardingLoading || !userDataLoaded}
                  onClick={startOnboarding}
                >
                  {isOnboardingLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Loading...</span>
                    </div>
                  ) : (
                    "Start Onboarding"
                  )}
                </Button>
                <Button
                  variant="outline"
                  disabled={isOnboardingLoading}
                  onClick={() => {
                    // Close the panel
                    setShowOnboarding(false);
                    console.log("Close onboarding panel");
                  }}
                >
                  Maybe Later
                </Button>
              </div>
            </>
          )}
        </div>
      )}

      {/* Agents section - Responsive Grid */}
      <div
        className={cn(
          "mb-8 w-full transition-all duration-200 sm:mb-12",
          isInputFocused
            ? "pointer-events-none opacity-40 backdrop-blur-sm"
            : "opacity-100",
        )}
      >
        <h2 className="mb-4 text-xl font-semibold sm:text-2xl">
          Here&apos;s your team!
        </h2>

        {/* useMemo block to filter and render team agents */}
        {useMemo(() => {
          // Add nutrition to the team
          const teamAgentIds = ["mental", "recovery", "strength", "nutrition"];
          const teamAgents = agents.filter((agent) =>
            teamAgentIds.includes(agent.id),
          );

          // Conditionally render based on userDataLoaded
          if (!userDataLoaded) {
            // Optional: Render a loading state or placeholder for the cards
            return (
              <div className="text-gray-500 text-center">
                Loading team information...
              </div>
            );
          }

          return (
            <>
              {/* Mobile: horizontal scroll */}
              <div className="sm:hidden">
                <ScrollArea className="w-full rounded-md">
                  <div className="flex space-x-4 p-4">
                    {teamAgents.map((agent) => {
                      const coachMessage = coachMessages[agent.id];
                      return (
                        <Card
                          key={agent.id}
                          className="hover:bg-gray-50 flex-shrink-0 transform cursor-pointer transition duration-200 ease-in-out hover:scale-105 hover:shadow-lg"
                          style={{ width: "280px" }}
                          onClick={() =>
                            handleAgentClick(agent.id, agent.artifact)
                          }
                        >
                          <CardHeader className="pb-2">
                            <div className="flex items-center gap-2">
                              <div
                                className={`flex h-6 w-6 items-center justify-center rounded-full ${agent.iconBg}`}
                              >
                                {agent.icon}
                              </div>
                              <h3 className="font-medium">{agent.title}</h3>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-gray-600 mt-2 whitespace-normal text-sm">
                              {coachMessage?.loading ? (
                                <span className="flex items-center gap-2">
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                  Loading message...
                                </span>
                              ) : (
                                coachMessage?.message ||
                                `Welcome${firstName ? `, ${firstName}` : ""}!`
                              )}
                            </p>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                  <ScrollBar orientation="horizontal" className="mt-1" />
                </ScrollArea>
              </div>

              {/* Responsive grid for tablet and up */}
              <div className="hidden grid-cols-1 gap-4 sm:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {teamAgents.map((agent) => {
                  const coachMessage = coachMessages[agent.id];
                  return (
                    <Card
                      key={agent.id}
                      className="hover:bg-gray-50 transform cursor-pointer transition duration-200 ease-in-out hover:scale-105 hover:shadow-lg"
                      onClick={() => handleAgentClick(agent.id, agent.artifact)}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center gap-2">
                          <div
                            className={`flex h-6 w-6 items-center justify-center rounded-full ${agent.iconBg}`}
                          >
                            {agent.icon}
                          </div>
                          <h3 className="font-medium">{agent.title}</h3>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-600 mt-2 whitespace-normal text-sm">
                          {coachMessage?.loading ? (
                            <span className="flex items-center gap-2">
                              <Loader2 className="h-3 w-3 animate-spin" />
                              Loading message...
                            </span>
                          ) : (
                            coachMessage?.message ||
                            `Welcome${firstName ? `, ${firstName}` : ""}!`
                          )}
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </>
          );
        }, [userDataLoaded, handleAgentClick, coachMessages])}
      </div>
    </div>
  );
};

export default HomeView;
