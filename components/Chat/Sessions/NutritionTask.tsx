import NutritionGraph from "@/components/Graphs/NutritionGraph";
import React, { FC } from "react";
import {
  Draggable,
  DraggableProvided,
  DraggableStateSnapshot,
} from "react-beautiful-dnd";
import { useDispatch } from "react-redux";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import toast from "react-hot-toast";
import { v4 as uuidv4 } from "uuid";
import useCalendarToggle from "@/hooks/useCalendarToggle";
import { CronofyEventUtil } from "@/utils/CronofyEventUtil";
import { RootState } from "@/store/store";
import { useAppSelector } from "@/store/jobHook";

interface MealIngredient {
  name: string;
  quantity: string;
  unit?: string;
  calories: number;
}

interface MealItem {
  description: string;
  ingredients: MealIngredient[];
  preparation?: string;
  total_calories: number;
}

interface Meal {
  items: MealItem[];
  meal_calories: number;
}

interface DailyPlan {
  breakfast: Meal;
  morning_snack: Meal;
  lunch: Meal;
  afternoon_snack: Meal;
  dinner: Meal;
  daily_total_calories: number;
}

interface Macronutrients {
  protein?: string;
  carbohydrates?: string;
  fat?: string;
}

interface NutritionTaskProps {
  task: {
    session_type: string;
    caloric_intake?: string;
    macronutrients?: Macronutrients;
    meals?: DailyPlan;
    day?: string;
    rationale?: string;
  };
  msgId?: number;
  index?: number;
}

const parseValue = (value?: string) => {
  if (!value) return 0;
  return value.includes("%")
    ? parseInt(value.replace("%", ""), 10)
    : parseInt(value, 10);
};

const NutritionTask: FC<NutritionTaskProps> = ({ task, msgId, index = 0 }) => {
  const dispatch = useDispatch();
  const { taskDivRef } = useCalendarToggle();
  const userId = useAppSelector(
    (state) => state.user.userData?.user_id,
  );
  const { cronofyCalendars } = useAppSelector(
    (state: RootState) => state.calendar,
  );

  // Use the meals data if available, or fall back to the caloric_intake and macronutrients
  const totalCalories =
    task.meals?.daily_total_calories ||
    parseInt(task.caloric_intake || "0", 10);

  // Determine macros from either the structured data or the legacy format
  const protein = task.macronutrients?.protein || "";
  const carbs = task.macronutrients?.carbohydrates || "";
  const fats = task.macronutrients?.fat || "";

  const graphsData = [
    {
      label: "Energy",
      value: totalCalories,
      unit: "kcal",
      max: 2900,
      color: "#88D26F",
      markers: [2900],
    },
    {
      label: "Carbs",
      value: parseValue(carbs),
      max: 480,
      color: "#0D99FF",
      markers: [360, 480],
    },
    {
      label: "Protein",
      value: parseValue(protein),
      unit: "g",
      max: 130,
      color: "#3FDAE1",
      markers: [110, 130],
    },
    {
      label: "Fats",
      value: parseValue(fats),
      unit: "g",
      max: 71,
      color: "#FFA629",
      markers: [17, 71],
    },
  ];

  const handleAddToCalendar = async() => {
    try {
      // Get today's date as default
      const today = new Date();
      const sessionDate = today.toISOString().split("T")[0]; // Format as YYYY-MM-DD

      // Create the session task with current task data
      const sessionTask = {
        ...task,
        session_type: "nutrition",
        id: uuidv4(),
        isTemp: true,
      };

      // Dispatch the action to add the session to the calendar
      dispatch({
        type: "calendar/addSession",
        payload: {
          session: sessionTask,
          coach: "nutrition_coach",
          sessionDate,
        },
      });

      toast.success("Nutrition plan added to calendar");
          // Create Cronofy event using the utility class
          const calendarId = cronofyCalendars?.[0]?.calendar_id;
          if (calendarId) {
            await CronofyEventUtil.createEvent(userId, sessionTask, calendarId);
          } else {
            console.error('No calendar ID available');
            toast.error('Failed to add event to calendar - no calendar selected');
          }
    } catch (error) {
      console.error("Failed to add nutrition plan to calendar:", error);
      toast.error("Failed to add nutrition plan to calendar");
    }
  };

  // Helper function to render a meal section
  const renderMeal = (meal: Meal, title: string) => {
    if (!meal || !meal.items || meal.items.length === 0) return null;

    return (
      <div className="mb-6">
        <h4 className="mb-2 text-lg font-medium">{title}</h4>
        <div className="pl-4">
          {meal.items.map((item, idx) => (
            <div key={idx} className="mb-4 rounded-md bg-slate-50 p-3">
              <div className="mb-1 font-medium">{item.description}</div>
              {item.preparation && (
                <div className="mb-2 text-sm text-slate-600">
                  <span className="font-medium">Preparation: </span>
                  {item.preparation}
                </div>
              )}
              <div className="mb-2 text-sm text-slate-600">
                <span className="font-medium">Calories: </span>
                {item.total_calories} kcal
              </div>
              <div className="text-sm">
                <span className="font-medium">Ingredients:</span>
                <ul className="mt-1 list-disc pl-5">
                  {item.ingredients.map((ingredient, ingIdx) => (
                    <li key={ingIdx}>
                      {ingredient.name} - {ingredient.quantity}
                      {ingredient.unit ? ` ${ingredient.unit}` : ""} (
                      {ingredient.calories} kcal)
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Use a unique draggable ID based on msgId or index
  const draggableId = `draggable-nutrition-task-${msgId || index}`;

  return (
    <div ref={taskDivRef} className="w-full">
      <div className="mb-6 flex flex-row justify-between align-bottom">
        <h3 className="text-xl font-semibold dark:text-white">Nutrition</h3>
        <Button
          size="sm"
          onClick={handleAddToCalendar}
          className="flex items-center gap-1"
        >
          <Calendar className="h-4 w-4" />
          Add to Calendar
        </Button>
      </div>

      {task.rationale && (
        <div className="mb-4 rounded-md bg-slate-50 p-3">
          <div className="mb-1 font-medium">Rationale:</div>
          <div className="text-sm text-slate-600">{task.rationale}</div>
        </div>
      )}

      <div className="mb-6">
        <h4 className="mb-4 text-lg font-medium">Macronutrients</h4>
        <div className="space-y-4">
          {graphsData.map((data, index) => (
            <div key={index} className="flex flex-col gap-2">
              <div className="flex justify-between">
                <span className="font-medium">{data.label}:</span>
                <span>
                  {data.value} {data.unit} / {data.max} {data.unit}
                </span>
              </div>
              <NutritionGraph
                value={data.value}
                max={data.max}
                color={data.color}
                markers={data.markers}
                label={data.label}
              />
            </div>
          ))}
        </div>
      </div>

      {task.meals && (
        <div>
          <h4 className="mb-4 text-xl font-semibold">Daily Meal Plan</h4>
          {renderMeal(task.meals.breakfast, "Breakfast")}
          {renderMeal(task.meals.morning_snack, "Morning Snack")}
          {renderMeal(task.meals.lunch, "Lunch")}
          {renderMeal(task.meals.afternoon_snack, "Afternoon Snack")}
          {renderMeal(task.meals.dinner, "Dinner")}
          <div className="text-right font-medium">
            Total Daily Calories: {task.meals.daily_total_calories} kcal
          </div>
        </div>
      )}
    </div>
  );
};

export default NutritionTask;
