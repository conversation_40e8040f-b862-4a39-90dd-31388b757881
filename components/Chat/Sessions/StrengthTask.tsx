import StrengthGraph from "@/components/Graphs/StrengthGraph";
import WorkoutOverview from "@/components/Overviews/StrengthWorkoutOverview";
import useCalendarToggle from "@/hooks/useCalendarToggle";
import React, { FC, useMemo } from "react";
import {
  Draggable,
  DraggableProvided,
  DraggableStateSnapshot,
} from "react-beautiful-dnd";
import { useDispatch } from "react-redux";
import { Button } from "@/components/ui/button";
import { Calendar, Book, Search } from "lucide-react";
import toast from "react-hot-toast";
import { v4 as uuidv4 } from "uuid";
import { CronofyEventUtil } from "@/utils/CronofyEventUtil";
import { useAppSelector } from "@/store/jobHook";
import { RootState } from "@/store/store";

interface StrengthTaskProps {
  index: number;
  task: any;
  msgId?: number;
}

const StrengthTask: FC<StrengthTaskProps> = ({ task, index, msgId }) => {
  // const task = {
  //   "day": "Thursday, January 16, 2025",
  //   "session_type": "strength",
  //   "duration": 60,
  //   "intensity": "moderate",
  //   "segments": [
  //     {
  //       "name": "Warm-up",
  //       "sets": 1,
  //       "reps": 10,
  //       "duration": 10,
  //       "rest": 0,
  //       "load_percentage": 0,
  //       "body_part": "Other",
  //       "rationale": "Light mobility work to prepare the body for strength training, focusing on joint mobility and muscle activation."
  //     },
  //     {
  //       "name": "Plank",
  //       "sets": 4,
  //       "reps": 1,
  //       "duration": 4,
  //       "rest": 30,
  //       "load_percentage": 0,
  //       "body_part": "Core",
  //       "rationale": "Core stability exercise crucial for supporting lower back health; duration focused on endurance."
  //     },
  //     {
  //       "name": "Dead Bug",
  //       "sets": 3,
  //       "reps": 15,
  //       "duration": 6,
  //       "rest": 30,
  //       "load_percentage": 0,
  //       "body_part": "Core",
  //       "rationale": "Targets core stability and coordination, reducing lower back strain during movements."
  //     },
  //     {
  //       "name": "Russian Twist",
  //       "sets": 3,
  //       "reps": 20,
  //       "duration": 6,
  //       "rest": 30,
  //       "load_percentage": 0,
  //       "body_part": "Core",
  //       "rationale": "Enhances rotational core strength, which is essential for overall core stability."
  //     },
  //     {
  //       "name": "Cool Down",
  //       "sets": 1,
  //       "reps": 10,
  //       "duration": 10,
  //       "rest": 0,
  //       "load_percentage": 0,
  //       "body_part": "Other",
  //       "rationale": "Gentle stretching to aid recovery and flexibility, focusing on areas worked during the session."
  //     }
  //   ],
  //   "notes": null,
  //   "rationale": "This strength training session is designed to focus on core strength development, which is particularly important for supporting lower back health given the user's injury history. The session begins with a warm-up to prepare the body and mobilize the joints. The main exercises—plank, dead bug, and Russian twist—target core stability and strength, enhancing functionality and reducing the risk of aggravating the lower back injury. The session is structured to gradually build endurance in the core while ensuring proper form and stability. Finally, a cool-down allows for muscle recovery and flexibility, which is essential for overall performance and injury prevention."
  // }
  console.log("StrengthTask", task);
  const { taskDivRef } = useCalendarToggle();
  const dispatch = useDispatch();
  const userId = useAppSelector(
    (state) => state.user.userData?.user_id,
  );
  const { cronofyCalendars } = useAppSelector(
    (state: RootState) => state.calendar,
  );

  const sessionType = task.session_type || task.sessionType || "strength";
  // const segments = useMemo(
  //   () => task.segments.filter((item: any) => item.load_percentage),
  //   [task.segments],
  // );

  let idPart: string | number | undefined = task.id;
  if (idPart === undefined) {
    idPart = msgId;
  }
  if (idPart === undefined) {
    idPart = index;
  }

  if (idPart === undefined) {
    console.warn(
      "StrengthTask: Critical error - task.id, msgId, and index are all undefined. This will cause a crash in react-beautiful-dnd. Task:",
      JSON.stringify(task),
    );
    // Potentially return a placeholder or error UI here to prevent a crash,
    // or ensure this state is impossible by design.
  }

  // Use the task's id if available, otherwise fall back to msgId or index
  const draggableId = `draggable-task_${idPart}_${sessionType}`;

  const handleAddToCalendar = async() => {
    try {
      // Get today's date as default
      const today = new Date();
      const sessionDate = today.toISOString().split("T")[0]; // Format as YYYY-MM-DD

      // Create the session task with current task data
      const sessionTask = {
        ...task,
        session_type: "strength",
        id: uuidv4(),
        isTemp: true,
      };

      // We're directly using Redux's dispatch function here, but you would normally use an action creator
      dispatch({
        type: "calendar/addSession",
        payload: {
          session: sessionTask,
          coach: "strength_coach",
          sessionDate,
        },
      });

      toast.success("Strength session added to calendar");
      const calendarId = cronofyCalendars?.[0]?.calendar_id;
      if (calendarId) {
        await CronofyEventUtil.createEvent(userId, sessionTask, calendarId);
      } else {
        console.error('No calendar ID available');
        toast.error('Failed to add event to calendar - no calendar selected');
      }
    } catch (error) {
      console.error("Failed to add session to calendar:", error);
      toast.error("Failed to add session to calendar");
    }
  };

  return (
    <div
      ref={taskDivRef}
      className="mx-auto flex w-full max-w-2xl flex-col gap-0 overflow-hidden rounded-2xl border border-[#E6E8EC] bg-[#F8F9FB] p-0 shadow-lg md:gap-4 md:p-3"
    >
      {/* Header Image and Video */}
      <Draggable key={index} draggableId={draggableId} index={index}>
            {(
              providedDraggable: DraggableProvided,
              snapshotDraggable: DraggableStateSnapshot,
            ) => (
      <div ref={providedDraggable.innerRef}
      {...providedDraggable.draggableProps}
      {...providedDraggable.dragHandleProps}>
      <div className="relative flex h-[140px] w-full items-center justify-center bg-[#E6E8EC] md:h-[200px]">
        {/* Replace with actual video/image component as needed */}
        <img
          src="https://images.pexels.com/photos/317157/pexels-photo-317157.jpeg"
          alt="1 to 2 Jump Box Demo"
          className="h-full w-full rounded-xl object-cover"
        />
        <button
          className="absolute left-3 top-3 rounded-md bg-[#E6E8EC] px-2 py-1 text-xs font-medium text-[#6B7280] shadow"
          style={{ fontSize: "12px" }}
        >
          Straight on
        </button>
        <button
          onClick={handleAddToCalendar}
          className="absolute right-3 top-3 flex items-center gap-1 rounded-md bg-[#E6E8EC] px-2 py-1 text-xs font-medium text-[#6B7280] shadow"
          style={{ fontSize: "12px" }}
        >
          <Calendar size={14} />
          Add to calendar
        </button>
        <button
          className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full border border-[#E6E8EC] bg-white bg-opacity-80 p-3 shadow-lg"
          aria-label="Play video"
        >
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
            <circle cx="16" cy="16" r="16" fill="#F8F9FB" />
            <path d="M13 11L21 16L13 21V11Z" fill="#6B7280" />
          </svg>
        </button>
      </div>
      {/* Thumbnails */}
      <div className="flex flex-row gap-2 border-b border-[#E6E8EC] bg-[#F8F9FB] px-4 py-2">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="flex h-12 w-12 items-center justify-center overflow-hidden rounded-lg border border-[#E6E8EC] bg-white"
          >
            <img
              src={`https://images.pexels.com/photos/3823039/pexels-photo-3823039.jpeg`}
              alt={`Thumbnail ${i}`}
              className="h-full w-full object-cover"
            />
          </div>
        ))}
      </div>
      {/* Title and Meta */}
      <div className="flex flex-col justify-between gap-2 px-4 pb-2 pt-4 md:flex-row md:items-center">
        <div>
          <div className="mb-1 flex flex-col items-start gap-0.5">
            <span className="text-xs text-[#6B7280]">
              1 × 30 seconds per side
            </span>
            <span className="text-xs text-[#6B7280]">
              ~ {task.duration} min
            </span>
          </div>
          <h2 className="text-lg font-bold text-[#222B45] md:text-xl">
            1 to 2 Jump Box
          </h2>
          <div className="mt-2 flex flex-wrap gap-2">
            <span className="rounded-full bg-[#E6E8EC] px-2 py-1 text-xs font-medium text-[#6B7280]">
              Body weight
            </span>
            <span className="rounded-full bg-[#E6E8EC] px-2 py-1 text-xs font-medium text-[#6B7280]">
              Aerobic
            </span>
            <span className="rounded-full bg-[#E6E8EC] px-2 py-1 text-xs font-medium text-[#6B7280]">
              Plyometrics
            </span>
            <span className="rounded-full bg-[#E6E8EC] px-2 py-1 text-xs font-medium text-[#6B7280]">
              Male
            </span>
          </div>
        </div>
      </div>
      {/* Description */}
      <div className="px-4 pb-4">
        <h3 className="mb-1 text-base font-semibold text-[#222B45]">
          Description:
        </h3>
        <p className="text-sm leading-relaxed text-[#6B7280]">
          The Adductor Brevis exercise is an excellent way to target and
          strengthen the inner thighs and hips, enhancing stability and mobility
          in these areas. This exercise not only tones the adductor muscles but
          also improves overall lower body strength and balance, contributing to
          better performance in various physical activities.
        </p>
      </div>
      {/* Info Grid */}
      <div className="flex flex-col gap-4 px-4 pb-4">
        <div className="flex flex-col items-center rounded-lg border border-[#E6E8EC] bg-[#F8F9FB] p-3">
          <span className="mb-1 text-xs font-medium text-[#6B7280]">
            Body areas:
          </span>
          <span className="text-center text-sm font-semibold text-[#222B45]">
            Glutes, Hips, Thighs, and Lower Legs
          </span>
        </div>
        <div className="flex flex-col items-center rounded-lg border border-[#E6E8EC] bg-[#F8F9FB] p-3">
          <span className="mb-1 text-xs font-medium text-[#6B7280]">
            Target Muscles
          </span>
          <span className="text-center text-sm font-semibold text-[#222B45]">
            Quadriceps and Gluteus Maximus
          </span>
        </div>
        <div className="flex flex-col items-center rounded-lg border border-[#E6E8EC] bg-[#F8F9FB] p-3">
          <span className="mb-1 text-xs font-medium text-[#6B7280]">
            Support Muscles
          </span>
          <span className="text-center text-sm font-semibold text-[#222B45]">
            Core Muscles and Hip Flexors
          </span>
        </div>
      </div>
      {/* Scientific Sources */}
      <div className="px-4 pb-4">
        <h3 className="mb-1 flex items-center gap-2 text-base font-semibold text-[#222B45]">
          <Book size={18} className="text-[#6B7280]" />
          Scientific Sources
        </h3>
        <div className="flex flex-col gap-2">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="flex flex-col gap-1 rounded-lg border border-[#E6E8EC] bg-white p-3"
            >
              <a
                href="#"
                className="text-xs font-semibold text-purple-600 hover:underline"
              >
                5 of the best exercises you can ever do
              </a>
              <span className="text-xs text-[#6B7280]">
                Publication, 2024 · You might call swimming the perfect workout
                — it&apos;s one of the few activities that combines full-body
                movement, relaxation, and low-impact resistance. Whether
                you&apos;re pushing for performance or just…
              </span>
              <div className="mt-1 flex flex-wrap gap-1">
                <span className="rounded-full bg-[#E6E8EC] px-2 py-0.5 text-[10px] font-medium text-[#6B7280]">
                  Strength & Power
                </span>
                <span className="rounded-full bg-[#E6E8EC] px-2 py-0.5 text-[10px] font-medium text-[#6B7280]">
                  Endurance
                </span>
                <span className="rounded-full bg-[#E6E8EC] px-2 py-0.5 text-[10px] font-medium text-[#6B7280]">
                  Speed & Agility
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Web Search */}
      <div className="px-4 pb-4">
        <h3 className="mb-1 flex items-center gap-2 text-base font-semibold text-[#222B45]">
          <Search size={18} className="text-[#6B7280]" />
          Search from the Web
        </h3>
        <div className="flex flex-col gap-2">
          <div className="flex flex-1 flex-col gap-1 rounded-lg border border-[#E6E8EC] bg-white p-3">
            <a
              href="#"
              className="text-xs font-semibold text-purple-600 hover:underline"
            >
              1 to 2 Jump Box
            </a>
            <img
              src="https://images.pexels.com/photos/3823039/pexels-photo-3823039.jpeg"
              alt="1 to 2 Jump Box"
              className="mt-2 h-20 w-20 rounded-lg object-cover"
            />
            <span className="text-xs text-[#6B7280]">
              A 1 to 2 jump* refers to a basic jump variation where…
            </span>
            <ul className="mt-1 list-disc pl-4 text-xs text-[#6B7280]">
              <li>Start standing, feet hip-width apart.</li>
              <li>
                Lower into a quarter squat, swing arms, and jump onto box with
                both feet.
              </li>
              <li>Land softly, stand, and step down.</li>
            </ul>
          </div>
          <div className="flex flex-1 flex-col gap-1 rounded-lg border border-[#E6E8EC] bg-white p-3">
            <a
              href="#"
              className="text-xs font-semibold text-purple-600 hover:underline"
            >
              2-Box Jump - YouTube
            </a>
            <img
              src="https://images.pexels.com/photos/3823039/pexels-photo-3823039.jpeg"
              alt="2-Box Jump - YouTube"
              className="mt-2 h-20 w-20 rounded-lg object-cover"
            />
            <span className="text-xs text-[#6B7280]">
              Strength & Power, Endurance, Speed & Agility
            </span>
          </div>
          <div className="flex flex-1 flex-col gap-1 rounded-lg border border-[#E6E8EC] bg-white p-3">
            <a
              href="#"
              className="text-xs font-semibold text-purple-600 hover:underline"
            >
              Jump Box Plyometric - Instagram
            </a>
            <img
              src="https://images.pexels.com/photos/3823039/pexels-photo-3823039.jpeg"
              alt="Jump Box Plyometric - Instagram"
              className="mt-2 h-20 w-20 rounded-lg object-cover"
            />
            <span className="text-xs text-[#6B7280]">
              Jumping on a box is a great way to build explosive power…
            </span>
          </div>
        </div>
      </div>
      </div>
        )}
          </Draggable>
    </div>
  );
};

export default StrengthTask;
