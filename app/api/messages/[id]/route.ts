import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";
import { Message, SystemMessage, SystemMessageType } from "@/types/chat";
import { Collection } from "mongodb";
import { TrainingPlan } from "@/types/plan";

interface RouteParams {
  params: Promise<{ id: string }>;
}

// Fetch messages from the database using the jobId
export async function GET(req: NextRequest, props: RouteParams) {
  const params = await props.params;
  console.log("GET request received!");
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const jobId = await params.id;
    console.log("Job ID:", jobId);

    if (!jobId) {
      console.log("Missing jobId");
      return new NextResponse(JSON.stringify({ error: "Missing jobId" }), {
        status: 400,
      });
    }

    const collection = db.collection("trainingPlans");
    const trainingPlan = await collection.findOne({ jobId });

    if (!trainingPlan) {
      console.log("No training plan found for jobId:", jobId);
      return new NextResponse(
        JSON.stringify({ messageList: [], lastMessage: null, coachName: null }),
        {
          status: 200,
        },
      );
    }

    const messageList = trainingPlan.messages || [];
    const artifacts = trainingPlan.artifacts || [];
    const sourceDocuments = trainingPlan.sourceDocuments || [];

    // Find the last message of type "question" or "task"
    const lastMessage =
      messageList
        .slice()
        .reverse()
        .find(
          (msg: Message) => msg.type === "question" || msg.type === "task",
        ) || null;

    console.log("Last message found:", lastMessage);

    return new NextResponse(
      JSON.stringify({
        sourceDocuments,
        artifacts,
        messageList,
        lastMessage,
        coachName: trainingPlan.coachName,
      }),
      {
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error fetching messages:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch messages" }),
      { status: 500 },
    );
  }
}

// Save the message list to the database using the jobId
export async function POST(req: NextRequest, props: RouteParams) {
  const params = await props.params;
  console.log("POST request received");
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const jobId = await params.id;
    console.log("Job ID:", jobId);

    let {
      sourceDocuments,
      toolCalls,
      messageList,
      initialMessage,
    }: {
      sourceDocuments: any[];
      toolCalls: any[];
      messageList: any[];
      initialMessage: any;
    } = await req.json();
    console.log("Message List length:", messageList.length);

    if (!jobId || !messageList) {
      console.log("Missing jobId or messageList");
      return new NextResponse(
        JSON.stringify({ error: "Missing jobId or messageList" }),
        { status: 400 },
      );
    }

    const collection = db.collection("trainingPlans");

    // Check if the training plan already exists
    const existingPlan = await collection.findOne({ jobId });

    if (existingPlan && existingPlan.messages) {
      console.log("Existing plan found. Updating messages.");
      let firstInitialMsg = null;

      const oldSystemMessages: any[] = existingPlan.messages.filter(
        (item: Message | SystemMessageType) => item.type === "system",
      );
      console.log("Old system messages count:", oldSystemMessages.length);

      if (initialMessage !== undefined && existingPlan.messages.length > 0) {
        firstInitialMsg = existingPlan.messages.find(
          (item: any) => item.type === "answer",
        );
      } else if (initialMessage !== undefined) {
        messageList = [
          {
            type: "answer",
            text: initialMessage,
            goalOptions: [],
            serviceName: "",
            timestamp: new Date().toISOString(),
          },
        ];
      }
      console.log("First initial message:", firstInitialMsg);

      let oldMessages: Message[] = existingPlan.messages;
      if (oldMessages.length > 0) {
        console.log("Updating message list with existing keywords");
        messageList = messageList.map((item: Message) => {
          const foundMsg = oldMessages.find(
            (olditem: Message) => olditem.id === item.id,
          );
          if (foundMsg && foundMsg.keywords) {
            item.keywords = foundMsg.keywords;
            return item;
          } else {
            return item;
          }
        });
      }

      const newSystemMessages = messageList.filter(
        (item: Message | SystemMessageType) => item.type === "system",
      );
      console.log("New system messages count:", newSystemMessages.length);

      if (oldSystemMessages.length > 0 && newSystemMessages.length === 0) {
        console.log("Appending old system messages to the message list");
        messageList = [...messageList, ...oldSystemMessages];
      } else if (messageList.length === 1 && newSystemMessages.length === 1) {
        messageList = [
          {
            ...newSystemMessages[0],
            type: "user",
            text: newSystemMessages[0].message,
          },
        ];
      }

      await collection.updateOne(
        { jobId },
        {
          $set: {
            sourceDocuments,
            artifacts: toolCalls,
            messages: firstInitialMsg
              ? [firstInitialMsg, ...messageList]
              : messageList,
          },
        },
      );
      console.log("Messages updated successfully");
    } else {
      console.log("No existing plan found. Creating a new one.");
      if (initialMessage !== undefined) {
        messageList = [
          ...messageList,
          {
            type: "answer",
            text: initialMessage,
            goalOptions: [],
            serviceName: "",
            timestamp: new Date().toISOString(),
          },
        ];
      }
      // Create a new training plan with dateAdded
      const dateAdded = new Date().toISOString();
      await collection.updateOne(
        { jobId },
        {
          $set: {
            sourceDocuments,
            artifacts: toolCalls,
            messages: messageList,
            dateAdded,
          },
        },
        { upsert: true },
      );
      console.log("New training plan created with messages");
    }

    return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
  } catch (error: any) {
    console.error("Error saving messages:", error);
    return new NextResponse(
      JSON.stringify({
        error: "Failed to save messages",
        details: error.message,
      }),
      { status: 500 },
    );
  }
}

export async function DELETE(req: NextRequest, props: RouteParams) {
  const params = await props.params;
  const { searchParams } = new URL(req.url);
  const clearAll = searchParams.get("clear_all") === "true";

  console.log(
    `DELETE request received to ${clearAll ? "clear all messages" : "remove plan messages"}`,
  );

  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const jobId = await params.id;
    console.log("Job ID:", jobId);

    if (!jobId) {
      console.log("Missing jobId");
      return new NextResponse(JSON.stringify({ error: "Missing jobId" }), {
        status: 400,
      });
    }

    const collection: Collection<TrainingPlan> = db.collection("trainingPlans");

    if (clearAll) {
      // Clear all messages for development purposes
      console.log("Clearing all messages for thread:", jobId);

      const result = await collection.updateOne(
        { jobId },
        {
          $set: {
            messages: [],
            artifacts: [],
            sourceDocuments: [],
          },
        },
      );

      if (result.matchedCount === 0) {
        console.log("No training plan found for jobId:", jobId);
        return new NextResponse(
          JSON.stringify({ error: "No training plan found" }),
          { status: 404 },
        );
      }

      console.log("All messages cleared successfully");
      return new NextResponse(
        JSON.stringify({
          success: true,
          message: "All messages cleared successfully",
        }),
        { status: 200 },
      );
    } else {
      // Original functionality: Remove only plan messages
      const result = await collection.updateOne(
        { jobId },
        { $pull: { messages: { type: "plan" } } },
      );

      if (result.matchedCount === 0) {
        console.log("No training plan found for jobId:", jobId);
        return new NextResponse(
          JSON.stringify({ error: "No training plan found" }),
          { status: 404 },
        );
      }

      if (result.modifiedCount === 0) {
        console.log("No plan messages found to remove");
        return new NextResponse(
          JSON.stringify({ message: "No plan messages found to remove" }),
          { status: 200 },
        );
      }

      console.log("Plan messages removed successfully");
      return new NextResponse(
        JSON.stringify({
          success: true,
          message: "Plan messages removed successfully",
        }),
        { status: 200 },
      );
    }
  } catch (error) {
    console.error("Error in DELETE operation:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to complete DELETE operation" }),
      { status: 500 },
    );
  }
}
