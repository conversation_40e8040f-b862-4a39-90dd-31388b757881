import { MongoClient, ServerApiVersion } from "mongodb";
import { NextRequest, NextResponse } from "next/server";

async function getMongoClient() {
  const uri = process.env.MONGODB_URI;
  if (!uri) {
    throw new Error("MONGODB_URI environment variable is not set.");
  }
  const client = new MongoClient(uri, {
    serverApi: {
      version: ServerApiVersion.v1,
      strict: true,
      deprecationErrors: true,
    },
  });
  await client.connect();
  return client;
}

export async function POST(request: NextRequest) {
  // 1. Get user authentication
  // 2. Retrieve Cronofy access token
  // 3. Create calendar event via Cronofy API
  // 4. Return event details

 const clientId = process.env.CRONOFY_CLIENT_ID;
  const redirectUri = process.env.CRONOFY_REDIRECT_URI;
  const sdkIdentifier = process.env.CRONOFY_SDK_IDENTIFIER;

  const searchParams = request.nextUrl.searchParams;
  const provider = searchParams.get("provider"); // Get the provider from query params

  if (!clientId || !redirectUri) {
    console.error(
      "Cronofy Client ID or Redirect URI is not configured in environment variables.",
    );
    return NextResponse.json(
      { error: "Server configuration error." },
      { status: 500 },
    );
  }

  const scopes = ["read_write"];
  const scopeString = scopes.join(" ");

  let cronofySiteHost;

  switch (sdkIdentifier) {
    case "us":
      cronofySiteHost = "https://app.cronofy.com";
      break;
    case "au":
      cronofySiteHost = "https://app-au.cronofy.com";
      break;
    case "ca":
      cronofySiteHost = "https://app-ca.cronofy.com";
      break;
    case "de":
      cronofySiteHost = "https://app-de.cronofy.com";
      break;
    case "sg":
      cronofySiteHost = "https://app-sg.cronofy.com";
      break;
    case "uk":
      cronofySiteHost = "https://app-uk.cronofy.com";
      break;
    default:
      console.warn(
        `Unknown or undefined CRONOFY_SDK_IDENTIFIER: '${sdkIdentifier}'. Defaulting to https://app.cronofy.com. Ensure this is correct for your app region.`,
      );
      cronofySiteHost = "https://app.cronofy.com";
  }


  const authUrl = new URL(`${cronofySiteHost}/oauth/authorize`);
  authUrl.searchParams.append("response_type", "code");
  authUrl.searchParams.append("client_id", clientId);
  authUrl.searchParams.append("redirect_uri", redirectUri);
  authUrl.searchParams.append("scope", scopeString);

  

  if (provider) {
    authUrl.searchParams.append("provider_name", provider);
  }

  return NextResponse.redirect(authUrl.toString());
}
