/**
 * Python LangGraph Coaching API Proxy Route
 *
 * This route proxies requests to the Python FastAPI backend that runs
 * the LangGraph coaching system. It maintains the same interface as
 * the TypeScript coaching route but forwards to the Python implementation.
 */

import { NextRequest } from "next/server";

// Python backend configuration
const PYTHON_BACKEND_URL =
  process.env.PYTHON_LANGGRAPH_URL || "http://localhost:8000";

export async function GET(req: NextRequest) {
  const startTime = Date.now();
  console.log("🚀 PROXY: [GET] Received request to Python coaching proxy");

  // Get parameters from URL
  const searchParams = req.nextUrl.searchParams;
  const messageContent = searchParams.get("message");
  const threadId = searchParams.get("threadId");
  const userId = searchParams.get("userId");
  const singleCoach = searchParams.get("singleCoach");

  console.log("📊 PROXY: [GET] Request parameters:", {
    messageContent:
      messageContent?.substring(0, 100) +
      (messageContent && messageContent.length > 100 ? "..." : ""),
    threadId,
    userId,
    singleCoach: singleCoach || "None",
    timestamp: new Date().toISOString(),
  });

  // Validate required parameters
  if (!threadId || !userId || !messageContent) {
    const errorMsg = `Missing required parameters: ${!threadId ? "threadId " : ""}${!userId ? "userId " : ""}${!messageContent ? "message" : ""}`;
    console.error("❌ PROXY: [GET] Validation failed:", errorMsg);
    return new Response(JSON.stringify({ error: errorMsg }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  console.log(
    `SERVER API: Proxying to Python backend - Thread=${threadId}, UserId=${userId}, Message=${messageContent}, SingleCoach=${singleCoach || "None"}`,
  );

  try {
    // Construct Python backend URL
    const pythonParams = new URLSearchParams({
      message: messageContent,
      threadId: threadId,
      userId: userId,
    });

    if (singleCoach) {
      pythonParams.set("singleCoach", singleCoach);
      console.log("🎯 PROXY: [GET] Single coach specified:", singleCoach);
    }

    const pythonUrl = `${PYTHON_BACKEND_URL}/api/coaching?${pythonParams.toString()}`;

    console.log("🔗 PROXY: [GET] Forwarding to Python backend:", {
      url: pythonUrl,
      backend_host: PYTHON_BACKEND_URL,
      params_count: pythonParams.toString().length,
    });

    // Create SSE stream that proxies the Python backend response
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        let eventCount = 0;
        let bytesStreamed = 0;

        console.log("🌊 PROXY: [GET] Starting SSE stream proxy");

        try {
          // Use fetch with streaming for Server-Side EventSource
          const fetchStartTime = Date.now();
          const response = await fetch(pythonUrl, {
            method: "GET",
            headers: {
              Accept: "text/event-stream",
              "Cache-Control": "no-cache",
            },
          });

          const fetchDuration = Date.now() - fetchStartTime;

          console.log("📡 PROXY: [GET] Python backend response:", {
            status: response.status,
            statusText: response.statusText,
            fetch_duration_ms: fetchDuration,
            has_body: !!response.body,
            content_type: response.headers.get("content-type"),
          });

          if (!response.ok) {
            throw new Error(
              `Python backend responded with status: ${response.status}`,
            );
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error("No response body from Python backend");
          }

          // Stream the response with proper SSE handling
          const decoder = new TextDecoder();

          console.log("⚡ PROXY: [GET] Starting stream processing...");

          try {
            let lastHeartbeat = Date.now();
            const heartbeatTimeout = 35000; // 35 seconds (slightly more than backend's 30s stall timeout)

            while (true) {
              const readStartTime = Date.now();
              const { done, value } = await reader.read();
              const readDuration = Date.now() - readStartTime;

              if (done) {
                console.log(
                  "✅ PROXY: [GET] Python backend stream completed:",
                  {
                    total_events: eventCount,
                    total_bytes: bytesStreamed,
                    total_duration_ms: Date.now() - startTime,
                  },
                );
                break;
              }

              eventCount++;
              bytesStreamed += value.length;

              // Immediately forward chunks to avoid buffering issues
              const chunk = decoder.decode(value, { stream: true });

              if (chunk) {
                // Check for heartbeat events
                if (chunk.includes("event: heartbeat")) {
                  lastHeartbeat = Date.now();
                  console.log(
                    "💓 PROXY: [GET] Received heartbeat from backend",
                  );
                }

                // Check for error events that should stop retries
                if (chunk.includes("event: error")) {
                  console.log("❌ PROXY: [GET] Error event detected in stream");
                  // Extract error message if possible
                  const errorMatch = chunk.match(/data: ({.*?})\n/);
                  if (errorMatch) {
                    try {
                      const errorData = JSON.parse(errorMatch[1]);
                      console.error(
                        "❌ PROXY: [GET] Error details:",
                        errorData,
                      );
                    } catch (e) {
                      console.error(
                        "❌ PROXY: [GET] Could not parse error data",
                      );
                    }
                  }
                }

                console.log("📨 PROXY: [GET] Forwarding chunk:", {
                  event_number: eventCount,
                  chunk_size: chunk.length,
                  read_duration_ms: readDuration,
                  chunk_preview:
                    chunk.substring(0, 200) + (chunk.length > 200 ? "..." : ""),
                });

                controller.enqueue(encoder.encode(chunk));
              }

              // Check for heartbeat timeout
              if (Date.now() - lastHeartbeat > heartbeatTimeout) {
                console.error(
                  "❌ PROXY: [GET] Heartbeat timeout - backend may be unresponsive",
                );
                const timeoutEvent = `event: error\ndata: ${JSON.stringify({
                  type: "error",
                  message: "Backend timeout - no heartbeat received",
                })}\n\n`;
                controller.enqueue(encoder.encode(timeoutEvent));
                break;
              }

              // Log every 10th event to avoid spam
              if (eventCount % 10 === 0) {
                console.log("📈 PROXY: [GET] Stream progress:", {
                  events_processed: eventCount,
                  bytes_streamed: bytesStreamed,
                  elapsed_ms: Date.now() - startTime,
                });
              }
            }
          } catch (streamError) {
            console.error(
              "❌ PROXY: [GET] Error reading Python backend stream:",
              {
                error: streamError,
                events_before_error: eventCount,
                bytes_before_error: bytesStreamed,
              },
            );

            // Send error event with retry prevention flag
            const errorEvent = `event: error\ndata: ${JSON.stringify({
              type: "error",
              message: "Stream interrupted",
              preventRetry: true, // Signal to frontend to not retry automatically
            })}\n\n`;
            controller.enqueue(encoder.encode(errorEvent));
          }
        } catch (error) {
          console.error("❌ PROXY: [GET] Error proxying to Python backend:", {
            error: error instanceof Error ? error.message : String(error),
            backend_url: PYTHON_BACKEND_URL,
            elapsed_ms: Date.now() - startTime,
          });

          // Send error event to frontend
          const errorEvent = `event: error\ndata: ${JSON.stringify({
            type: "error",
            message: "Failed to connect to Python coaching backend",
            error: error instanceof Error ? error.message : String(error),
          })}\n\n`;

          controller.enqueue(encoder.encode(errorEvent));
        } finally {
          console.log("🏁 PROXY: [GET] Stream closed, final stats:", {
            total_events: eventCount,
            total_bytes: bytesStreamed,
            total_duration_ms: Date.now() - startTime,
          });
          controller.close();
        }
      },

      cancel() {
        console.log("🚫 PROXY: [GET] Client cancelled Python backend stream");
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache, no-transform",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("SERVER API: Error in Python coaching proxy:", error);

    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  console.log("🚀 PROXY: [POST] Received request to Python coaching proxy");

  try {
    const body = await req.json();
    const { message, threadId, userId, userProfile, singleCoach } = body;

    console.log("📊 PROXY: [POST] Request body parsed:", {
      message:
        message?.substring(0, 100) +
        (message && message.length > 100 ? "..." : ""),
      threadId,
      userId,
      has_userProfile: !!userProfile,
      singleCoach: singleCoach || "None",
      timestamp: new Date().toISOString(),
    });

    // Validate required parameters
    if (!threadId || !userId || !message) {
      console.error(
        "❌ PROXY: [POST] Validation failed: Missing required parameters",
      );
      return new Response(
        JSON.stringify({
          error: "Missing required parameters: message, threadId, userId",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Prepare request body for Python backend
    const pythonRequestBody: Record<string, any> = {
      message,
      threadId,
      userId,
      userProfile,
    };

    // Choose endpoint based on singleCoach parameter
    const endpoint = singleCoach
      ? "/api/coaching/specialized"
      : "/api/coaching";
    if (singleCoach) {
      pythonRequestBody.coachType = singleCoach;
      console.log("🎯 PROXY: [POST] Single coach specified:", singleCoach);
    }

    const pythonUrl = `${PYTHON_BACKEND_URL}${endpoint}`;

    console.log("🔗 PROXY: [POST] Forwarding to Python backend:", {
      url: pythonUrl,
      method: "POST",
      endpoint,
      backend_host: PYTHON_BACKEND_URL,
      body_size: JSON.stringify(pythonRequestBody).length,
    });

    // Create SSE stream that proxies the Python backend response
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        let eventCount = 0;
        let bytesStreamed = 0;

        console.log("🌊 PROXY: [POST] Starting SSE stream proxy");

        try {
          const fetchStartTime = Date.now();
          const response = await fetch(pythonUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "text/event-stream",
            },
            body: JSON.stringify(pythonRequestBody),
          });

          const fetchDuration = Date.now() - fetchStartTime;

          console.log("📡 PROXY: [POST] Python backend response:", {
            status: response.status,
            statusText: response.statusText,
            fetch_duration_ms: fetchDuration,
            has_body: !!response.body,
            content_type: response.headers.get("content-type"),
          });

          if (!response.ok) {
            throw new Error(
              `Python backend responded with status: ${response.status}`,
            );
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error("No response body from Python backend");
          }

          // Stream the response
          const decoder = new TextDecoder();

          console.log("⚡ PROXY: [POST] Starting stream processing...");

          try {
            let lastHeartbeat = Date.now();
            const heartbeatTimeout = 35000; // 35 seconds (slightly more than backend's 30s stall timeout)

            while (true) {
              const readStartTime = Date.now();
              const { done, value } = await reader.read();
              const readDuration = Date.now() - readStartTime;

              if (done) {
                console.log(
                  "✅ PROXY: [POST] Python backend stream completed:",
                  {
                    total_events: eventCount,
                    total_bytes: bytesStreamed,
                    total_duration_ms: Date.now() - startTime,
                  },
                );
                break;
              }

              eventCount++;
              bytesStreamed += value.length;

              // Immediately forward chunks to avoid buffering issues
              const chunk = decoder.decode(value, { stream: true });

              if (chunk) {
                // Check for heartbeat events
                if (chunk.includes("event: heartbeat")) {
                  lastHeartbeat = Date.now();
                  console.log(
                    "💓 PROXY: [POST] Received heartbeat from backend",
                  );
                }

                // Check for error events that should stop retries
                if (chunk.includes("event: error")) {
                  console.log(
                    "❌ PROXY: [POST] Error event detected in stream",
                  );
                  // Extract error message if possible
                  const errorMatch = chunk.match(/data: ({.*?})\n/);
                  if (errorMatch) {
                    try {
                      const errorData = JSON.parse(errorMatch[1]);
                      console.error(
                        "❌ PROXY: [POST] Error details:",
                        errorData,
                      );
                    } catch (e) {
                      console.error(
                        "❌ PROXY: [POST] Could not parse error data",
                      );
                    }
                  }
                }

                console.log("📨 PROXY: [POST] Forwarding chunk:", {
                  event_number: eventCount,
                  chunk_size: chunk.length,
                  read_duration_ms: readDuration,
                  chunk_preview:
                    chunk.substring(0, 200) + (chunk.length > 200 ? "..." : ""),
                });

                controller.enqueue(encoder.encode(chunk));
              }

              // Check for heartbeat timeout
              if (Date.now() - lastHeartbeat > heartbeatTimeout) {
                console.error(
                  "❌ PROXY: [POST] Heartbeat timeout - backend may be unresponsive",
                );
                const timeoutEvent = `event: error\ndata: ${JSON.stringify({
                  type: "error",
                  message: "Backend timeout - no heartbeat received",
                })}\n\n`;
                controller.enqueue(encoder.encode(timeoutEvent));
                break;
              }

              // Log every 10th event to avoid spam
              if (eventCount % 10 === 0) {
                console.log("📈 PROXY: [POST] Stream progress:", {
                  events_processed: eventCount,
                  bytes_streamed: bytesStreamed,
                  elapsed_ms: Date.now() - startTime,
                });
              }
            }
          } catch (streamError) {
            console.error(
              "❌ PROXY: [POST] Error reading Python backend stream:",
              {
                error: streamError,
                events_before_error: eventCount,
                bytes_before_error: bytesStreamed,
              },
            );

            // Send error event with retry prevention flag
            const errorEvent = `event: error\ndata: ${JSON.stringify({
              type: "error",
              message: "Stream interrupted",
              preventRetry: true, // Signal to frontend to not retry automatically
            })}\n\n`;
            controller.enqueue(encoder.encode(errorEvent));
          }
        } catch (error) {
          console.error("❌ PROXY: [POST] Error proxying to Python backend:", {
            error: error instanceof Error ? error.message : String(error),
            backend_url: PYTHON_BACKEND_URL,
            endpoint,
            elapsed_ms: Date.now() - startTime,
          });

          const errorEvent = `event: error\ndata: ${JSON.stringify({
            type: "error",
            message: "Failed to connect to Python coaching backend",
            error: error instanceof Error ? error.message : String(error),
          })}\n\n`;

          controller.enqueue(encoder.encode(errorEvent));
        } finally {
          console.log("🏁 PROXY: [POST] Stream closed, final stats:", {
            total_events: eventCount,
            total_bytes: bytesStreamed,
            total_duration_ms: Date.now() - startTime,
          });
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache, no-transform",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("SERVER API POST: Error in Python coaching proxy:", error);

    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
