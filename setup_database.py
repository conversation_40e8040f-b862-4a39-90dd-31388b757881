#!/usr/bin/env python3
"""
Database Setup Wrapper

This script calls the actual setup script in the scripts directory.
"""

import subprocess
import sys
import os


def main():
    """Run the database setup script."""
    script_path = os.path.join("scripts", "setup_database.py")

    if not os.path.exists(script_path):
        print("❌ Setup script not found at scripts/setup_database.py")
        sys.exit(1)

    # Run the setup script
    try:
        subprocess.run([sys.executable, script_path], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Setup failed with exit code {e.returncode}")
        sys.exit(e.returncode)
    except KeyboardInterrupt:
        print("\n⚠️ Setup cancelled by user")
        sys.exit(1)


if __name__ == "__main__":
    main()
