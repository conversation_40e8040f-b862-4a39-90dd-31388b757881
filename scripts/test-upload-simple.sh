#!/bin/bash

# Simple File Upload Test Script using curl
# This tests the file upload functionality with Azure OpenAI

echo "🧪 Simple File Upload Test"
echo "=========================="

# Configuration
UPLOAD_ENDPOINT="http://localhost:3000/api/upload"
TEST_USER_ID="test-user-$(date +%s)"

echo "📍 Testing endpoint: $UPLOAD_ENDPOINT"
echo "👤 Test user ID: $TEST_USER_ID"

# Create test directory
mkdir -p test-files

# Create test workout file
cat > test-files/workout.txt << 'EOF'
Training Log - Week 1

Monday: 5km run in 25 minutes
- Heart rate: 150-165 bpm
- Felt good, steady pace

Tuesday: Strength training
- Squats: 3x10 @ 80kg
- Bench press: 3x8 @ 70kg
- Duration: 45 minutes

Thursday: Cycling
- Distance: 20km
- Time: 45 minutes
- Average speed: 26.7 km/h
EOF

# Create test CSV file
cat > test-files/fitness.csv << 'EOF'
Date,Exercise,Duration,Distance,Calories
2024-01-15,Running,25,5,350
2024-01-16,Strength Training,45,0,280
2024-01-18,Cycling,45,20,420
EOF

echo "📁 Created test files"

# Test 1: Upload text file
echo ""
echo "🚀 Testing upload: workout.txt"
response=$(curl -s -w "\nHTTP_STATUS_CODE:%{http_code}" \
  -X POST "$UPLOAD_ENDPOINT" \
  -F "file=@test-files/workout.txt" \
  -F "user_id=$TEST_USER_ID")

http_status=$(echo "$response" | grep "HTTP_STATUS_CODE" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_STATUS_CODE/d')

echo "📊 Response Status: $http_status"
echo "📋 Response Body: $response_body"

if [ "$http_status" -eq 200 ]; then
    echo "✅ workout.txt uploaded successfully!"
else
    echo "❌ workout.txt upload failed"
fi

# Test 2: Upload CSV file
echo ""
echo "🚀 Testing upload: fitness.csv"
response=$(curl -s -w "\nHTTP_STATUS_CODE:%{http_code}" \
  -X POST "$UPLOAD_ENDPOINT" \
  -F "file=@test-files/fitness.csv" \
  -F "user_id=$TEST_USER_ID")

http_status=$(echo "$response" | grep "HTTP_STATUS_CODE" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_STATUS_CODE/d')

echo "📊 Response Status: $http_status"
echo "📋 Response Body: $response_body"

if [ "$http_status" -eq 200 ]; then
    echo "✅ fitness.csv uploaded successfully!"
else
    echo "❌ fitness.csv upload failed"
fi

# Cleanup
echo ""
echo "🧹 Cleaning up test files..."
rm -rf test-files
echo "🗑️ Test files deleted"

echo ""
echo "📊 Test completed!"
echo "If uploads failed, check your .env.local file and ensure:"
echo "- AZURE_OPENAI_API_KEY is set"
echo "- AZURE_OPENAI_ENDPOINT is set"
echo "- AZURE_DEPLOYMENT_NAME is set"
echo "- MONGODB_URI is set"
echo "- Your Next.js dev server is running (npm run dev)" 