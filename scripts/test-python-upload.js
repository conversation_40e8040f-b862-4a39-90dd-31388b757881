#!/usr/bin/env node
/**
 * Python Backend Direct Upload Test Script
 *
 * This script tests direct file upload to the Python backend
 * for onboarding file integration.
 */

// Load environment variables from .env.local
require("dotenv").config({
  path: require("path").join(__dirname, "../.env.local"),
});

const fs = require("fs");
const FormData = require("form-data");

// Configuration
const PYTHON_BACKEND_URL =
  process.env.NEXT_PUBLIC_PYTHON_LANGGRAPH_URL || "http://localhost:8000";
const UPLOAD_ENDPOINT = `${PYTHON_BACKEND_URL}/api/onboarding/upload`;
const TEST_USER_ID = "test-user-" + Date.now();
const TEST_THREAD_ID = "test-thread-" + Date.now();

// Check environment variables
function checkEnvironmentVariables() {
  console.log("\n🔧 Checking environment variables...");

  const requiredEnvVars = ["NEXT_PUBLIC_PYTHON_LANGGRAPH_URL"];

  const missingVars = [];

  requiredEnvVars.forEach((varName) => {
    if (process.env[varName]) {
      console.log(`✅ ${varName}: ${process.env[varName]}`);
    } else {
      console.log(`❌ ${varName}: Missing`);
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    console.log(
      `\n❌ Missing required environment variables: ${missingVars.join(", ")}`,
    );
    console.log("Please add them to your .env.local file");
    process.exit(1);
  }

  console.log("✅ All required environment variables are set");
}

// Test health check
async function testHealthCheck() {
  console.log("\n🏥 Testing Python backend health...");

  try {
    const response = await fetch(`${PYTHON_BACKEND_URL}/api/health`);
    if (response.ok) {
      const data = await response.json();
      console.log("✅ Python backend is healthy:", data);
      return true;
    } else {
      console.log("❌ Python backend health check failed:", response.status);
      return false;
    }
  } catch (error) {
    console.log("❌ Python backend is not responding:", error.message);
    console.log(
      "💡 Make sure your Python backend is running on:",
      PYTHON_BACKEND_URL,
    );
    return false;
  }
}

// Create test files
function createTestFiles() {
  console.log("\n📁 Creating test files...");

  if (!fs.existsSync("test-files")) {
    fs.mkdirSync("test-files");
  }

  // Create a test workout file
  const workoutContent = `Training Log - Week 1

Monday: 5km run in 25 minutes
- Heart rate: 150-165 bpm
- Felt good, steady pace
- Weather: Sunny, 18°C

Tuesday: Strength training
- Squats: 3x10 @ 80kg
- Bench press: 3x8 @ 70kg
- Deadlifts: 1x5 @ 100kg
- Duration: 45 minutes

Thursday: Cycling
- Distance: 20km
- Time: 45 minutes
- Average speed: 26.7 km/h
- Route: City loop

Saturday: Long run
- Distance: 10km
- Time: 52 minutes
- Pace: 5:12 min/km
- Feeling: Tired but satisfied`;

  fs.writeFileSync("test-files/workout-log.txt", workoutContent);
  console.log("✅ Created test workout log: test-files/workout-log.txt");

  // Create a test CSV file
  const csvContent = `Date,Activity,Duration,Distance,Notes
2024-01-15,Running,25,5km,Good pace
2024-01-16,Strength,45,,Heavy session
2024-01-18,Cycling,45,20km,City loop
2024-01-20,Running,52,10km,Long run`;

  fs.writeFileSync("test-files/training-data.csv", csvContent);
  console.log("✅ Created test CSV: test-files/training-data.csv");
}

// Test file upload to Python backend
async function testPythonUpload(filename) {
  console.log(`\n🚀 Testing Python backend upload: ${filename}`);

  const filePath = `test-files/${filename}`;

  if (!fs.existsSync(filePath)) {
    console.log(`❌ Test file not found: ${filePath}`);
    return false;
  }

  try {
    const formData = new FormData();
    formData.append("file", fs.createReadStream(filePath));
    formData.append("user_id", TEST_USER_ID);
    formData.append("thread_id", TEST_THREAD_ID);

    console.log(`📤 Uploading to: ${UPLOAD_ENDPOINT}`);
    console.log(`👤 User ID: ${TEST_USER_ID}`);
    console.log(`🧵 Thread ID: ${TEST_THREAD_ID}`);

    // Use form-data with fetch
    const response = await fetch(UPLOAD_ENDPOINT, {
      method: "POST",
      body: formData,
      headers: formData.getHeaders(),
    });

    const responseText = await response.text();
    console.log(`📥 Response status: ${response.status}`);
    console.log(
      `📥 Response headers:`,
      Object.fromEntries(response.headers.entries()),
    );

    if (response.ok) {
      try {
        const result = JSON.parse(responseText);
        console.log("✅ Upload successful!");
        console.log("📊 Response data:", JSON.stringify(result, null, 2));
        return true;
      } catch (parseError) {
        console.log("✅ Upload successful (non-JSON response)");
        console.log("📄 Response:", responseText.substring(0, 500));
        return true;
      }
    } else {
      console.log("❌ Upload failed");
      console.log("📄 Error response:", responseText);
      return false;
    }
  } catch (error) {
    console.log("❌ Upload error:", error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log("🧪 Python Backend Direct Upload Test");
  console.log("=====================================");

  // Check environment
  checkEnvironmentVariables();

  // Test backend health
  const isHealthy = await testHealthCheck();
  if (!isHealthy) {
    console.log("\n❌ Cannot continue tests - Python backend is not healthy");
    process.exit(1);
  }

  // Create test files
  createTestFiles();

  // Test uploads
  const testFiles = ["workout-log.txt", "training-data.csv"];
  let successCount = 0;

  for (const filename of testFiles) {
    const success = await testPythonUpload(filename);
    if (success) successCount++;
  }

  // Summary
  console.log("\n📊 Test Summary");
  console.log("================");
  console.log(`✅ Successful uploads: ${successCount}/${testFiles.length}`);
  console.log(
    `❌ Failed uploads: ${testFiles.length - successCount}/${testFiles.length}`,
  );

  if (successCount === testFiles.length) {
    console.log(
      "\n🎉 All tests passed! Python backend direct upload is working.",
    );
  } else {
    console.log(
      "\n⚠️  Some tests failed. Check the Python backend logs for details.",
    );
  }

  // Cleanup
  console.log("\n🧹 Cleaning up test files...");
  if (fs.existsSync("test-files")) {
    fs.rmSync("test-files", { recursive: true });
    console.log("✅ Test files cleaned up");
  }
}

// Run tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error("\n💥 Test script error:", error);
    process.exit(1);
  });
}

module.exports = { runTests };
