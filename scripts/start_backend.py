#!/usr/bin/env python3
"""
Athlea Backend Startup Script

Automatically starts the FastAPI backend server with advanced Redis caching
and comprehensive system checks.
"""

import asyncio
import os
import sys
import time
import warnings
import logging
from pathlib import Path
from dotenv import load_dotenv
import subprocess
import atexit

# Load environment variables
load_dotenv()

# The MCP servers are now launched by the MCPClientManager, so this is no longer needed.
# mcp_processes = []


async def test_cache_system():
    """Test the advanced caching system before starting the server."""
    print("🔧 Testing Advanced Cache System...")

    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("   ⚠️  MONGODB_URI not set - cache will be disabled")
        return False

    try:
        from athlea_langgraph.utils.advanced_user_data_cache import (
            get_advanced_cache,
            fetch_user_data_ultra_fast,
        )

        # Initialize cache
        cache = get_advanced_cache(mongodb_uri)
        await cache.initialize()

        # Test health
        health = await cache.health_check()

        print(f"   Redis Status: {health['redis']['status']}")
        if "latency_ms" in health["redis"]:
            print(f"   Redis Latency: {health['redis']['latency_ms']:.2f}ms")

        print(f"   MongoDB Status: {health['mongodb']['status']}")
        if "latency_ms" in health["mongodb"]:
            print(f"   MongoDB Latency: {health['mongodb']['latency_ms']:.2f}ms")

        print(f"   Overall Status: {health['overall']}")

        # Test data fetch with test user
        test_user_id = os.getenv("TEST_USER_ID", "test_user_cache")
        start_time = time.time()
        user_data = await fetch_user_data_ultra_fast(test_user_id, mongodb_uri)
        elapsed = (time.time() - start_time) * 1000

        cache_source = user_data.get("cache_source", "unknown")
        print(f"   Test Fetch: {elapsed:.2f}ms from {cache_source}")

        await cache.close()

        if health["overall"] in ["healthy", "degraded"]:
            print("✅ Advanced cache system ready!")
            return True
        else:
            print("⚠️  Cache system has issues but proceeding...")
            return True

    except Exception as e:
        print(f"   ❌ Cache system test failed: {e}")
        print("   Proceeding without cache optimization...")
        return False


async def warm_cache_for_common_users():
    """Warm cache for commonly accessed users."""
    print("🔥 Warming cache for common users...")

    mongodb_uri = os.getenv("MONGODB_URI")
    common_users = os.getenv("CACHE_WARM_USERS", "").split(",")

    if not mongodb_uri or not common_users or common_users == [""]:
        print("   No users specified for cache warming (set CACHE_WARM_USERS)")
        return

    try:
        from athlea_langgraph.utils.advanced_user_data_cache import get_advanced_cache

        cache = get_advanced_cache(mongodb_uri)
        await cache.initialize()

        results = await cache.warm_cache_for_users(common_users)
        successful = sum(results.values())
        total = len(common_users)

        print(f"   Warmed cache for {successful}/{total} users")

        await cache.close()

    except Exception as e:
        print(f"   Cache warming failed: {e}")


def check_environment():
    """Check if required environment variables are set."""
    print("🔍 Checking Environment Configuration...")

    # Azure OpenAI requirements
    required_vars = [
        "AZURE_OPENAI_API_KEY",
        "AZURE_OPENAI_ENDPOINT",
        "AZURE_DEPLOYMENT_NAME",
    ]

    # Optional but recommended vars
    optional_vars = {
        "MONGODB_URI": "Advanced user data caching",
        "TEST_USER_ID": "Testing with specific user data",
        "CACHE_WARM_USERS": "Pre-warming cache on startup",
        "PORT": "Custom server port",
        "HOST": "Custom server host",
    }

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file or environment configuration.")
        return False

    print("✅ Required environment variables configured")

    # Check optional vars
    configured_optional = []
    missing_optional = []
    for var, description in optional_vars.items():
        if os.getenv(var):
            configured_optional.append(f"{var} ({description})")
        else:
            missing_optional.append(f"{var} ({description})")

    if configured_optional:
        print("✅ Optional features configured:")
        for feature in configured_optional:
            print(f"   • {feature}")

    if missing_optional:
        print("⚠️  Optional features not configured:")
        for feature in missing_optional:
            print(f"   • {feature}")

    return True


def print_startup_info():
    """Print helpful startup information."""
    print("🚀 Athlea Python LangGraph Backend with Advanced Caching")
    print("=" * 70)

    # Environment check
    mongodb_uri = os.getenv("MONGODB_URI")
    test_user_id = os.getenv("TEST_USER_ID")
    port = os.getenv("PORT", "8000")
    host = os.getenv("HOST", "0.0.0.0")

    print(f"📡 Server Configuration:")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   MongoDB: {'✅ Configured' if mongodb_uri else '❌ Missing'}")
    print(f"   Test User ID: {test_user_id or 'Not set'}")

    print(f"\n🔗 API Endpoints:")
    print(f"   Health Check: http://{host}:{port}/api/health")
    print(f"   Coaching API: http://{host}:{port}/api/coaching")
    print(f"   Onboarding API: http://{host}:{port}/api/onboarding")

    print(f"\n💡 MCP Servers are now launched on-demand by the MCPClientManager.")

    if mongodb_uri:
        print(f"   Cache Stats: http://{host}:{port}/api/cache/stats")
        print(f"   Cache Health: http://{host}:{port}/api/cache/health")

    if mongodb_uri:
        print(f"\n⚡ Cache Management:")
        print(f"   Cache Statistics: GET /api/cache/stats")
        print(f"   Warm Cache: POST /api/cache/warm")
        print(f"   Invalidate User: DELETE /api/cache/user/{{user_id}}")
        print(f"   Performance Test: POST /api/cache/test")

    redis_config = {
        "host": "athlea-redis-standard.redis.cache.windows.net",
        "port": 6380,
        "ssl": True,
    }
    print(f"\n🔴 Redis Configuration:")
    print(f"   Host: {redis_config['host']}")
    print(f"   Port: {redis_config['port']}")
    print(f"   SSL: {redis_config['ssl']}")

    print()


async def startup_sequence():
    """Run the complete startup sequence."""
    print_startup_info()

    # Check environment
    if not check_environment():
        sys.exit(1)

    # Test cache system if MongoDB is configured
    cache_ok = False
    if os.getenv("MONGODB_URI"):
        cache_ok = await test_cache_system()

        # Warm cache if configured
        if cache_ok:
            await warm_cache_for_common_users()

    print("\n" + "=" * 70)

    if cache_ok:
        print("✅ Backend ready with advanced caching!")
        print("   Expected performance: 1-5ms for cached data")
        print("   Cache hit rate target: >90%")
    else:
        print(
            "✅ Backend ready"
            + (" (limited caching)" if os.getenv("MONGODB_URI") else " (no caching)")
        )
        if not os.getenv("MONGODB_URI"):
            print("   Configure MONGODB_URI for advanced caching features")

    print(f"\n🎯 Quick Test Commands:")
    port = os.getenv("PORT", "8000")
    print(f"   curl http://localhost:{port}/api/health")

    if os.getenv("MONGODB_URI"):
        print(f"   curl http://localhost:{port}/api/cache/stats")
        print(f"   curl http://localhost:{port}/api/cache/health")

    test_user_id = os.getenv("TEST_USER_ID", "test_user_123")
    print(f"\n🧪 Test with user data:")
    print(
        f"   curl 'http://localhost:{port}/api/coaching?message=test&threadId=test&userId={test_user_id}'"
    )

    print("\n🚀 Starting FastAPI server...")
    print("   Press Ctrl+C to stop")


def start_backend():
    """Start the backend server with full initialization."""
    try:
        # Run startup sequence
        asyncio.run(startup_sequence())

        # Check if we're in the right directory
        main_file = Path("athlea_langgraph/api/main.py")
        if not main_file.exists():
            print(
                "❌ Backend main.py not found. Make sure you're in the project root directory."
            )
            sys.exit(1)

        # Set default environment variables if not provided
        os.environ.setdefault("PORT", "8000")
        os.environ.setdefault("HOST", "0.0.0.0")

        # Start the FastAPI server
        import uvicorn

        # Suppress warnings
        warnings.filterwarnings("ignore", category=DeprecationWarning)
        warnings.filterwarnings("ignore", message=".*websockets.legacy.*")
        warnings.filterwarnings("ignore", message=".*WebSocketServerProtocol.*")

        # Suppress uvicorn's websockets warnings
        websockets_logger = logging.getLogger("websockets.legacy")
        websockets_logger.setLevel(logging.ERROR)
        uvicorn_logger = logging.getLogger("uvicorn.protocols.websockets")
        uvicorn_logger.setLevel(logging.ERROR)

        # Server configuration
        port = int(os.getenv("PORT", 8000))
        host = os.getenv("HOST", "0.0.0.0")

        # Start server
        uvicorn.run(
            "athlea_langgraph.api.main:app",
            host=host,
            port=port,
            reload=True,
            log_level="info",
            ws="auto",
        )

    except KeyboardInterrupt:
        print("\n\n👋 Shutting down backend server...")
        print("   Cache connections will be closed automatically")
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        # The cleanup function will be called automatically by atexit
        sys.exit(1)


if __name__ == "__main__":
    start_backend()
