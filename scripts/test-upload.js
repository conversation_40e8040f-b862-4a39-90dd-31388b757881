#!/usr/bin/env node
/**
 * File Upload Test Script
 *
 * This script tests the file upload functionality to ensure it's working
 * properly with Azure OpenAI API keys. It uploads various file types and
 * checks the responses.
 */

// Load environment variables from .env.local
require("dotenv").config({
  path: require("path").join(__dirname, "../.env.local"),
});

const fs = require("fs");
const path = require("path");
const FormData = require("form-data");

// Configuration
const UPLOAD_ENDPOINT = process.env.NEXT_PUBLIC_BASE_URL
  ? `${process.env.NEXT_PUBLIC_BASE_URL}/api/upload`
  : "http://localhost:3000/api/upload";

const TEST_USER_ID = "test-user-" + Date.now();

// Test files to create and upload
const testFiles = [
  {
    name: "test-workout.txt",
    content: `Training Log - Week 1

Monday: 5km run in 25 minutes
- Heart rate: 150-165 bpm
- Felt good, steady pace
- Weather: Sunny, 18°C

Tuesday: Strength training
- Squats: 3x10 @ 80kg
- Bench press: 3x8 @ 70kg
- Deadlifts: 3x5 @ 100kg
- Duration: 45 minutes

Wednesday: Rest day

Thursday: Cycling
- Distance: 20km
- Time: 45 minutes
- Average speed: 26.7 km/h
- Route: City loop

Friday: Swimming
- Distance: 1500m
- Time: 35 minutes
- Strokes: Freestyle and backstroke
- Pool: 25m indoor

Saturday: Long run
- Distance: 10km
- Time: 52 minutes
- Average pace: 5:12/km
- Felt challenging but completed

Sunday: Yoga and stretching
- Duration: 30 minutes
- Focus: Hip flexibility and core
`,
    type: "text/plain",
  },
  {
    name: "fitness-data.csv",
    content: `Date,Exercise,Duration,Distance,Calories,Heart Rate
2024-01-15,Running,25,5,350,155
2024-01-16,Strength Training,45,0,280,140
2024-01-18,Cycling,45,20,420,150
2024-01-19,Swimming,35,1.5,320,145
2024-01-20,Running,52,10,550,160
2024-01-21,Yoga,30,0,120,105
`,
    type: "text/csv",
  },
];

// Create test files
function createTestFiles() {
  console.log("📁 Creating test files...");

  // Create a test directory
  const testDir = path.join(__dirname, "../test-files");
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  const createdFiles = [];

  testFiles.forEach((file) => {
    const filePath = path.join(testDir, file.name);
    fs.writeFileSync(filePath, file.content);
    createdFiles.push({ ...file, path: filePath });
    console.log(`✅ Created: ${file.name}`);
  });

  return createdFiles;
}

// Test file upload
async function testFileUpload(file) {
  console.log(`\n🚀 Testing upload: ${file.name}`);

  try {
    const formData = new FormData();
    formData.append("file", fs.createReadStream(file.path));
    formData.append("user_id", TEST_USER_ID);

    // Use the form-data package's built-in fetch method or axios-like approach
    const response = await new Promise((resolve, reject) => {
      const options = {
        method: "POST",
        ...formData.getHeaders(), // This sets the correct multipart/form-data headers
      };

      const req = require("http").request(UPLOAD_ENDPOINT, options, resolve);
      req.on("error", reject);
      formData.pipe(req);
    });

    // Read the response body
    let responseBody = "";
    response.on("data", (chunk) => {
      responseBody += chunk;
    });

    await new Promise((resolve) => {
      response.on("end", resolve);
    });

    const result = JSON.parse(responseBody);

    console.log(`📊 Response Status: ${response.statusCode}`);
    console.log(`📋 Response Body:`, JSON.stringify(result, null, 2));

    if (response.statusCode >= 200 && response.statusCode < 300) {
      console.log(`✅ ${file.name} uploaded successfully!`);
      return { success: true, result };
    } else {
      console.log(`❌ ${file.name} upload failed:`, result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error(`❌ Network error uploading ${file.name}:`, error.message);
    return { success: false, error: error.message };
  }
}

// Test API endpoint availability
async function testEndpointHealth() {
  console.log("\n🏥 Testing endpoint health...");

  try {
    const response = await fetch(
      UPLOAD_ENDPOINT.replace("/upload", "/health"),
      {
        method: "GET",
      },
    );

    if (response.ok) {
      console.log("✅ API server is responding");
      return true;
    } else {
      console.log(`⚠️  API server responded with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Could not reach API server: ${error.message}`);
    console.log(
      `📍 Make sure your Next.js server is running on the correct port`,
    );
    return false;
  }
}

// Check environment variables
function checkEnvironmentVariables() {
  console.log("\n🔧 Checking environment variables...");

  const requiredEnvVars = [
    "AZURE_OPENAI_API_KEY", // The actual variable name in .env.local
    "AZURE_OPENAI_ENDPOINT",
    "AZURE_DEPLOYMENT_NAME",
    "MONGODB_URI",
  ];

  const missingVars = [];

  requiredEnvVars.forEach((varName) => {
    if (process.env[varName]) {
      console.log(`✅ ${varName}: Set`);
    } else {
      console.log(`❌ ${varName}: Missing`);
      missingVars.push(varName);
    }
  });

  // Also check for embedding deployment (optional)
  const embeddingDeployment =
    process.env.AZURE_EMBEDDING_DEPLOYMENT ||
    process.env.AZURE_EMBEDDING_DEPLOYMENT_ID;
  if (embeddingDeployment) {
    console.log(`✅ AZURE_EMBEDDING_DEPLOYMENT: Set (${embeddingDeployment})`);
  } else {
    console.log(`⚠️  AZURE_EMBEDDING_DEPLOYMENT: Not set, will use default`);
  }

  if (missingVars.length > 0) {
    console.log(
      `\n⚠️  Missing environment variables: ${missingVars.join(", ")}`,
    );
    console.log("Please set these in your .env.local file");
    return false;
  }

  console.log("\n✅ All required environment variables are set");
  return true;
}

// Cleanup test files
function cleanup(createdFiles) {
  console.log("\n🧹 Cleaning up test files...");

  createdFiles.forEach((file) => {
    if (fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
      console.log(`🗑️  Deleted: ${file.name}`);
    }
  });

  // Remove test directory if empty
  const testDir = path.dirname(createdFiles[0].path);
  try {
    fs.rmdirSync(testDir);
    console.log(`🗑️  Removed test directory`);
  } catch (error) {
    // Directory not empty or doesn't exist, that's fine
  }
}

// Main test function
async function runTests() {
  console.log("🧪 File Upload Test Suite");
  console.log("========================\n");
  console.log(`📍 Testing endpoint: ${UPLOAD_ENDPOINT}`);
  console.log(`👤 Test user ID: ${TEST_USER_ID}`);

  // Check environment variables
  if (!checkEnvironmentVariables()) {
    console.log("\n❌ Test failed: Missing environment variables");
    process.exit(1);
  }

  // Test endpoint health
  const endpointHealthy = await testEndpointHealth();
  if (!endpointHealthy) {
    console.log("\n❌ Test failed: API endpoint not accessible");
    process.exit(1);
  }

  // Create test files
  const createdFiles = createTestFiles();

  let successCount = 0;
  let failureCount = 0;
  const results = [];

  // Test each file upload
  for (const file of createdFiles) {
    const result = await testFileUpload(file);
    results.push({ file: file.name, ...result });

    if (result.success) {
      successCount++;
    } else {
      failureCount++;
    }

    // Add delay between uploads to avoid rate limiting
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  // Cleanup
  cleanup(createdFiles);

  // Summary
  console.log("\n📊 Test Summary");
  console.log("===============");
  console.log(`✅ Successful uploads: ${successCount}`);
  console.log(`❌ Failed uploads: ${failureCount}`);
  console.log(
    `📈 Success rate: ${((successCount / (successCount + failureCount)) * 100).toFixed(1)}%`,
  );

  if (failureCount > 0) {
    console.log(
      "\n❌ Some uploads failed. Check the errors above for details.",
    );
    console.log("Common issues:");
    console.log("- Azure OpenAI API key not set or incorrect");
    console.log("- MongoDB connection string not configured");
    console.log("- Azure OpenAI endpoint or deployment name incorrect");
    process.exit(1);
  } else {
    console.log("\n🎉 All tests passed! File upload is working correctly.");
    process.exit(0);
  }
}

// Handle errors gracefully
process.on("unhandledRejection", (reason, promise) => {
  console.error("\n❌ Unhandled Promise Rejection:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  console.error("\n❌ Uncaught Exception:", error.message);
  process.exit(1);
});

// Add help text
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log(`
🧪 File Upload Test Script

Usage: npm run test:upload

This script tests the file upload functionality by:
1. Checking required environment variables
2. Testing API endpoint health  
3. Creating test fitness files (text and CSV)
4. Uploading files and checking responses
5. Cleaning up test files

Environment variables required:
- AZURE_OPENAI_KEY
- AZURE_OPENAI_ENDPOINT  
- AZURE_DEPLOYMENT_NAME
- MONGODB_URI

Make sure your Next.js development server is running before running this test.
`);
  process.exit(0);
}

// Run the tests
runTests().catch((error) => {
  console.error("\n❌ Test suite failed:", error.message);
  process.exit(1);
});
