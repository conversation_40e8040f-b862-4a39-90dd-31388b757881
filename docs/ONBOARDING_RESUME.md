# Onboarding Resume Functionality

This document describes the onboarding state persistence and resume functionality that allows users to continue their onboarding session where they left off instead of starting from scratch each time.

## 🎯 **Features Implemented**

### **1. Automatic State Persistence**
- **Backend State Management**: The Python LangGraph backend automatically saves onboarding state using MongoDB checkpointer
- **Thread-based Persistence**: Each user has a unique thread ID that persists their conversation state
- **State Recovery**: The system can resume from any point in the onboarding flow

### **2. Frontend Resume Detection**
- **Status Checking**: Frontend automatically checks if a user has an existing onboarding session
- **Resume Dialog**: Users are presented with options to continue or start over
- **Seamless Continuation**: Resuming preserves sidebar data, conversation history, and current stage

### **3. Development Controls**
- **Force Reset**: `?reset=true` parameter to force start a new onboarding session
- **Skip Resume**: `?skip_resume=true` parameter to bypass resume checks during development
- **Development Panel**: Visual status information in development mode

## 🛠️ **Technical Implementation**

### **API Endpoints**

#### **Status Check**
```
GET /api/onboarding/status/{user_id}?thread_id={thread_id}
```
Returns current onboarding status:
```json
{
  "status": "in_progress|completed|not_started",
  "stage": "initial|gathering|reviewing|complete",
  "has_enough_info": boolean,
  "needs_input": boolean,
  "sidebar_data": {...},
  "generated_plan": {...}
}
```

#### **Reset Session**
```
POST /api/onboarding/reset/{user_id}?thread_id={thread_id}
```
Clears all onboarding state and starts fresh:
```json
{
  "status": "reset",
  "message": "Onboarding has been reset",
  "user_id": "user123",
  "thread_id": "thread456"
}
```

### **Frontend Implementation**

#### **State Management**
```typescript
const [onboardingStatus, setOnboardingStatus] = useState<{
  status: 'not_started' | 'in_progress' | 'completed' | 'loading';
  stage?: string;
  message?: string;
  canResume?: boolean;
}>({ status: 'loading' });
```

#### **Development Controls**
```typescript
// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development';

// URL parameter handling
const forceReset = searchParams?.get('reset') === 'true';
const skipResume = searchParams?.get('skip_resume') === 'true';
```

#### **Resume Logic Flow**
1. **Page Load**: Check if user/thread IDs exist
2. **Status Check**: Call status API to get current state
3. **Resume Decision**: Show dialog if resumable state exists
4. **Action Handling**: Execute user choice (resume or reset)
5. **State Sync**: Update frontend state accordingly

### **Proxy Route Updates**

The TypeScript proxy route (`/api/onboarding-python`) now handles resume flags:

```typescript
// Extract resume parameter
const resumeFromInterrupt = searchParams.get("resume") === "true";

// Pass to Python backend
const pythonRequestBody = {
  message: message,
  user_id: userId,
  thread_id: threadId,
  user_profile: null,
  conversation_history: [],
  resume_from_interrupt: resumeFromInterrupt, // 🔄 Resume flag
};
```

## 🎮 **Usage Examples**

### **Normal User Flow**
1. User visits `/onboarding/thread123`
2. System checks for existing state
3. If found, shows resume dialog
4. User chooses to continue or start over
5. Onboarding proceeds accordingly

### **Development Testing**

#### **Force Reset (Start Fresh)**
```
http://localhost:3000/onboarding/thread123?reset=true
```

#### **Skip Resume Check**
```
http://localhost:3000/onboarding/thread123?skip_resume=true
```

#### **Combined Parameters**
```
http://localhost:3000/onboarding/thread123?reset=true&skip_resume=true
```

### **API Testing**

#### **Check Status**
```bash
curl "http://localhost:8000/api/onboarding/status/user123?thread_id=thread456"
```

#### **Reset Session**
```bash
curl -X POST "http://localhost:8000/api/onboarding/reset/user123?thread_id=thread456"
```

## 🧪 **Testing**

### **Automated Tests**
Run the comprehensive test suite:
```bash
npm run test:onboarding-resume
```

This tests:
- ✅ Status check API functionality
- ✅ Reset functionality
- ✅ Resume flag handling in proxy
- ✅ Development control URLs

### **Manual Testing Scenarios**

#### **Scenario 1: First Time User**
1. Visit onboarding page
2. Should see no resume dialog
3. Onboarding starts normally

#### **Scenario 2: Returning User with Progress**
1. Start onboarding, complete some steps
2. Leave page and return
3. Should see resume dialog
4. Choose "Continue" → resumes where left off
5. Choose "Start Over" → resets and starts fresh

#### **Scenario 3: Development Testing**
1. Use `?reset=true` to force reset
2. Use `?skip_resume=true` to bypass checks
3. Development panel should show current state

## 🎨 **User Interface**

### **Resume Dialog**
- **Modern Design**: Clean modal with clear options
- **User-Friendly**: Simple "Continue" vs "Start Over" choice
- **Development Info**: Additional controls and status in dev mode

### **Development Panel**
Visible only in development mode:
- Current onboarding status
- Current stage
- Resume capability status
- Quick action links (Reset, Skip Resume)

### **Status Indicator**
In development mode, the sidebar shows:
```
Development Info:
Status: in_progress
Stage: gathering
Can Resume: Yes
[Reset] | [Skip Resume]
```

## ⚙️ **Configuration**

### **Environment Variables**
```bash
# Python LangGraph Backend URL
NEXT_PUBLIC_PYTHON_LANGGRAPH_URL=http://localhost:8000

# Development mode (automatic)
NODE_ENV=development
```

### **Backend Configuration**
The Python backend uses MongoDB checkpointer for persistence:
```python
checkpointer = MongoDbCheckpointer()
config = {"configurable": {"thread_id": thread_id}}
```

## 🚀 **Benefits**

### **User Experience**
- **No Lost Progress**: Users can safely navigate away and return
- **Flexible Options**: Choice to continue or start fresh
- **Reduced Friction**: Faster completion for returning users

### **Development Experience**
- **Easy Testing**: Development controls for different scenarios
- **State Visibility**: Clear view of current onboarding state
- **Quick Reset**: Force restart without backend changes

### **System Reliability**
- **Graceful Degradation**: Falls back to new session if status check fails
- **State Consistency**: Backend and frontend stay synchronized
- **Error Handling**: Robust error handling for all scenarios

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Resume Dialog Not Showing**
- Check if `skip_resume=true` is in URL
- Verify Python backend is running on port 8000
- Check browser console for API errors

#### **Reset Not Working**
- Verify MongoDB connection in Python backend
- Check that reset endpoint returns 200 status
- Ensure frontend state updates after reset

#### **Development Panel Missing**
- Confirm `NODE_ENV=development`
- Check that onboarding status is being passed to sidebar
- Verify the status contains valid data

### **Debug Commands**
```bash
# Test status API directly
curl "http://localhost:8000/api/onboarding/status/testuser?thread_id=testthread"

# Test reset API
curl -X POST "http://localhost:8000/api/onboarding/reset/testuser?thread_id=testthread"

# Run comprehensive tests
npm run test:onboarding-resume
```

## 📋 **Implementation Checklist**

- ✅ **Backend Status API**: Python endpoint to check onboarding state
- ✅ **Backend Reset API**: Python endpoint to clear onboarding state  
- ✅ **Frontend Status Check**: Automatic status checking on page load
- ✅ **Resume Dialog**: User interface for continue/restart choice
- ✅ **Development Controls**: URL parameters and visual indicators
- ✅ **Proxy Updates**: Resume flag handling in TypeScript proxy
- ✅ **State Management**: Frontend state synchronization
- ✅ **Error Handling**: Graceful fallbacks for all error cases
- ✅ **Testing Suite**: Comprehensive automated tests
- ✅ **Documentation**: Complete usage and technical documentation

## 🔮 **Future Enhancements**

### **Potential Improvements**
- **Auto-save Progress**: Save state after each interaction
- **Multiple Resume Points**: Allow resuming from specific stages
- **Progress Visualization**: Show completion percentage
- **Session Timeout**: Auto-expire old sessions
- **User Preferences**: Remember user's resume preferences

### **Advanced Features**
- **Cross-device Resume**: Resume on different devices
- **Backup States**: Multiple save points per session
- **Analytics Integration**: Track resume vs restart rates
- **A/B Testing**: Test different resume dialog designs 