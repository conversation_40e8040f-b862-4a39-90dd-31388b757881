# Developer Testing Guide

This guide explains the testing strategy for the Athlea coaching system, detailing the different types of tests and how to run them.

## Overview

Our testing architecture is organized into several categories, each serving a specific purpose. This structure allows developers to run targeted tests for quick validation or comprehensive suites for release readiness.

The `tests/` directory is structured as follows:

-   **/unit**: Fast, isolated tests for individual functions and classes.
-   **/integration**: Tests that verify the interaction between multiple components (e.g., agents, tools, and memory).
-   **/e2e**: End-to-end tests that validate complete user workflows, from API endpoints to final output.
-   **/synthetic**: Tests using pre-defined, realistic coaching scenarios to ensure agent quality.
-   **/runners**: Convenience scripts for executing specific test suites.

## How to Run Tests

### 1. Running Specific Test Categories

You can run tests for a specific category using `pytest`. All commands should be run from the project root.

**Unit Tests (Fastest)**
For quick validation of a specific component.
```bash
poetry run pytest tests/unit/
```

**Integration Tests**
To ensure different parts of the system work together correctly.
```bash
poetry run pytest tests/integration/
```
Key integration tests include:
-   `test_specialized_coaches.py`: Verifies the entire specialized coach stack, including tool integration, memory, and routing.
-   `test_tool_integration.py`: Checks that agents receive the correct tools from the `SpecializedCoachToolsManager`.
-   `test_memory_integration.py`: Validates that conversation state is persisted and retrieved correctly.
-   `test_multi_coach_aggregation.py`: Ensures the system can route to and aggregate responses from multiple coaches.

**End-to-End (E2E) Tests**
To validate a full user workflow from the API down to the agent response.
```bash
poetry run pytest tests/e2e/
```
Key E2E tests include:
-   `test_onboarding_complex.py`: Simulates a full, multi-turn onboarding conversation.
-   `test_api_single_coach.py`: Validates that the `singleCoach` API parameter correctly routes to a specific coach.
-   `test_coaching_stream_endpoint.py`: Tests the real-time streaming API to ensure events are correctly formatted.

### 2. Using Test Runners

The `tests/runners/` directory contains convenience scripts for executing common testing scenarios.

**Run All Core Tests**
This script runs the main unit, integration, and tool tests.
```bash
poetry run python tests/runners/run_tests.py
```

**Run Modular Agent Tests**
This script focuses on testing the multi-agent planning and routing architecture.
```bash
poetry run python tests/runners/run_modular_agent_tests.py
```

**Run a Single, Specific Query**
For debugging, you can use `run_test_query.py` to execute a single query against the comprehensive graph. You can edit the `TEST_QUERY` variable within the script.
```bash
poetry run python tests/runners/run_test_query.py
```

## Interpreting Test Output

When running tests, pay attention to the output for key information:

-   **Specialist Routing**: In integration tests, look for logs like `Specialists consulted: ['strength', 'nutrition']` to verify that the planning node correctly routed the query.
-   **Tool Usage**: For ReAct agent tests, the output will show the "thought" process and which tools were invoked.
-   **Memory Context**: Memory-related tests will indicate if context was successfully retrieved from past conversations.
-   **Assertions**: A successful test run will end with `PASSED`. Any `FAILED` tests will show a detailed error trace.

## Prerequisites for Testing

Before running most integration or E2E tests, ensure your local environment is correctly configured:

1.  **MongoDB Database**: A local or cloud-hosted MongoDB instance must be running and accessible.
2.  **Environment Variables**: Your `.env` file must be populated with the necessary credentials for Azure, OpenAI, and other external services.
3.  **Dependencies**: Ensure all project dependencies are installed with `poetry install`.

**Required Environment Variables:**
```bash
# Required for memory storage
MONGODB_URI="mongodb://localhost:27017/athlea_coaching_tests" # Use a dedicated test DB

# Required for agent execution
AZURE_OPENAI_API_KEY="your_azure_openai_api_key"
AZURE_OPENAI_ENDPOINT="your_azure_openai_endpoint"

# Optional: For full tool functionality
AIRTABLE_API_KEY="your_airtable_api_key"
GOOGLE_MAPS_API_KEY="your_google_maps_api_key"
# ... and other external service keys
```

## Running the Tests

We have several scripts available to test different aspects of the system.

### 1. Comprehensive Specialized Coaches Test

This is the main test suite for the specialized coaches.

```bash
python test_specialized_coaches.py
```

This script provides a comprehensive validation of the entire specialized coach stack, including:
-   Individual coach node functionality.
-   **Tool Integration**: Verifies that the `SpecializedCoachToolsManager` initializes correctly and that coaches receive their domain-specific tools.
-   **Memory Integration**: Confirms that conversations with specialized coaches are persisted in MongoDB and that context is correctly retrieved across sessions.
-   **Specialist Routing**: Checks that the main coaching graph routes queries to the appropriate specialist.

### 2. Quick Specialist Verification

For a faster check to ensure the specialists are importable and their dependencies are met, you can run:

```bash
python test_memory_coaching_with_specialists.py
```

This script is useful for a quick sanity check during development.

## What to Look For in Test Output

When you run the tests, you should see output that confirms the key features are working.

**Successful Test Output Example:**
```
🔧 Quick Specialist Test
==============================
✅ Specialist coaches imported successfully
✅ Tools manager initialized
✅ Strength coach tools: 3 available
...

🏃‍♂️ Memory Coaching with Specialized Coaches Demo
============================================================
...
📝 Conversation 1: First interaction...
User: Hi! I want to start a new workout routine.
----------------------------------------
🤖 Coach Response:
Hello! I'm excited to help you start your fitness journey...
👥 Specialists consulted: strength, nutrition
🧠 Memory Context: 1 relevant memories found
```

### Key Areas to Monitor in Output:

-   **Specialist Routing**: The `Specialists consulted` line should show which coach(es) were activated. Verify that a question about strength training routes to the `strength` coach.
-   **Memory Context**: The `Memory Context` line shows how many relevant memories were found from past conversations. For a new user, this should be 0. For a returning user, it should be greater than 0.
-   **Tool Usage**: For tests involving tools, you may see output indicating that a tool was called (e.g., searching an Airtable database). If API keys for external services are not provided, you should see warnings that those tools failed to initialize, but the tests should still pass, demonstrating graceful degradation.

## Troubleshooting Tests

-   **MongoDB Connection Error**: Ensure your MongoDB instance is running and the `MONGODB_URI` is correct. Check for firewall issues.
-   **Tool Initialization Warnings**: This is expected if you haven't provided all optional API keys in your `.env` file. The coaches should still function, but without the capabilities of the uninitialized tools.
-   **Import Errors**: This usually indicates a problem with your Python environment. Make sure you have installed all dependencies from `pyproject.toml` (`poetry install`).
-   **Authentication Errors**: If you see errors from Azure OpenAI, verify that your API key and endpoint are correct.

By running these tests regularly, you can ensure that the specialized coaches are functioning correctly and that their complex integrations with tools and memory remain stable. 