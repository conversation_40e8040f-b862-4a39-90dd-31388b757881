# Agent and Tool Development Guide

This guide explains how to develop new tools and integrate them with our specialized coaching agents using the Model Context Protocol (MCP).

## Overview of the Architecture

Our system uses a decoupled architecture where tools are exposed via standalone **MCP servers**. The coaching agents then connect to these servers at runtime to dynamically load and use the tools. This allows us to develop and maintain tools independently from the agents themselves.

The development process follows these steps:
1.  **Create the Tool**: Write the Python code for your new tool.
2.  **Create an MCP Server**: Expose your new tool via an MCP server.
3.  **Configure the MCP Server**: Add the new server to the MCP configuration so that development environments like Cursor IDE and LangGraph Studio can find it.
4.  **Integrate with a Coach**: Assign the new tool to the relevant specialized coach using the `SpecializedCoachToolsManager`.

---

## 1. Tool and MCP Server Development

### Step 1.1: Create the Tool Logic

First, create your tool. A tool can be a simple function or a more complex class. The key is that it should be compatible with LangChain's tool format.

### Step 1.2: Create the MCP Server

Next, create an MCP server to expose your tool. All MCP servers are located in the `mcp_servers/` directory.

An MCP server is a simple Python script that implements three key functions:
-   `@app.list_tools()`: Returns a list of the tools available on this server.
-   `@app.call_tool()`: Handles the execution of a specific tool when called by an agent.
-   `async def main()`: The server's startup function.

**Example (`mcp_servers/strength_mcp/server.py`):**
```python
from langchain_mcp_adapters.protocol import app, CallToolResult, Tool
from athlea_langgraph.tools.strength import search_strength_exercises # Your tool import

# Define the tool for the protocol
STRENGTH_TOOLS = [
    Tool(
        name="search_strength_exercises",
        description="Searches for strength exercises based on criteria.",
        input_schema={"type": "object", "properties": {"query": {"type": "string"}}},
        function=search_strength_exercises,
    )
]

@app.list_tools
async def list_tools() -> list[Tool]:
    return STRENGTH_TOOLS

@app.call_tool
async def call_tool(name: str, args: dict) -> CallToolResult:
    for tool in STRENGTH_TOOLS:
        if tool.name == name:
            try:
                # Execute the actual tool function
                result = await tool.function(**args)
                return CallToolResult(result=result)
            except Exception as e:
                return CallToolResult(error=str(e))
    return CallToolResult(error=f"Tool '{name}' not found.")

async def main():
    await app.run()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
```

---

## 2. Configuration and Integration

### Step 2.1: Configure the MCP Server for IDEs

To make your new MCP server available in development environments like Cursor IDE or LangGraph Studio, you need to add it to the MCP configuration file. For Cursor, this file is `.cursor/mcp.json`.

```json
{
  "mcpServers": {
    "athlea-strength": {
      "command": "python",
      "args": ["-m", "mcp_servers.strength_mcp.server"],
      "env": {
        "OPENAI_API_KEY": "your_openai_api_key_here"
      }
    }
    // ... add your new server here
  }
}
```

### Step 2.2: Integrate the Tool with a Specialized Coach

The final step is to make the agent aware of the new tool. This is managed by the `SpecializedCoachToolsManager`.

-   **Location**: `athlea_langgraph/agents/tools_manager.py` (or similar)

**How it Works:**
The `SpecializedCoachToolsManager` is responsible for initializing all available tools and providing the correct set of tools to each specialized coach.

1.  **Initialize the Tool**: In the `initialize_tools` method of the manager, add the logic to create an instance of your new tool. This is often done lazily to improve performance.
2.  **Assign to Coach**: In the appropriate `get_*_coach_tools` method (e.g., `get_strength_coach_tools`), add your new tool to the list of tools returned.

**Example `SpecializedCoachToolsManager`:**
```python
class SpecializedCoachToolsManager:
    async def initialize_tools(self):
        # Lazy load all tools
        self._airtable_tools = await create_airtable_mcp_tools()
        self._your_new_tool = YourNewTool() # Initialize your new tool
        # ... other tools

    def get_strength_coach_tools(self) -> List[Any]:
        tools = []
        if self._airtable_tools:
            tools.extend(self._airtable_tools)
        
        # Add your new tool to the strength coach's toolkit
        if self._your_new_tool:
            tools.append(self._your_new_tool)
            
        return tools

    # ... other get_*_coach_tools methods
```

Once this is done, the specialized coach node (e.g., `strength_coach_node`) will automatically receive the new tool when it calls the manager. The coach's underlying LLM will be aware of the tool and can decide to use it when appropriate.

This architecture ensures that tools are reusable, independently maintainable, and cleanly integrated into the agent system. 