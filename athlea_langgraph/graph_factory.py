"""
Graph Factory

This module provides a centralized, dynamic factory for creating and managing
different types of graphs in the Athlea LangGraph system. It automatically
discovers and registers graph creation functions from the 'graphs' directory.
"""

import importlib
import inspect
import logging
import os
import pkgutil
from typing import Any, Callable, Dict, List, Optional

from langgraph.checkpoint.memory import MemorySaver

# Assuming graphs are in a package 'athlea_langgraph.graphs'
from athlea_langgraph import graphs
from athlea_langgraph.memory.mongo_memory import MongoSaver

logger = logging.getLogger(__name__)

GRAPH_CREATE_PREFIX = "create_"
GRAPH_CREATE_SUFFIX = "_graph"

# Whitelist of allowed graphs - only these will be discovered and registered
ALLOWED_GRAPHS = {
    "onboarding",
    "optimized_studio",
    "strength_coach",
    "cardio_coach",
    "cycling_coach",
    "nutrition_coach",
    "recovery_coach",
    "mental_coach",
}


def is_graph_creator(func: Callable) -> bool:
    """Check if a function is a graph creator based on naming convention."""
    return (
        inspect.isfunction(func)
        and func.__name__.startswith(GRAPH_CREATE_PREFIX)
        and func.__name__.endswith(GRAPH_CREATE_SUFFIX)
    )


def is_allowed_graph(graph_name: str) -> bool:
    """Check if a graph is in the allowed whitelist."""
    return graph_name in ALLOWED_GRAPHS


class DynamicGraphFactory:
    """
    A dynamic factory that discovers and creates graph instances
    from the 'athlea_langgraph.graphs' module.
    """

    def __init__(self):
        self._graph_creators: Dict[str, Callable] = {}
        self._graph_cache: Dict[str, Any] = {}
        self._checkpointers: Dict[str, Any] = {}
        self.discover_graphs()

    def discover_graphs(self):
        """Discover all graph creation functions in the 'graphs' package."""
        logger.info("[DynamicGraphFactory] Discovering graphs...")

        graph_package_path = graphs.__path__
        graph_package_name = graphs.__name__

        for _, name, ispkg in pkgutil.walk_packages(
            graph_package_path, prefix=f"{graph_package_name}."
        ):
            if not ispkg:
                try:
                    module = importlib.import_module(name)
                    for member_name, member_obj in inspect.getmembers(module):
                        if is_graph_creator(member_obj):
                            graph_name = member_name.replace(
                                GRAPH_CREATE_PREFIX, ""
                            ).replace(GRAPH_CREATE_SUFFIX, "")

                            # Only register graphs that are in the whitelist
                            if (
                                is_allowed_graph(graph_name)
                                and graph_name not in self._graph_creators
                            ):
                                self._graph_creators[graph_name] = member_obj
                                logger.info(
                                    f"[DynamicGraphFactory] Discovered graph '{graph_name}' from {name}"
                                )
                            elif not is_allowed_graph(graph_name):
                                logger.debug(
                                    f"[DynamicGraphFactory] Skipping graph '{graph_name}' (not in whitelist)"
                                )
                except Exception as e:
                    logger.error(
                        f"[DynamicGraphFactory] Error discovering graphs in {name}: {e}"
                    )

    def list_available_graphs(self) -> List[str]:
        """Return a list of all discovered graph names."""
        return list(self._graph_creators.keys())

    def create_checkpointer(self, checkpointer_type: str = "memory", **kwargs) -> Any:
        """Create and cache a checkpointer instance."""
        cache_key = f"{checkpointer_type}_{hash(str(sorted(kwargs.items())))}"
        if cache_key not in self._checkpointers:
            if checkpointer_type == "memory":
                self._checkpointers[cache_key] = MemorySaver()
            elif checkpointer_type == "mongo":
                self._checkpointers[cache_key] = MongoSaver(**kwargs)
            else:
                raise ValueError(f"Unknown checkpointer type: {checkpointer_type}")
        return self._checkpointers[cache_key]

    def create_graph(
        self,
        graph_name: str,
        graph_config: Optional[Dict[str, Any]] = None,
        use_cache: bool = True,
    ) -> Any:
        """
        Create a graph instance by its discovered name.

        Args:
            graph_name: The name of the graph to create.
            graph_config: Configuration for the graph creation function.
            use_cache: Whether to use a cached instance of the graph.

        Returns:
            A compiled graph instance.
        """
        if graph_name not in self._graph_creators:
            raise ValueError(
                f"Graph '{graph_name}' not found. Available graphs: {self.list_available_graphs()}"
            )

        config_hash = hash(str(sorted((graph_config or {}).items())))
        cache_key = f"{graph_name}_{config_hash}"

        if use_cache and cache_key in self._graph_cache:
            logger.info(f"[DynamicGraphFactory] Returning cached graph: {cache_key}")
            return self._graph_cache[cache_key]

        logger.info(
            f"[DynamicGraphFactory] Creating new instance of graph: {graph_name}"
        )
        creator_func = self._graph_creators[graph_name]

        # Check if graph_config should be passed
        sig = inspect.signature(creator_func)
        if "config" in sig.parameters:
            graph = creator_func(config=graph_config or {})
        else:
            graph = creator_func()

        if use_cache:
            self._graph_cache[cache_key] = graph

        return graph

    def clear_cache(self):
        """Clear all caches."""
        logger.info("[DynamicGraphFactory] Clearing all caches")
        self._graph_cache.clear()
        self._checkpointers.clear()

    def refresh_graphs(self):
        """Force a re-discovery of graphs."""
        logger.info("[DynamicGraphFactory] Refreshing graph definitions")
        self._graph_creators.clear()
        self.discover_graphs()


# Singleton instance of the factory
_graph_factory = DynamicGraphFactory()


def get_graph_factory() -> DynamicGraphFactory:
    """Get the singleton instance of the dynamic graph factory."""
    return _graph_factory


# GraphType enum for backwards compatibility
class GraphType:
    """Graph type constants for backwards compatibility."""

    ONBOARDING = "onboarding"
    STRENGTH_COACH = "strength_coach"
    CARDIO_COACH = "cardio_coach"
    CYCLING_COACH = "cycling_coach"
    NUTRITION_COACH = "nutrition_coach"
    RECOVERY_COACH = "recovery_coach"
    MENTAL_COACH = "mental_coach"
    OPTIMIZED_STUDIO = "optimized_studio"


def get_graph_by_type(
    graph_type: str,
    checkpointer_type: str = "memory",
    graph_config: Optional[Dict[str, Any]] = None,
    use_cache: bool = True,
    **checkpointer_kwargs,
) -> Any:
    """
    Get a graph instance by type with appropriate checkpointer.

    This is a convenience function that combines graph creation and checkpointer setup.

    Args:
        graph_type: The type of graph to create (e.g., "onboarding", "strength_coach")
        checkpointer_type: Type of checkpointer ("memory" or "mongo")
        graph_config: Optional configuration for graph creation
        use_cache: Whether to use cached instances
        **checkpointer_kwargs: Additional arguments for checkpointer creation

    Returns:
        Compiled graph instance with checkpointer

    Raises:
        ValueError: If graph_type is not found
    """
    factory = get_graph_factory()

    # Create the graph
    graph = factory.create_graph(
        graph_name=graph_type, graph_config=graph_config, use_cache=use_cache
    )

    # Create and set checkpointer if not already set
    if not hasattr(graph, "checkpointer") or graph.checkpointer is None:
        checkpointer = factory.create_checkpointer(
            checkpointer_type=checkpointer_type, **checkpointer_kwargs
        )
        # Compile the graph with the checkpointer
        graph = graph.compile(checkpointer=checkpointer)

    logger.info(
        f"[get_graph_by_type] Created {graph_type} graph with {checkpointer_type} checkpointer"
    )

    return graph
