"""
Base Hardened Tool Implementation

Provides a robust foundation for all external tools with:
- Schema validation using Pydantic
- Circuit breaker pattern
- Timeout protection and exponential backoff retries
- Structured error handling and logging
- Performance metrics tracking

Updated to include BaseDomainTool for Phase 2: Tool Layer Organization
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union

from prometheus_client import Counter, Gauge, Histogram
from pydantic import BaseModel, Field, ValidationError, ConfigDict

# Metrics for tool performance
tool_calls_total = Counter(
    "tool_calls_total", "Total tool calls", ["tool_name", "status", "error_type"]
)

tool_duration_seconds = Histogram(
    "tool_duration_seconds", "Tool execution time", ["tool_name", "status"]
)

active_tool_calls = Gauge(
    "active_tool_calls", "Number of active tool calls", ["tool_name"]
)

logger = logging.getLogger(__name__)

# Type variable for input/output schemas
InputSchema = TypeVar("InputSchema", bound=BaseModel)
OutputSchema = TypeVar("OutputSchema", bound=BaseModel)


class ToolErrorType(Enum):
    """Types of tool errors for classification and handling."""

    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    CIRCUIT_BREAKER_OPEN = "circuit_breaker_open"
    EXTERNAL_SERVICE_ERROR = "external_service_error"
    NETWORK_ERROR = "network_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    UNKNOWN_ERROR = "unknown_error"


class ToolError(Exception):
    """Structured error for tool failures."""

    def __init__(
        self,
        message: str,
        error_type: ToolErrorType = ToolErrorType.UNKNOWN_ERROR,
        retry_after: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.retry_after = retry_after
        self.metadata = metadata or {}
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/serialization."""
        return {
            "message": self.message,
            "error_type": self.error_type.value,
            "retry_after": self.retry_after,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
        }


class ToolResponse(BaseModel, Generic[OutputSchema]):
    """Standardized tool response with metadata."""

    data: OutputSchema
    success: bool = True
    execution_time_ms: float
    tool_name: str
    request_id: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

    model_config = ConfigDict(arbitrary_types_allowed=True)


class ToolFallbackResponse(BaseModel):
    """Fallback response when tool fails gracefully."""

    message: str
    success: bool = False
    error_type: str
    retry_after: Optional[int] = None
    fallback_data: Optional[Dict[str, Any]] = None


class BaseDomainTool(ABC):
    """
    Base class for domain-specific tools in Phase 2: Tool Layer Organization.

    Provides a simpler interface for domain tools that don't need the full
    hardening features of HardenedTool but still need domain organization.
    """

    domain: str = "unknown"
    name: str = "base_tool"
    description: str = "Base domain tool"

    def __init__(self):
        """Initialize the domain tool."""
        self.permissions: List[str] = []
        logger.debug(f"Initialized domain tool: {self.name} for domain: {self.domain}")

    def get_domain(self) -> str:
        """Get the tool's domain."""
        return self.domain

    def get_name(self) -> str:
        """Get the tool's name."""
        return self.name

    def get_description(self) -> str:
        """Get the tool's description."""
        return self.description

    def get_permissions(self) -> List[str]:
        """Get required permissions for this tool."""
        return self.permissions

    def set_permissions(self, permissions: List[str]):
        """Set required permissions for this tool."""
        self.permissions = permissions


class HardenedTool(ABC, Generic[InputSchema, OutputSchema]):
    """
    Base class for hardened tools with comprehensive error handling.

    Features:
    - Input/output schema validation
    - Circuit breaker pattern
    - Timeout protection with exponential backoff
    - Structured logging and metrics
    - Graceful degradation
    """

    def __init__(
        self,
        name: str,
        input_schema: type[InputSchema],
        output_schema: type[OutputSchema],
        timeout_seconds: float = 10.0,
        max_retries: int = 3,
        circuit_breaker: Optional[object] = None,
        enable_metrics: bool = True,
    ):
        self.name = name
        self.input_schema = input_schema
        self.output_schema = output_schema
        self.timeout_seconds = timeout_seconds
        self.max_retries = max_retries
        self.circuit_breaker = circuit_breaker
        self.enable_metrics = enable_metrics

        # Internal state
        self._last_failure_time: Optional[float] = None
        self._consecutive_failures = 0

        logger.info(f"Initialized hardened tool: {name}")

    async def invoke(
        self, input_data: Dict[str, Any]
    ) -> Union[ToolResponse[OutputSchema], ToolFallbackResponse]:
        """
        Main entry point for tool execution with full hardening.

        Args:
            input_data: Raw input data to validate and process

        Returns:
            ToolResponse with validated output or ToolFallbackResponse on failure
        """
        request_id = str(uuid.uuid4())
        start_time = time.time()

        # Track active calls
        if self.enable_metrics:
            active_tool_calls.labels(tool_name=self.name).inc()

        try:
            # Log tool invocation
            logger.info(f"[{request_id}] Invoking tool: {self.name}")

            # 1. Input validation
            try:
                validated_input = self.input_schema(**input_data)
            except ValidationError as e:
                error = ToolError(
                    f"Input validation failed: {e}",
                    ToolErrorType.VALIDATION_ERROR,
                    metadata={"validation_errors": e.errors()},
                )
                return await self._handle_error(error, request_id, start_time)

            # 2. Circuit breaker check
            if self.circuit_breaker and self.circuit_breaker.is_open:
                retry_after = (
                    int(self.circuit_breaker.next_attempt_time - time.time())
                    if self.circuit_breaker.next_attempt_time
                    else self.circuit_breaker.recovery_timeout
                )
                error = ToolError(
                    "Circuit breaker is open",
                    ToolErrorType.CIRCUIT_BREAKER_OPEN,
                    retry_after=max(retry_after, 1),
                )
                return await self._handle_error(error, request_id, start_time)

            # 3. Execute with timeout and retries
            result = await self._execute_with_retries(validated_input, request_id)

            # 4. Output validation
            try:
                validated_output = self.output_schema(**result)
            except ValidationError as e:
                error = ToolError(
                    f"Output validation failed: {e}",
                    ToolErrorType.VALIDATION_ERROR,
                    metadata={"validation_errors": e.errors(), "raw_output": result},
                )
                return await self._handle_error(error, request_id, start_time)

            # 5. Success response
            execution_time = (time.time() - start_time) * 1000

            if self.enable_metrics:
                tool_calls_total.labels(
                    tool_name=self.name, status="success", error_type="none"
                ).inc()
                tool_duration_seconds.labels(
                    tool_name=self.name, status="success"
                ).observe(execution_time / 1000)

            logger.info(
                f"[{request_id}] Tool {self.name} completed successfully in {execution_time:.2f}ms"
            )

            return ToolResponse[OutputSchema](
                data=validated_output,
                execution_time_ms=execution_time,
                tool_name=self.name,
                request_id=request_id,
            )

        except Exception as e:
            # Handle unexpected errors
            if isinstance(e, ToolError):
                # This is already a ToolError, handle it directly
                return await self._handle_error(e, request_id, start_time)
            else:
                # Wrap unexpected exceptions
                error = ToolError(
                    f"Unexpected error in {self.name}: {str(e)}",
                    ToolErrorType.UNKNOWN_ERROR,
                    metadata={"exception_type": type(e).__name__},
                )
                return await self._handle_error(error, request_id, start_time)

        finally:
            # Cleanup
            if self.enable_metrics:
                active_tool_calls.labels(tool_name=self.name).dec()

    async def _execute_with_retries(
        self, validated_input: InputSchema, request_id: str
    ) -> Dict[str, Any]:
        """Execute tool with exponential backoff retries."""

        for attempt in range(self.max_retries + 1):
            try:
                # Execute with timeout and circuit breaker protection
                if self.circuit_breaker:
                    # Use circuit breaker to wrap the execution
                    result = await self.circuit_breaker.call(
                        self._execute_tool_with_timeout, validated_input
                    )
                else:
                    # Execute with timeout only
                    result = await asyncio.wait_for(
                        self._execute_tool(validated_input),
                        timeout=self.timeout_seconds,
                    )

                # Reset failure tracking on success
                self._consecutive_failures = 0
                self._last_failure_time = None

                return result

            except asyncio.TimeoutError:
                logger.warning(
                    f"[{request_id}] Tool {self.name} timeout on attempt {attempt + 1}"
                )
                if attempt == self.max_retries:
                    raise ToolError(
                        f"Tool {self.name} timed out after {self.max_retries + 1} attempts",
                        ToolErrorType.TIMEOUT_ERROR,
                    )

            except Exception as e:
                logger.warning(
                    f"[{request_id}] Tool {self.name} error on attempt {attempt + 1}: {e}"
                )
                if attempt == self.max_retries:
                    # Classify error type based on the original exception
                    if hasattr(e, "error_type"):
                        # This is already a ToolError
                        raise e
                    else:
                        # Classify the exception
                        error_type = self._classify_error(e)
                        raise ToolError(
                            f"Tool {self.name} failed after {self.max_retries + 1} attempts: {str(e)}",
                            error_type,
                            metadata={"last_exception": str(e)},
                        )

            # Exponential backoff
            if attempt < self.max_retries:
                delay = 2**attempt + (time.time() % 1)  # Add jitter
                logger.info(f"[{request_id}] Retrying {self.name} in {delay:.2f}s...")
                await asyncio.sleep(delay)

    async def _execute_tool_with_timeout(
        self, validated_input: InputSchema
    ) -> Dict[str, Any]:
        """Execute tool with timeout wrapper for circuit breaker."""
        return await asyncio.wait_for(
            self._execute_tool(validated_input), timeout=self.timeout_seconds
        )

    def _classify_error(self, error: Exception) -> ToolErrorType:
        """Classify errors for better handling."""
        # If it's already a ToolError, preserve its error type
        if isinstance(error, ToolError):
            return error.error_type

        error_str = str(error).lower()

        if "timeout" in error_str:
            return ToolErrorType.TIMEOUT_ERROR
        elif "network" in error_str or "connection" in error_str:
            return ToolErrorType.NETWORK_ERROR
        elif "auth" in error_str or "permission" in error_str:
            return ToolErrorType.AUTHENTICATION_ERROR
        elif "rate limit" in error_str or "throttle" in error_str:
            return ToolErrorType.RATE_LIMIT_ERROR
        else:
            return ToolErrorType.EXTERNAL_SERVICE_ERROR

    async def _handle_error(
        self, error: ToolError, request_id: str, start_time: float
    ) -> ToolFallbackResponse:
        """Handle tool errors with structured logging and metrics."""

        execution_time = (time.time() - start_time) * 1000

        # Update failure tracking
        self._consecutive_failures += 1
        self._last_failure_time = time.time()

        # Log structured error
        logger.error(f"[{request_id}] Tool {self.name} failed: {error.to_dict()}")

        # Track metrics
        if self.enable_metrics:
            tool_calls_total.labels(
                tool_name=self.name, status="error", error_type=error.error_type.value
            ).inc()
            tool_duration_seconds.labels(tool_name=self.name, status="error").observe(
                execution_time / 1000
            )

        # Generate fallback response
        fallback_data = await self._generate_fallback(error)

        return ToolFallbackResponse(
            message=error.message,
            error_type=error.error_type.value,
            retry_after=error.retry_after,
            fallback_data=fallback_data,
        )

    @abstractmethod
    async def _execute_tool(self, validated_input: InputSchema) -> Dict[str, Any]:
        """
        Implement the actual tool logic here.

        Args:
            validated_input: Input data that has passed schema validation

        Returns:
            Raw output data (will be validated against output schema)

        Raises:
            Any exception (will be caught and handled by the framework)
        """
        pass

    async def _generate_fallback(self, error: ToolError) -> Optional[Dict[str, Any]]:
        """
        Generate fallback data when tool fails.
        Override this method to provide domain-specific fallbacks.

        Args:
            error: The error that caused the failure

        Returns:
            Optional fallback data
        """
        return {
            "fallback_message": f"The {self.name} service is temporarily unavailable. Please try again later.",
            "error_type": error.error_type.value,
            "timestamp": error.timestamp.isoformat(),
        }
