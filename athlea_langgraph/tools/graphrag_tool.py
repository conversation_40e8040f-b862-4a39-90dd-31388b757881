"""
Standalone GraphRAG Tool for Domain Agent Integration

This tool can be directly added to any domain agent's tool list,
following the patterns from the existing codebase and avoiding
Pydantic field issues by not using instance variables.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

from langchain_core.tools import BaseTool

logger = logging.getLogger(__name__)


class GraphRAGTool(BaseTool):
    """
    Standalone GraphRAG tool that can be added to any domain agent.

    Uses proper LangChain tool patterns without Pydantic field conflicts.
    """

    name: str = "graphrag_search"
    description: str = """Search the knowledge base for evidence-based strategies, research, and best practices.

Use this tool when you need:
- Recent scientific research or studies
- Evidence-based training protocols  
- Specific performance optimization strategies
- Detailed technical information beyond basic coaching knowledge
- Verification of coaching recommendations with research

Input: A specific research question or topic as a string.
Example: "What does research say about optimal protein timing for strength training?"
"""

    def _run(self, query: str, domain: str = "fitness") -> str:
        """Sync wrapper for async execution."""
        try:
            # Run in async context
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're already in an async context, can't use run()
                return "GraphRAG search requires async execution. Tool will be called asynchronously."
            else:
                return loop.run_until_complete(self._arun(query, domain))
        except Exception as e:
            logger.error(f"GraphRAG sync execution error: {e}")
            return f"Unable to execute GraphRAG search: {str(e)}"

    async def _arun(self, query: str, domain: str = "fitness") -> str:
        """Async execution of GraphRAG search."""
        try:
            logger.info(
                f"🔍 GRAPHRAG_TOOL: Searching for '{query}' in domain '{domain}'"
            )

            # Import GraphRAG retriever (lazy import to avoid circular dependencies)
            from ..agents.graphrag_nodes import get_graphrag_retriever

            # Get GraphRAG retriever
            retriever = await get_graphrag_retriever()

            # Perform hybrid search (vector + graph)
            search_results = await retriever.hybrid_search(query, domain)

            # Extract results
            vector_results = search_results.get("vector_results", [])
            graph_results = search_results.get("graph_results", [])

            # Synthesize knowledge using the same method as graphrag_nodes
            knowledge_context = await retriever.synthesize_knowledge(
                vector_results, graph_results, query, domain
            )

            logger.info(
                f"✅ GRAPHRAG_TOOL: Retrieved knowledge ({len(knowledge_context)} chars)"
            )

            # Return structured response
            if knowledge_context and len(knowledge_context.strip()) > 50:
                return f"Research findings for '{query}':\n\n{knowledge_context}"
            else:
                return f"No specific research data found for '{query}'. Consider rephrasing your question or asking about general best practices."

        except Exception as e:
            logger.error(f"❌ GRAPHRAG_TOOL: Error during search: {e}")
            return f"Unable to retrieve knowledge for '{query}': {str(e)}. You can still provide general coaching advice based on established principles."


# Factory function for easy integration
def create_graphrag_tool() -> GraphRAGTool:
    """
    Factory function to create a GraphRAG tool instance.

    This can be imported and used in any domain agent:

    ```python
    from ..tools.graphrag_tool import create_graphrag_tool

    async def get_domain_tools(self):
        tools = []
        # ... existing tools ...
        tools.append(create_graphrag_tool())
        return tools
    ```
    """
    return GraphRAGTool()


# Global instance for reuse
_graphrag_tool_instance = None


def get_shared_graphrag_tool() -> GraphRAGTool:
    """Get a shared GraphRAG tool instance to avoid creating multiple instances."""
    global _graphrag_tool_instance
    if _graphrag_tool_instance is None:
        _graphrag_tool_instance = GraphRAGTool()
    return _graphrag_tool_instance
