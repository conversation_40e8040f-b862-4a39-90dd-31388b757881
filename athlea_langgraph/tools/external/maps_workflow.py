"""
Maps Workflow Tool

A comprehensive workflow that combines multiple mapping and location services:
- Google Maps Elevation API for terrain analysis
- Azure Maps for location data, geocoding, and nearby search
- Weather data for environmental conditions

This workflow provides complete location analysis for fitness activities,
route planning, and environmental assessment.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..base_tool import HardenedTool, ToolError, ToolErrorType
from ..circuit_breaker import CircuitBreaker
from .azure_maps import AzureMapsTool
from .google_maps_elevation import GoogleMapsElevationTool

logger = logging.getLogger(__name__)


class MapsWorkflowInput(BaseModel):
    """Input schema for Maps Workflow."""

    workflow_type: str = Field(
        description="Type of workflow: 'location_analysis', 'route_planning', 'area_exploration', 'weather_check'"
    )

    # Location input (either address or coordinates)
    address: Optional[str] = Field(
        default=None, description="Address to analyze (will be geocoded to coordinates)"
    )
    lat: Optional[float] = Field(
        default=None, description="Latitude for coordinate-based analysis"
    )
    lon: Optional[float] = Field(
        default=None, description="Longitude for coordinate-based analysis"
    )

    # Route planning specific
    route_points: Optional[List[Dict[str, float]]] = Field(
        default=None, description="List of lat/lon points for route elevation profile"
    )

    # Area exploration specific
    search_query: Optional[str] = Field(
        default=None,
        description="Search query for nearby places (e.g., 'gyms', 'parks', 'trails')",
    )
    search_radius: Optional[int] = Field(
        default=5000, description="Search radius in meters for nearby places"
    )

    # Additional options
    include_elevation: bool = Field(
        default=True, description="Include elevation data in analysis"
    )
    include_weather: bool = Field(
        default=True, description="Include weather data in analysis"
    )
    include_nearby: bool = Field(
        default=True, description="Include nearby places search"
    )


class MapsWorkflowOutput(BaseModel):
    """Output schema for Maps Workflow."""

    workflow_type: str = Field(..., description="Type of workflow executed")
    success: bool = Field(..., description="Overall workflow success")
    location: Optional[Dict[str, Any]] = Field(
        None, description="Primary location data (geocoded if needed)"
    )
    elevation_data: Optional[Dict[str, Any]] = Field(
        None, description="Elevation analysis results"
    )
    weather_data: Optional[Dict[str, Any]] = Field(
        None, description="Weather conditions"
    )
    nearby_places: Optional[Dict[str, Any]] = Field(
        None, description="Nearby places and facilities"
    )
    route_analysis: Optional[Dict[str, Any]] = Field(
        None, description="Route elevation profile and metrics"
    )
    summary: str = Field(..., description="Human-readable summary of results")
    execution_steps: List[str] = Field(
        default_factory=list, description="Steps executed in workflow"
    )
    errors: List[str] = Field(
        default_factory=list, description="Any errors encountered during execution"
    )


class MapsWorkflowLangChainTool(BaseTool):
    """LangChain-compatible wrapper for Maps Workflow."""

    name: str = "maps_workflow"
    description: str = """Comprehensive location analysis workflow combining elevation, weather, and mapping data.
    
    Workflow types available:
    - location_analysis: Complete analysis of a single location (elevation, weather, nearby facilities)
    - route_planning: Elevation profile and terrain analysis for a planned route
    - area_exploration: Discover nearby facilities, trails, and points of interest
    - weather_check: Weather conditions and environmental factors for outdoor activities
    
    Perfect for fitness route planning, finding training locations, and environmental assessment for outdoor activities."""

    args_schema: type[BaseModel] = MapsWorkflowInput
    maps_workflow: "MapsWorkflow" = Field(exclude=True)

    def __init__(self, maps_workflow: "MapsWorkflow"):
        super().__init__(maps_workflow=maps_workflow)

    async def _arun(self, **kwargs) -> str:
        """Async implementation of the tool."""
        try:
            result = await self.maps_workflow.execute(MapsWorkflowInput(**kwargs))
            return f"Maps workflow completed: {result.model_dump_json()}"
        except Exception as e:
            return f"Error in maps workflow: {str(e)}"

    def _run(self, **kwargs) -> str:
        """Sync implementation of the tool."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                return f"Maps workflow requires async execution. Error: Cannot run sync in async context."
            else:
                result = loop.run_until_complete(
                    self.maps_workflow.execute(MapsWorkflowInput(**kwargs))
                )
                return f"Maps workflow completed: {result.model_dump_json()}"
        except Exception as e:
            return f"Error in maps workflow: {str(e)}"


class MapsWorkflow(HardenedTool[MapsWorkflowInput, MapsWorkflowOutput]):
    """
    Maps Workflow that orchestrates multiple mapping and location services.

    This workflow provides comprehensive location analysis by combining:
    1. Azure Maps for geocoding, weather, and nearby search
    2. Google Maps Elevation for terrain analysis
    3. Intelligent workflow orchestration based on user needs
    """

    def __init__(
        self,
        azure_maps_key: Optional[str] = None,
        google_maps_key: Optional[str] = None,
        timeout_seconds: float = 30.0,
        max_retries: int = 2,
    ):
        """
        Initialize Maps Workflow.

        Args:
            azure_maps_key: Azure Maps subscription key
            google_maps_key: Google Maps API key
            timeout_seconds: Overall workflow timeout
            max_retries: Maximum retry attempts for the workflow
        """
        # Initialize individual tools
        try:
            self.azure_maps = AzureMapsTool(subscription_key=azure_maps_key)
            logger.info("✅ Azure Maps tool initialized")
        except Exception as e:
            logger.warning(f"⚠️ Azure Maps tool initialization failed: {e}")
            self.azure_maps = None

        try:
            self.elevation_tool = GoogleMapsElevationTool(api_key=google_maps_key)
            logger.info("✅ Google Maps Elevation tool initialized")
        except Exception as e:
            logger.warning(f"⚠️ Google Maps Elevation tool initialization failed: {e}")
            self.elevation_tool = None

        # Circuit breaker for the workflow
        circuit_breaker = CircuitBreaker(
            name="maps_workflow",
            failure_threshold=3,
            recovery_timeout=120,
            expected_exception=(ToolError, asyncio.TimeoutError),
        )

        super().__init__(
            name="maps_workflow",
            input_schema=MapsWorkflowInput,
            output_schema=MapsWorkflowOutput,
            timeout_seconds=timeout_seconds,
            max_retries=max_retries,
            circuit_breaker=circuit_breaker,
        )

        logger.info("🗺️ Maps Workflow initialized")

    def to_langchain_tool(self) -> MapsWorkflowLangChainTool:
        """Convert to LangChain-compatible tool."""
        return MapsWorkflowLangChainTool(maps_workflow=self)

    async def _execute_tool(self, validated_input: MapsWorkflowInput) -> Dict[str, Any]:
        """
        Execute the maps workflow based on the workflow type.

        Args:
            validated_input: Validated input parameters

        Returns:
            Comprehensive maps workflow results
        """
        workflow_type = validated_input.workflow_type
        execution_steps = []
        errors = []

        logger.info(f"🚀 Starting maps workflow: {workflow_type}")

        try:
            # Step 1: Get primary location (geocode if needed)
            location_data = await self._get_primary_location(
                validated_input, execution_steps, errors
            )

            # Extract coordinates for subsequent steps
            if location_data and location_data.get("success"):
                if location_data.get("geocode_result"):
                    lat = location_data["geocode_result"]["position"]["lat"]
                    lon = location_data["geocode_result"]["position"]["lon"]
                else:
                    lat = validated_input.lat
                    lon = validated_input.lon
            else:
                lat = validated_input.lat
                lon = validated_input.lon

            # Execute workflow based on type
            if workflow_type == "location_analysis":
                return await self._execute_location_analysis(
                    validated_input, lat, lon, location_data, execution_steps, errors
                )
            elif workflow_type == "route_planning":
                return await self._execute_route_planning(
                    validated_input, lat, lon, location_data, execution_steps, errors
                )
            elif workflow_type == "area_exploration":
                return await self._execute_area_exploration(
                    validated_input, lat, lon, location_data, execution_steps, errors
                )
            elif workflow_type == "weather_check":
                return await self._execute_weather_check(
                    validated_input, lat, lon, location_data, execution_steps, errors
                )
            else:
                raise ToolError(
                    f"Unsupported workflow type: {workflow_type}",
                    ToolErrorType.VALIDATION_ERROR,
                )

        except Exception as e:
            logger.error(f"❌ Maps workflow error: {e}")
            errors.append(str(e))

            return {
                "workflow_type": workflow_type,
                "success": False,
                "location": location_data if "location_data" in locals() else None,
                "summary": f"Workflow failed: {str(e)}",
                "execution_steps": execution_steps,
                "errors": errors,
            }

    async def _get_primary_location(
        self,
        validated_input: MapsWorkflowInput,
        execution_steps: List[str],
        errors: List[str],
    ) -> Optional[Dict[str, Any]]:
        """Get primary location data, geocoding address if needed."""

        if validated_input.address and self.azure_maps:
            execution_steps.append("geocoding_address")
            logger.info(f"📍 Geocoding address: {validated_input.address}")

            try:
                geocode_input = {
                    "command": "geocode",
                    "address": validated_input.address,
                }
                result = await self.azure_maps.invoke(geocode_input)

                if result.success:
                    execution_steps.append("geocoding_success")
                    return result.data.model_dump()
                else:
                    errors.append(f"Geocoding failed: {result.message}")
                    execution_steps.append("geocoding_failed")

            except Exception as e:
                errors.append(f"Geocoding error: {str(e)}")
                execution_steps.append("geocoding_error")

        elif validated_input.lat and validated_input.lon:
            execution_steps.append("using_coordinates")
            return {
                "success": True,
                "command": "coordinates",
                "position": {"lat": validated_input.lat, "lon": validated_input.lon},
            }

        return None

    async def _execute_location_analysis(
        self,
        validated_input: MapsWorkflowInput,
        lat: Optional[float],
        lon: Optional[float],
        location_data: Optional[Dict[str, Any]],
        execution_steps: List[str],
        errors: List[str],
    ) -> Dict[str, Any]:
        """Execute comprehensive location analysis workflow."""

        execution_steps.append("location_analysis_start")

        elevation_data = None
        weather_data = None
        nearby_places = None

        # Get elevation data
        if validated_input.include_elevation and lat and lon and self.elevation_tool:
            execution_steps.append("fetching_elevation")
            try:
                elevation_input = {"mode": "point", "lat": lat, "lon": lon}
                result = await self.elevation_tool.invoke(elevation_input)
                if result.success:
                    elevation_data = result.data.model_dump()
                    execution_steps.append("elevation_success")
                else:
                    errors.append(f"Elevation failed: {result.message}")
                    execution_steps.append("elevation_failed")
            except Exception as e:
                errors.append(f"Elevation error: {str(e)}")
                execution_steps.append("elevation_error")

        # Get weather data
        if validated_input.include_weather and lat and lon and self.azure_maps:
            execution_steps.append("fetching_weather")
            try:
                weather_input = {"command": "weather", "lat": lat, "lon": lon}
                result = await self.azure_maps.invoke(weather_input)
                if result.success:
                    weather_data = result.data.model_dump()
                    execution_steps.append("weather_success")
                else:
                    errors.append(f"Weather failed: {result.message}")
                    execution_steps.append("weather_failed")
            except Exception as e:
                errors.append(f"Weather error: {str(e)}")
                execution_steps.append("weather_error")

        # Get nearby places (default search for fitness facilities)
        if validated_input.include_nearby and lat and lon and self.azure_maps:
            execution_steps.append("fetching_nearby")
            search_query = (
                validated_input.search_query or "gyms fitness parks recreation"
            )
            try:
                nearby_input = {
                    "command": "nearby",
                    "lat": lat,
                    "lon": lon,
                    "query": search_query,
                    "radius": validated_input.search_radius,
                }
                result = await self.azure_maps.invoke(nearby_input)
                if result.success:
                    nearby_places = result.data.model_dump()
                    execution_steps.append("nearby_success")
                else:
                    errors.append(f"Nearby search failed: {result.message}")
                    execution_steps.append("nearby_failed")
            except Exception as e:
                errors.append(f"Nearby search error: {str(e)}")
                execution_steps.append("nearby_error")

        # Generate summary
        summary = self._generate_location_summary(
            location_data, elevation_data, weather_data, nearby_places
        )

        execution_steps.append("location_analysis_complete")

        return {
            "workflow_type": "location_analysis",
            "success": True,
            "location": location_data,
            "elevation_data": elevation_data,
            "weather_data": weather_data,
            "nearby_places": nearby_places,
            "summary": summary,
            "execution_steps": execution_steps,
            "errors": errors,
        }

    async def _execute_route_planning(
        self,
        validated_input: MapsWorkflowInput,
        lat: Optional[float],
        lon: Optional[float],
        location_data: Optional[Dict[str, Any]],
        execution_steps: List[str],
        errors: List[str],
    ) -> Dict[str, Any]:
        """Execute route planning workflow with elevation profile."""

        execution_steps.append("route_planning_start")

        route_analysis = None
        weather_data = None

        # Get route elevation profile
        if validated_input.route_points and self.elevation_tool:
            execution_steps.append("calculating_route_elevation")
            try:
                # Convert route points to ElevationPoint format
                from ..schemas.elevation_schemas import ElevationPoint

                points = [
                    ElevationPoint(lat=p["lat"], lon=p["lon"])
                    for p in validated_input.route_points
                ]

                elevation_input = {"mode": "path", "points": points}
                result = await self.elevation_tool.invoke(elevation_input)
                if result.success:
                    route_analysis = result.data.model_dump()
                    execution_steps.append("route_elevation_success")
                else:
                    errors.append(f"Route elevation failed: {result.message}")
                    execution_steps.append("route_elevation_failed")
            except Exception as e:
                errors.append(f"Route elevation error: {str(e)}")
                execution_steps.append("route_elevation_error")

        # Get weather for starting point
        if lat and lon and self.azure_maps:
            execution_steps.append("fetching_route_weather")
            try:
                weather_input = {"command": "weather", "lat": lat, "lon": lon}
                result = await self.azure_maps.invoke(weather_input)
                if result.success:
                    weather_data = result.data.model_dump()
                    execution_steps.append("route_weather_success")
                else:
                    errors.append(f"Route weather failed: {result.message}")
                    execution_steps.append("route_weather_failed")
            except Exception as e:
                errors.append(f"Route weather error: {str(e)}")
                execution_steps.append("route_weather_error")

        # Generate route summary
        summary = self._generate_route_summary(
            location_data, route_analysis, weather_data
        )

        execution_steps.append("route_planning_complete")

        return {
            "workflow_type": "route_planning",
            "success": True,
            "location": location_data,
            "route_analysis": route_analysis,
            "weather_data": weather_data,
            "summary": summary,
            "execution_steps": execution_steps,
            "errors": errors,
        }

    async def _execute_area_exploration(
        self,
        validated_input: MapsWorkflowInput,
        lat: Optional[float],
        lon: Optional[float],
        location_data: Optional[Dict[str, Any]],
        execution_steps: List[str],
        errors: List[str],
    ) -> Dict[str, Any]:
        """Execute area exploration workflow to find facilities and points of interest."""

        execution_steps.append("area_exploration_start")

        nearby_places = None
        routes_and_trails = None
        weather_data = None

        # Search for specified facilities
        if lat and lon and self.azure_maps:
            search_query = (
                validated_input.search_query or "gyms fitness centers parks trails"
            )
            execution_steps.append("searching_facilities")
            try:
                nearby_input = {
                    "command": "nearby",
                    "lat": lat,
                    "lon": lon,
                    "query": search_query,
                    "radius": validated_input.search_radius,
                }
                result = await self.azure_maps.invoke(nearby_input)
                if result.success:
                    nearby_places = result.data.model_dump()
                    execution_steps.append("facilities_search_success")
                else:
                    errors.append(f"Facilities search failed: {result.message}")
                    execution_steps.append("facilities_search_failed")
            except Exception as e:
                errors.append(f"Facilities search error: {str(e)}")
                execution_steps.append("facilities_search_error")

            # Search for routes and trails
            execution_steps.append("searching_routes")
            try:
                routes_input = {
                    "command": "routes",
                    "lat": lat,
                    "lon": lon,
                    "radius": validated_input.search_radius,
                }
                result = await self.azure_maps.invoke(routes_input)
                if result.success:
                    routes_and_trails = result.data.model_dump()
                    execution_steps.append("routes_search_success")
                else:
                    errors.append(f"Routes search failed: {result.message}")
                    execution_steps.append("routes_search_failed")
            except Exception as e:
                errors.append(f"Routes search error: {str(e)}")
                execution_steps.append("routes_search_error")

        # Get weather for the area
        if lat and lon and self.azure_maps:
            execution_steps.append("fetching_area_weather")
            try:
                weather_input = {"command": "weather", "lat": lat, "lon": lon}
                result = await self.azure_maps.invoke(weather_input)
                if result.success:
                    weather_data = result.data.model_dump()
                    execution_steps.append("area_weather_success")
                else:
                    errors.append(f"Area weather failed: {result.message}")
                    execution_steps.append("area_weather_failed")
            except Exception as e:
                errors.append(f"Area weather error: {str(e)}")
                execution_steps.append("area_weather_error")

        # Generate exploration summary
        summary = self._generate_exploration_summary(
            location_data, nearby_places, routes_and_trails, weather_data
        )

        execution_steps.append("area_exploration_complete")

        return {
            "workflow_type": "area_exploration",
            "success": True,
            "location": location_data,
            "nearby_places": nearby_places,
            "route_analysis": routes_and_trails,
            "weather_data": weather_data,
            "summary": summary,
            "execution_steps": execution_steps,
            "errors": errors,
        }

    async def _execute_weather_check(
        self,
        validated_input: MapsWorkflowInput,
        lat: Optional[float],
        lon: Optional[float],
        location_data: Optional[Dict[str, Any]],
        execution_steps: List[str],
        errors: List[str],
    ) -> Dict[str, Any]:
        """Execute weather check workflow for outdoor activity planning."""

        execution_steps.append("weather_check_start")

        weather_data = None

        # Get detailed weather data
        if lat and lon and self.azure_maps:
            execution_steps.append("fetching_detailed_weather")
            try:
                weather_input = {"command": "weather", "lat": lat, "lon": lon}
                result = await self.azure_maps.invoke(weather_input)
                if result.success:
                    weather_data = result.data.model_dump()
                    execution_steps.append("detailed_weather_success")
                else:
                    errors.append(f"Weather check failed: {result.message}")
                    execution_steps.append("detailed_weather_failed")
            except Exception as e:
                errors.append(f"Weather check error: {str(e)}")
                execution_steps.append("detailed_weather_error")

        # Generate weather summary with fitness recommendations
        summary = self._generate_weather_summary(location_data, weather_data)

        execution_steps.append("weather_check_complete")

        return {
            "workflow_type": "weather_check",
            "success": True,
            "location": location_data,
            "weather_data": weather_data,
            "summary": summary,
            "execution_steps": execution_steps,
            "errors": errors,
        }

    def _generate_location_summary(
        self,
        location_data: Optional[Dict[str, Any]],
        elevation_data: Optional[Dict[str, Any]],
        weather_data: Optional[Dict[str, Any]],
        nearby_places: Optional[Dict[str, Any]],
    ) -> str:
        """Generate human-readable summary for location analysis."""

        summary_parts = []

        if location_data:
            if location_data.get("geocode_result"):
                address = (
                    location_data["geocode_result"]
                    .get("address", {})
                    .get("freeform_address", "Unknown location")
                )
                summary_parts.append(f"📍 Location: {address}")
            else:
                lat = location_data.get("position", {}).get("lat")
                lon = location_data.get("position", {}).get("lon")
                if lat and lon:
                    summary_parts.append(f"📍 Coordinates: {lat:.4f}, {lon:.4f}")

        if elevation_data:
            elevation = elevation_data.get("elevation")
            if elevation is not None:
                summary_parts.append(f"⛰️ Elevation: {elevation:.1f}m above sea level")

        if weather_data and weather_data.get("weather_result"):
            weather = weather_data["weather_result"]
            temp = weather.get("temperature", {}).get("value")
            condition = weather.get("condition")
            if temp is not None and condition:
                summary_parts.append(f"🌤️ Weather: {temp:.1f}°C, {condition}")

        if nearby_places:
            location_count = nearby_places.get("location_count", 0)
            if location_count > 0:
                summary_parts.append(
                    f"🏃‍♂️ Found {location_count} nearby fitness facilities"
                )

        if not summary_parts:
            return "Location analysis completed with limited data availability."

        return " | ".join(summary_parts)

    def _generate_route_summary(
        self,
        location_data: Optional[Dict[str, Any]],
        route_analysis: Optional[Dict[str, Any]],
        weather_data: Optional[Dict[str, Any]],
    ) -> str:
        """Generate human-readable summary for route planning."""

        summary_parts = []

        if route_analysis:
            min_elev = route_analysis.get("min_elevation")
            max_elev = route_analysis.get("max_elevation")
            gain = route_analysis.get("elevation_gain")
            loss = route_analysis.get("elevation_loss")

            if min_elev is not None and max_elev is not None:
                summary_parts.append(f"⛰️ Elevation: {min_elev:.0f}m to {max_elev:.0f}m")

            if gain is not None and loss is not None:
                summary_parts.append(f"📈 Gain: {gain:.0f}m, Loss: {loss:.0f}m")

        if weather_data and weather_data.get("weather_result"):
            weather = weather_data["weather_result"]
            temp = weather.get("temperature", {}).get("value")
            condition = weather.get("condition")
            if temp is not None and condition:
                summary_parts.append(f"🌤️ Conditions: {temp:.1f}°C, {condition}")

        if not summary_parts:
            return "Route planning completed with limited elevation data."

        return " | ".join(summary_parts)

    def _generate_exploration_summary(
        self,
        location_data: Optional[Dict[str, Any]],
        nearby_places: Optional[Dict[str, Any]],
        routes_and_trails: Optional[Dict[str, Any]],
        weather_data: Optional[Dict[str, Any]],
    ) -> str:
        """Generate human-readable summary for area exploration."""

        summary_parts = []

        if nearby_places:
            location_count = nearby_places.get("location_count", 0)
            if location_count > 0:
                summary_parts.append(f"🏋️‍♀️ {location_count} fitness facilities found")

        if routes_and_trails:
            trail_count = routes_and_trails.get("location_count", 0)
            if trail_count > 0:
                summary_parts.append(f"🥾 {trail_count} trails and routes discovered")

        if weather_data and weather_data.get("weather_result"):
            weather = weather_data["weather_result"]
            temp = weather.get("temperature", {}).get("value")
            condition = weather.get("condition")
            if temp is not None and condition:
                summary_parts.append(f"🌤️ Current: {temp:.1f}°C, {condition}")

        if not summary_parts:
            return "Area exploration completed with limited facility data."

        return " | ".join(summary_parts)

    def _generate_weather_summary(
        self,
        location_data: Optional[Dict[str, Any]],
        weather_data: Optional[Dict[str, Any]],
    ) -> str:
        """Generate human-readable weather summary with fitness recommendations."""

        if not weather_data or not weather_data.get("weather_result"):
            return "Weather data unavailable for fitness recommendations."

        weather = weather_data["weather_result"]
        temp = weather.get("temperature", {}).get("value")
        condition = weather.get("condition", "Unknown")
        humidity = weather.get("humidity", {}).get("value")
        wind_speed = weather.get("wind_speed", {}).get("value")

        summary_parts = [f"🌤️ {condition}"]

        if temp is not None:
            summary_parts.append(f"{temp:.1f}°C")

            # Add fitness recommendations based on temperature
            if temp < 5:
                summary_parts.append("❄️ Cold - indoor activities recommended")
            elif temp < 15:
                summary_parts.append("🧥 Cool - dress warmly for outdoor activities")
            elif temp < 25:
                summary_parts.append("👌 Ideal for outdoor fitness activities")
            elif temp < 30:
                summary_parts.append("☀️ Warm - stay hydrated during activities")
            else:
                summary_parts.append(
                    "🔥 Hot - consider indoor activities or early morning/evening workouts"
                )

        if humidity is not None:
            summary_parts.append(f"💧 {humidity:.0f}% humidity")

        if wind_speed is not None:
            summary_parts.append(f"💨 {wind_speed:.1f} km/h wind")

        return " | ".join(summary_parts)

    async def _generate_fallback(self, error: ToolError) -> Optional[Dict[str, Any]]:
        """Generate fallback response for workflow failures."""

        return {
            "workflow_type": "unknown",
            "success": False,
            "summary": f"Maps workflow temporarily unavailable: {error.message}",
            "execution_steps": ["fallback_response"],
            "errors": [str(error)],
        }


# Factory function to create the tool with auto-configuration
async def create_maps_workflow_tool(
    azure_maps_key: Optional[str] = None, google_maps_key: Optional[str] = None
) -> MapsWorkflowLangChainTool:
    """
    Create a Maps Workflow tool with automatic configuration.

    Args:
        azure_maps_key: Azure Maps subscription key (optional, will use config/env)
        google_maps_key: Google Maps API key (optional, will use config/env)

    Returns:
        LangChain-compatible Maps Workflow tool
    """
    workflow = MapsWorkflow(
        azure_maps_key=azure_maps_key, google_maps_key=google_maps_key
    )
    return workflow.to_langchain_tool()
