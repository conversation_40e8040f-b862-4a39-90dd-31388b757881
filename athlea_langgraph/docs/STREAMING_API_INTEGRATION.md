# Streaming Coaching API Integration Guide

This guide explains how to integrate the new streaming coaching API with the frontend application.

## Overview

The streaming coaching API provides real-time streaming of ReAct coaching responses using Server-Sent Events (SSE). It supports all specialized coach types with tool integration and proper error handling.

## API Endpoints

### 1. Stream Coaching Session
**POST** `/coaching/stream`

Streams a ReAct coaching session with real-time updates.

#### Request Body
```typescript
interface CoachingRequest {
  message: string;                    // User's message to the coach
  coach_type: string;                // Type of coach (see available types below)
  user_profile?: object;             // Optional user profile information
  conversation_history?: Array<{     // Optional conversation history
    role: "user" | "assistant";
    content: string;
  }>;
  session_id?: string;               // Optional session identifier
}
```

#### Available Coach Types
- `strength` - Strength Training Coach
- `cardio` - Cardiovascular Training Coach  
- `cycling` - Cycling Coach
- `nutrition` - Nutrition Coach
- `recovery` - Recovery Coach
- `mental` - Mental Training Coach

#### Response Format
The endpoint returns a streaming response with Server-Sent Events (SSE). Each event has this format:

```typescript
interface StreamEvent {
  event: string;                     // Event type
  data: object;                      // Event-specific data
  timestamp: number;                 // Event timestamp
}
```

#### Event Types

1. **status** - Status updates
```typescript
{
  event: "status",
  data: {
    type: "session_started" | "thinking" | "completed",
    message: string,
    coach_type?: string
  }
}
```

2. **response_start** - Response beginning
```typescript
{
  event: "response_start", 
  data: {
    coach_type: string,
    total_length: number
  }
}
```

3. **response_chunk** - Response content chunks
```typescript
{
  event: "response_chunk",
  data: {
    chunk: string,
    position: number,
    is_final: boolean
  }
}
```

4. **error** - Error events
```typescript
{
  event: "error",
  data: {
    message: string,
    coach_type: string
  }
}
```

### 2. Get Available Coaches
**GET** `/coaching/coaches`

Returns information about available coach types.

#### Response
```typescript
{
  coaches: {
    [coach_type: string]: {
      name: string,
      description: string,
      specialties: string[]
    }
  },
  total_count: number
}
```

### 3. Health Check
**GET** `/coaching/health`

Returns API health status and initialization state.

#### Response
```typescript
{
  status: "healthy" | "unhealthy",
  service: string,
  coaches_available: string[],
  tools_initialized: boolean,
  error?: string
}
```

## Frontend Integration Examples

### React/TypeScript Integration

```typescript
import { useState, useEffect } from 'react';

interface CoachingMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

interface CoachingSession {
  messages: CoachingMessage[];
  isStreaming: boolean;
  currentResponse: string;
  error?: string;
}

export function useCoachingStream() {
  const [session, setSession] = useState<CoachingSession>({
    messages: [],
    isStreaming: false,
    currentResponse: '',
  });

  const streamCoachingResponse = async (
    message: string,
    coachType: string,
    userProfile?: object
  ) => {
    try {
      setSession(prev => ({
        ...prev,
        isStreaming: true,
        currentResponse: '',
        error: undefined,
      }));

      // Add user message
      const userMessage: CoachingMessage = {
        role: 'user',
        content: message,
        timestamp: Date.now(),
      };

      setSession(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
      }));

      const response = await fetch('/api/coaching/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          coach_type: coachType,
          user_profile: userProfile,
          conversation_history: session.messages,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response body reader available');
      }

      let currentResponse = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData = JSON.parse(line.slice(6));
              
              switch (eventData.event) {
                case 'status':
                  console.log('Status:', eventData.data.message);
                  break;
                  
                case 'response_start':
                  console.log('Response starting...');
                  break;
                  
                case 'response_chunk':
                  currentResponse += eventData.data.chunk;
                  setSession(prev => ({
                    ...prev,
                    currentResponse,
                  }));
                  break;
                  
                case 'error':
                  throw new Error(eventData.data.message);
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', parseError);
            }
          }
        }
      }

      // Add final assistant message
      const assistantMessage: CoachingMessage = {
        role: 'assistant',
        content: currentResponse,
        timestamp: Date.now(),
      };

      setSession(prev => ({
        ...prev,
        messages: [...prev.messages, assistantMessage],
        isStreaming: false,
        currentResponse: '',
      }));

    } catch (error) {
      console.error('Streaming error:', error);
      setSession(prev => ({
        ...prev,
        isStreaming: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
    }
  };

  return {
    session,
    streamCoachingResponse,
  };
}
```

### React Component Example

```tsx
import React, { useState } from 'react';
import { useCoachingStream } from './useCoachingStream';

export function CoachingChat() {
  const [message, setMessage] = useState('');
  const [selectedCoach, setSelectedCoach] = useState('strength');
  const { session, streamCoachingResponse } = useCoachingStream();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    await streamCoachingResponse(message, selectedCoach, {
      fitness_level: 'intermediate',
      goals: ['build muscle', 'get stronger'],
    });

    setMessage('');
  };

  return (
    <div className="coaching-chat">
      <div className="messages">
        {session.messages.map((msg, index) => (
          <div key={index} className={`message ${msg.role}`}>
            <strong>{msg.role === 'user' ? 'You' : 'Coach'}:</strong>
            <p>{msg.content}</p>
          </div>
        ))}
        
        {session.isStreaming && (
          <div className="message assistant streaming">
            <strong>Coach:</strong>
            <p>{session.currentResponse}</p>
            <span className="typing-indicator">●●●</span>
          </div>
        )}
        
        {session.error && (
          <div className="error">
            Error: {session.error}
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit} className="message-form">
        <select 
          value={selectedCoach} 
          onChange={(e) => setSelectedCoach(e.target.value)}
        >
          <option value="strength">Strength Coach</option>
          <option value="cardio">Cardio Coach</option>
          <option value="cycling">Cycling Coach</option>
          <option value="nutrition">Nutrition Coach</option>
          <option value="recovery">Recovery Coach</option>
          <option value="mental">Mental Coach</option>
        </select>
        
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Ask your coach..."
          disabled={session.isStreaming}
        />
        
        <button type="submit" disabled={session.isStreaming}>
          {session.isStreaming ? 'Sending...' : 'Send'}
        </button>
      </form>
    </div>
  );
}
```

### JavaScript/Fetch API Example

```javascript
async function streamCoachingSession(message, coachType, userProfile = null) {
  const response = await fetch('/api/coaching/stream', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      message,
      coach_type: coachType,
      user_profile: userProfile,
    }),
  });

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let currentResponse = '';

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    const chunk = decoder.decode(value);
    const lines = chunk.split('\n');

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const eventData = JSON.parse(line.slice(6));
        
        if (eventData.event === 'response_chunk') {
          currentResponse += eventData.data.chunk;
          // Update UI with current response
          updateChatUI(currentResponse);
        }
      }
    }
  }

  return currentResponse;
}
```

## Error Handling

The API provides comprehensive error handling:

1. **Validation Errors** (400) - Invalid coach type or malformed request
2. **Server Errors** (500) - Internal processing errors
3. **Stream Errors** - Sent as error events in the stream

Always handle both HTTP errors and stream error events:

```typescript
try {
  // Handle HTTP errors
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  // Handle stream errors
  if (eventData.event === 'error') {
    throw new Error(eventData.data.message);
  }
} catch (error) {
  console.error('Coaching session error:', error);
  // Show user-friendly error message
}
```

## Best Practices

1. **Connection Management**: Always close SSE connections properly
2. **Error Recovery**: Implement retry logic for failed connections
3. **User Feedback**: Show loading states and typing indicators
4. **Message History**: Maintain conversation context for better coaching
5. **Profile Integration**: Pass user profile data for personalized responses
6. **Performance**: Limit conversation history to recent messages

## Testing

Use the provided test script to verify the API:

```bash
python test_streaming_api.py
```

This will test all endpoints and streaming functionality to ensure everything works correctly before frontend integration.

## CORS Configuration

The API includes CORS headers for frontend integration:
- `Access-Control-Allow-Origin: *`
- `Access-Control-Allow-Methods: GET, POST, OPTIONS`
- `Access-Control-Allow-Headers: Content-Type, Authorization`

For production, configure specific origins instead of using `*`. 