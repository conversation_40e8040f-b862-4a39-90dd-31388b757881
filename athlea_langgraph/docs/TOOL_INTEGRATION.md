# Tool Integration with Specialized Coaches

This document explains how tools are integrated with specialized coaching agents in the python-langgraph project, following the pattern established in the TypeScript implementation (athlea-web).

## Overview

The specialized coaches in the python-langgraph project now have access to domain-specific tools that enhance their capabilities. Each coach gets a curated set of tools based on their expertise area, allowing them to provide more accurate and actionable advice.

## Architecture

### Tool Management Pattern

The integration follows this architecture:

```
SpecializedCoachToolsManager
├── initialize_tools() - Lazy-loads all available tools
├── get_strength_coach_tools() - Returns tools for strength coaching
├── get_cardio_coach_tools() - Returns tools for cardio coaching
├── get_cycling_coach_tools() - Returns tools for cycling coaching
├── get_nutrition_coach_tools() - Returns tools for nutrition coaching
├── get_recovery_coach_tools() - Returns tools for recovery coaching
└── get_mental_coach_tools() - Returns tools for mental coaching
```

### Tool Distribution

Each specialized coach receives domain-specific tools:

#### 🏋️ Strength Coach
- **Airtable MCP Tools**: Exercise databases, program templates
- **Session Generation Tool**: Create structured strength workouts
- **Azure Search Tool**: Research evidence-based training methods

#### 🏃 Cardio Coach
- **Google Maps Elevation Tool**: Route elevation profiles
- **Azure Maps Tool**: Location and terrain analysis
- **Airtable MCP Tools**: Cardio programs and equipment databases
- **Session Generation Tool**: Create cardio workout sessions

#### 🚴 Cycling Coach
- **Google Maps Elevation Tool**: Cycling route analysis
- **Azure Maps Tool**: Cycling-specific route planning
- **Airtable MCP Tools**: Cycling equipment and program databases
- **Session Generation Tool**: Create cycling training sessions

#### 🥗 Nutrition Coach
- **Airtable MCP Tools**: Nutrition databases and meal plans
- **Azure Search Tool**: Nutritional research and evidence
- **Session Generation Tool**: Create nutrition plans and meal schedules

#### 😴 Recovery Coach
- **Airtable MCP Tools**: Recovery protocols and techniques
- **Session Generation Tool**: Create recovery and mobility sessions
- **Azure Search Tool**: Recovery research and methodologies

#### 🧠 Mental Coach
- **Airtable MCP Tools**: Mental training techniques and exercises
- **Session Generation Tool**: Create mental training programs
- **Azure Search Tool**: Sports psychology research

## Implementation Details

### 1. Tool Initialization

Tools are initialized lazily when first requested:

```python
# Tools manager handles initialization
tools_manager = await get_tools_manager()

# Tools are loaded on first access
strength_tools = tools_manager.get_strength_coach_tools()
```

### 2. Coach Integration

Each coach node automatically gets its domain-specific tools:

```python
async def strength_coach_node(state: AgentState, config: Optional[Dict[str, Any]] = None):
    """Strength coach node with specialized tools."""
    # Get tools for this coach
    tools_manager = await get_tools_manager()
    tools = tools_manager.get_strength_coach_tools()
    
    # Create LLM with tools bound
    coach_llm = create_azure_chat_openai(temperature=0.7, max_tokens=4000)
    coach_llm = coach_llm.bind_tools(tools)
    
    # ... rest of coach implementation
```

### 3. Tool Execution

When coaches make tool calls, the LLM can:
- Search Airtable databases for relevant information
- Get elevation data for route planning
- Generate structured workout sessions
- Retrieve research from Azure Search

## Available Tools

### Airtable MCP Tools
- **Purpose**: Access structured fitness data from Airtable
- **Tools**: `search_records`, `list_records`, `list_tables`, `list_bases`
- **Usage**: Exercise databases, program templates, equipment catalogs
- **Coaches**: All coaches (domain-specific data)

### Google Maps Elevation Tool
- **Purpose**: Get elevation data for route planning
- **Capabilities**: Point elevation, path profiles, bulk locations
- **Usage**: Cycling and running route analysis
- **Coaches**: Cardio, Cycling

### Azure Maps Tool
- **Purpose**: Advanced mapping and location services
- **Capabilities**: Route planning, terrain analysis, POI search
- **Usage**: Comprehensive route planning and analysis
- **Coaches**: Cardio, Cycling

### Session Generation Tool
- **Purpose**: Create structured workout and training sessions
- **Capabilities**: Workout planning, session templates, progression
- **Usage**: Generate specific training sessions
- **Coaches**: All coaches

### Azure Search Tool
- **Purpose**: Research and evidence-based information retrieval
- **Capabilities**: Search fitness research, studies, methodologies
- **Usage**: Evidence-based coaching recommendations
- **Coaches**: Strength, Nutrition, Recovery, Mental

## Comparison with TypeScript Implementation

### TypeScript Pattern (athlea-web)

```typescript
// Tools imported from exports.ts
import { strengthAssessmentTool, strengthProgramTool, ... } from "./exports";

// Domain-specific tool assignment
const strengthTools = [
  strengthAssessmentTool,
  strengthProgramTool,
  strengthEquipmentTool,
  airtableSearchRecordsTool, // If available
  // ...
];

// LLM binding
const llm_with_tools = llm.bind_tools(allToolsForLLMBinding);
```

### Python Implementation

```python
# Tools managed by SpecializedCoachToolsManager
class SpecializedCoachToolsManager:
    async def initialize_tools(self):
        self._airtable_tools = await create_airtable_mcp_tools()
        self._google_maps_tool = GoogleMapsElevationTool()
        # ...
    
    def get_strength_coach_tools(self) -> List[Any]:
        tools = []
        if self._airtable_tools:
            tools.extend(self._airtable_tools)
        # ...
        return tools

# Coach node with tools
async def strength_coach_node(state, config):
    tools_manager = await get_tools_manager()
    tools = tools_manager.get_strength_coach_tools()
    coach_llm = coach_llm.bind_tools(tools)
    # ...
```

## Usage Examples

### Running the Demo

```bash
cd python-langgraph
python demo_tool_integration.py
```

### Testing a Coach with Tools

```python
from athlea_langgraph.agents.specialized_coaches import strength_coach_node
from athlea_langgraph.state import AgentState
from langchain_core.messages import HumanMessage

# Create test state
state: AgentState = {
    "messages": [HumanMessage(content="Create a strength program for me")],
    "user_profile": {"experience_level": "beginner", "goals": ["muscle_gain"]},
    # ... other state fields
}

# Call the coach
result = await strength_coach_node(state)
```

### Adding New Tools

1. **Create the tool** in `athlea_langgraph/tools/external/`
2. **Add to SpecializedCoachToolsManager** initialization
3. **Assign to relevant coaches** in their `get_*_tools()` methods
4. **Update prompts** to mention new tool capabilities

## Error Handling

The system includes robust error handling:

- **Tool initialization failures**: Graceful degradation, coaches work without tools
- **Tool binding errors**: Warning logged, coach continues without problematic tools
- **Tool execution errors**: Handled by LangChain's tool framework

## Performance Considerations

- **Lazy loading**: Tools are only initialized when first needed
- **Caching**: Tools manager caches initialized tools
- **Selective binding**: Each coach only gets relevant tools to reduce token usage
- **Error resilience**: Failed tool initialization doesn't break the system

## Next Steps

1. **Start the Airtable MCP server** for full Airtable tool functionality
2. **Configure API keys** for Google Maps and Azure services
3. **Add more domain-specific tools** as needed
4. **Implement tool result processing** in coach nodes
5. **Add tool usage analytics** for monitoring and optimization

## Troubleshooting

### Common Issues

1. **"Airtable MCP tools not available"**
   - Start the Airtable MCP server
   - Check MCP server configuration
   - Verify Airtable API credentials

2. **"Failed to bind tools to coach"**
   - Check tool implementation for LangChain compatibility
   - Verify tool schema definitions
   - Check for circular imports

3. **"Tool not found in coach tools"**
   - Verify tool is added to correct coach's `get_*_tools()` method
   - Check tool initialization in `initialize_tools()`
   - Ensure tool module is properly imported

### Debug Mode

Enable debug logging to see tool initialization and usage:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
``` 