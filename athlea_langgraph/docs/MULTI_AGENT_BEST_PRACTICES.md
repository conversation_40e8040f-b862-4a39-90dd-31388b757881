 Oh it should be done come on and what we've just learned.# Multi-Agent System Best Practices Guide

## Overview

This guide outlines proven patterns for building reliable, maintainable, and scalable LLM-based "agentic" systems. The core principle is **modular architecture** where complex problems are decomposed into focused sub-agents, each operating within well-defined boundaries and responsibilities.

**Goal**: Build LLM-based "agentic" systems that reliably fetch data, reason about it, and take actions—without hidden single-point failures that "snowball" into production outages.

---

## 🏗️ Core Principle: Modularity

### Definition

**Modularity** means breaking a complex agentic system into:
- One **"parent" agent** whose sole job is **routing**: decide which child handles each request and when to exit
- Multiple **"child" agents** that are narrowly-focused, each with bounded context (single domain like returns, orders, nutrition)

### Why Monolithic Agents Fail

| Problem | Impact | Example |
|---------|--------|---------|
| **Large prompts and long tool lists** | Degrades LLM accuracy | Agent with 50+ tools struggles to pick the right one |
| **Blurred responsibilities** | Hard to trace errors or understand decision flows | Can't tell if bug is in nutrition logic or order processing |
| **Tight coupling** | Changes in one area break another | Updating return policy breaks order lookup |
| **Testing complexity** | Giant combined workflow is intractable | Can't isolate and test individual functions |

### Benefits of Modular Architecture

✅ **Predictable Decision Flows**: Parent hands off to one child; each step is clear and traceable  
✅ **Separation of Concerns**: Child agents focus only on their domain, reducing tangents and off-topic errors  
✅ **Reduced Complexity**: Smaller problem spaces per agent; fewer logical errors and easier reasoning  
✅ **Improved Maintainability**: Update or replace one child without impacting others  
✅ **Isolated Testing**: Unit-test each agent separately; integration-test just the routing  

---

## 🔧 Three Layers of an Agentic System

### Layer 1: Tool Layer
**Role**: Interfaces with external data (APIs, databases, vector stores, knowledge bases, user I/O)

| Common Pitfalls | Early Warning Signs | Mitigations |
|----------------|-------------------|-------------|
| **Stale or irrelevant data** from RAG engines | High "no-ops" (LLM queries without progress)<br>Low answer accuracy in dev tests | • Contract tests & schema validation (e.g. Zod)<br>• Relevance scoring metrics<br>• Mock responses in CI |
| **Slow or flaky endpoints** | Sporadic timeouts or long tail latency | • Timeouts/retries/circuit-breaker patterns<br>• Benchmark harness in staging |
| **Schema drift** when external APIs change | JSON parse errors; unexpected nulls | • Automated schema validation on every call<br>• Version function definitions |

### Layer 2: Reasoning Layer
**Role**: The LLM "brain" that decides which tools to call, how to decompose tasks, and when to exit

| Common Pitfalls | Early Warning Signs | Mitigations |
|----------------|-------------------|-------------|
| **Redundant loops** (same call repeated) | LLM calls same function twice | • Simulation tests with canned responses<br>• Step-limit enforcement |
| **Circular workflows** (A→B→A forever) | Infinite loops in staging | • Explicit exit functions<br>• Trace logs with loop detection |
| **Premature termination** | Incomplete workflows | • Modular sub-agents<br>• Detailed trace logging |

### Layer 3: Action Layer (Orchestration)
**Role**: Executes the LLM's function-call decisions, manages session state, and streams results (e.g. via SSE)

| Common Pitfalls | Early Warning Signs | Mitigations |
|----------------|-------------------|-------------|
| **Uncaught exceptions** crashing streams | Entire agent run crashes | • Try/catch around every call with structured error feedback |
| **Lost context** | LLM sees empty/truncated history | • Session objects for context persistence |
| **Resource leaks** | Gradual performance degradation | • Finally-blocks or "on close" hooks to release resources |

---

## 📚 Key Technical Definitions

### Core Concepts

**Function Calling**: The LLM emits structured "tool_call" objects (function name + JSON arguments) instead of plain text. The orchestration layer then invokes those functions.

**RAG (Retrieval-Augmented Generation)**: Retrieving relevant documents (via vector or keyword search) and feeding them into the LLM prompt to ground responses in up-to-date data.

**Circuit-Breaker Pattern**: Stops calling a failing service for a cooldown period after a threshold of errors, preventing cascading failures.

**Bounded Context**: From domain-driven design; each module or sub-agent has its own data model and responsibilities, minimizing coupling.

**Exit Function**: A no-args function exposed to the LLM that signals "I'm done," so the reasoning loop terminates cleanly.

### Advanced Concepts

**Batch (Parallel) Function Calls**: Instead of calling one tool, waiting for result, then calling next tool in separate LLM turn, let the LLM declare all needed functions in one shot and invoke them concurrently.

```python
# Instead of sequential:
result1 = await tool1.invoke(args1)  # Wait
result2 = await tool2.invoke(args2)  # Wait
result3 = await tool3.invoke(args3)  # Wait

# Use parallel:
results = await asyncio.gather(
    tool1.invoke(args1),
    tool2.invoke(args2), 
    tool3.invoke(args3)
)
```

**Chaos Testing**: Intentionally injecting failures (timeouts, exceptions) in staging to verify graceful degradation.

---

## 🎯 Design Principles

### 1. Modularity Implementation

**Parent Agent Pattern**:
```python
# Parent agent only routes - no domain logic
async def parent_router_node(state: AgentState) -> Dict[str, Any]:
    user_query = state.get("user_query", "")
    
    # Simple routing logic
    if "strength" in user_query.lower():
        return {"next_agent": "strength_coach"}
    elif "nutrition" in user_query.lower():
        return {"next_agent": "nutrition_coach"}
    else:
        return {"next_agent": "clarification"}
```

**Child Agent Pattern**:
```python
# Child agent focused on single domain
async def strength_coach_node(state: AgentState) -> Dict[str, Any]:
    # Only strength-related tools and logic
    assessment = await strength_assessment_tool.invoke(state.user_query)
    program = await strength_program_tool.invoke(assessment)
    
    return {
        "strength_response": program,
        "specialist_completed": True
    }
```

### 2. Observability

**Structured JSON Logs**:
```python
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

def log_agent_event(event_type: str, agent_name: str, **kwargs):
    log_data = {
        "timestamp": datetime.now().isoformat(),
        "event": event_type,
        "agent": agent_name,
        "request_id": kwargs.get("request_id"),
        "duration_ms": kwargs.get("duration_ms"),
        **kwargs
    }
    logger.info(json.dumps(log_data))

# Usage:
log_agent_event("agent_start", "strength_coach", request_id="req_123")
```

**Metrics Tracking**:
```python
from prometheus_client import Counter, Histogram

# Define metrics
agent_calls = Counter('agent_calls_total', 'Total agent calls', ['agent_name', 'status'])
agent_duration = Histogram('agent_duration_seconds', 'Agent execution time', ['agent_name'])

# Track in agent nodes
with agent_duration.labels(agent_name="strength_coach").time():
    try:
        result = await strength_coach_node(state)
        agent_calls.labels(agent_name="strength_coach", status="success").inc()
        return result
    except Exception as e:
        agent_calls.labels(agent_name="strength_coach", status="error").inc()
        raise
```

### 3. Testing Strategy

**Unit Tests for Agents**:
```python
import pytest
from unittest.mock import AsyncMock

@pytest.mark.asyncio
async def test_strength_coach_node():
    # Mock external tools
    mock_assessment_tool = AsyncMock(return_value="beginner level")
    mock_program_tool = AsyncMock(return_value="3x/week program")
    
    # Test state
    state = {"user_query": "I want to build muscle"}
    
    # Execute node
    result = await strength_coach_node(state)
    
    # Verify behavior
    assert result["strength_response"] == "3x/week program"
    assert result["specialist_completed"] is True
    mock_assessment_tool.assert_called_once()
```

**Integration Tests**:
```python
@pytest.mark.asyncio
async def test_full_coaching_flow():
    # Test complete parent → child → aggregation flow
    graph = await get_compiled_coaching_graph()
    
    result = await graph.ainvoke({
        "user_query": "I need a strength training plan",
        "user_profile": {"experience": "beginner"}
    })
    
    assert "strength_response" in result
    assert result["aggregated_response"] is not None
```

**Chaos Testing**:
```python
@pytest.mark.asyncio
async def test_tool_failure_resilience():
    # Simulate tool failures
    with patch('strength_assessment_tool.invoke', side_effect=TimeoutError()):
        result = await strength_coach_node(state)
        # Should return graceful error, not crash
        assert "error" in result["strength_response"].lower()
```

### 4. Performance & Scaling

**Connection Pooling**:
```python
# Use connection pools for external services
from motor.motor_asyncio import AsyncIOMotorClient

class DatabaseManager:
    def __init__(self):
        self._client = None
        self._pool_size = 50
    
    async def get_client(self):
        if not self._client:
            self._client = AsyncIOMotorClient(
                MONGODB_URI, 
                maxPoolSize=self._pool_size,
                minPoolSize=5
            )
        return self._client
```

**Caching Strategy**:
```python
from functools import lru_cache
import asyncio

class MemoryManager:
    def __init__(self):
        self._cache = {}
        self._ttl = {}
    
    async def get_cached_memories(self, user_id: str, query: str):
        cache_key = f"{user_id}:{hash(query)}"
        
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # Fetch from database
        memories = await self.fetch_memories(user_id, query)
        self._cache[cache_key] = memories
        
        return memories
```

### 5. Error Handling & Recovery

**Graceful Degradation**:
```python
async def nutrition_coach_with_fallback(state: AgentState) -> Dict[str, Any]:
    try:
        # Primary nutrition service
        detailed_plan = await nutrition_api_tool.invoke(state.user_query)
        return {"nutrition_response": detailed_plan}
        
    except Exception as primary_error:
        logger.warning(f"Primary nutrition service failed: {primary_error}")
        
        try:
            # Fallback to simpler tool
            basic_advice = await basic_nutrition_tool.invoke(state.user_query)
            return {
                "nutrition_response": f"Basic advice: {basic_advice}",
                "fallback_used": True
            }
        except Exception as fallback_error:
            logger.error(f"All nutrition services failed: {fallback_error}")
            return {
                "nutrition_response": "I'm having trouble accessing nutrition information right now. Please try again later.",
                "error": True
            }
```

**Circuit Breaker Implementation**:
```python
import asyncio
from enum import Enum
from typing import Callable, Any

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open" 
    HALF_OPEN = "half_open"

class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        return (
            self.last_failure_time and 
            asyncio.get_event_loop().time() - self.last_failure_time >= self.timeout
        )
    
    def _on_success(self):
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def _on_failure(self):
        self.failure_count += 1
        self.last_failure_time = asyncio.get_event_loop().time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
```

### 6. Security & Governance

**Input Sanitization**:
```python
import re
from typing import Dict, Any

def sanitize_user_input(user_query: str) -> str:
    # Remove potential injection attempts
    sanitized = re.sub(r'[<>\'\"\\]', '', user_query)
    
    # Limit length
    sanitized = sanitized[:1000]
    
    # Remove excessive whitespace
    sanitized = ' '.join(sanitized.split())
    
    return sanitized

async def reasoning_node_with_sanitization(state: AgentState) -> Dict[str, Any]:
    # Sanitize input before processing
    safe_query = sanitize_user_input(state.get("user_query", ""))
    
    # Update state with sanitized input
    state["user_query"] = safe_query
    
    return await reasoning_node(state)
```

**Access Controls**:
```python
class AgentPermissions:
    def __init__(self):
        self.agent_permissions = {
            "strength_coach": ["fitness_data", "user_profile"],
            "nutrition_coach": ["nutrition_data", "dietary_restrictions"],
            "head_coach": ["all_data"]  # Supervisor has broader access
        }
    
    def check_permission(self, agent_name: str, data_type: str) -> bool:
        allowed_data = self.agent_permissions.get(agent_name, [])
        return data_type in allowed_data or "all_data" in allowed_data

# Usage in agents
permissions = AgentPermissions()

async def strength_coach_node(state: AgentState) -> Dict[str, Any]:
    if not permissions.check_permission("strength_coach", "fitness_data"):
        raise PermissionError("Agent not authorized to access fitness data")
    
    # Proceed with authorized operations
    return await process_strength_request(state)
```

---

## 🚨 Preventing "Snowball" Problems

### 1. Contract-First Development

**Define Schemas Before Implementation**:
```python
from pydantic import BaseModel
from typing import Optional, List

# Define agent input/output contracts
class StrengthCoachInput(BaseModel):
    user_query: str
    user_profile: dict
    goals: List[str]

class StrengthCoachOutput(BaseModel):
    strength_response: str
    exercises: List[dict]
    error: Optional[str] = None

# Enforce contracts in agent implementations
async def strength_coach_node(state: AgentState) -> Dict[str, Any]:
    # Validate input
    try:
        input_data = StrengthCoachInput(**state)
    except ValidationError as e:
        return StrengthCoachOutput(
            strength_response="Invalid input provided",
            exercises=[],
            error=str(e)
        ).dict()
    
    # Process and validate output
    result = await process_strength_request(input_data)
    return StrengthCoachOutput(**result).dict()
```

### 2. CI Checks

**Automated Validation Pipeline**:
```python
# tests/test_contracts.py
import pytest
from athlea_langgraph.agents import *

def test_all_agents_have_required_exports():
    """Ensure all agent modules export required components"""
    agents = [strength_coach, nutrition_coach, cardio_coach]
    
    for agent in agents:
        assert hasattr(agent, 'node'), f"{agent} missing node function"
        assert hasattr(agent, 'tools'), f"{agent} missing tools list"
        assert hasattr(agent, 'InputSchema'), f"{agent} missing InputSchema"
        assert hasattr(agent, 'OutputSchema'), f"{agent} missing OutputSchema"

def test_schema_compatibility():
    """Verify agent schemas are compatible with state"""
    from athlea_langgraph.state import AgentState
    
    # Test each agent can parse from AgentState
    test_state = AgentState(
        user_query="test query",
        user_profile={"experience": "beginner"}
    )
    
    # Should not raise validation errors
    strength_input = StrengthCoachInput(**test_state.dict())
    nutrition_input = NutritionCoachInput(**test_state.dict())
```

### 3. Synthetic Transactions

**Automated End-to-End Testing**:
```python
# scripts/synthetic_health_check.py
import asyncio
import logging
from datetime import datetime

async def run_synthetic_coaching_session():
    """Run automated coaching sessions to detect issues early"""
    test_scenarios = [
        {
            "name": "strength_training_beginner",
            "query": "I'm new to strength training, help me start",
            "expected_agents": ["reasoning", "planning", "strength_coach", "aggregate_responses"]
        },
        {
            "name": "multi_domain_request", 
            "query": "I want a workout plan with nutrition advice",
            "expected_agents": ["strength_coach", "nutrition_coach", "aggregate_responses"]
        }
    ]
    
    results = []
    
    for scenario in test_scenarios:
        try:
            start_time = datetime.now()
            
            # Run coaching session
            graph = await get_compiled_coaching_graph()
            result = await graph.ainvoke({
                "user_query": scenario["query"],
                "user_profile": {"experience": "beginner"}
            })
            
            duration = (datetime.now() - start_time).total_seconds()
            
            # Validate expected behavior
            success = (
                result.get("aggregated_response") is not None and
                len(result.get("aggregated_response", "")) > 50 and
                duration < 30  # Should complete within 30 seconds
            )
            
            results.append({
                "scenario": scenario["name"],
                "success": success,
                "duration": duration,
                "error": None if success else "Failed validation"
            })
            
        except Exception as e:
            results.append({
                "scenario": scenario["name"], 
                "success": False,
                "duration": None,
                "error": str(e)
            })
    
    # Log results and alert on failures
    failed_scenarios = [r for r in results if not r["success"]]
    if failed_scenarios:
        logging.error(f"Synthetic test failures: {failed_scenarios}")
        # Could send alerts to monitoring system here
    
    return results

# Run in scheduled job (e.g., every hour)
if __name__ == "__main__":
    asyncio.run(run_synthetic_coaching_session())
```

### 4. Dashboard Alerts

**Key Metrics to Monitor**:
```python
# monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# Agent performance metrics
agent_execution_time = Histogram(
    'agent_execution_seconds',
    'Time spent in agent execution',
    ['agent_name', 'user_type']
)

agent_error_rate = Counter(
    'agent_errors_total',
    'Total agent errors',
    ['agent_name', 'error_type']
)

# LLM usage metrics
llm_token_usage = Counter(
    'llm_tokens_total',
    'Total LLM tokens consumed',
    ['model', 'agent']
)

# System health metrics
active_sessions = Gauge(
    'active_coaching_sessions',
    'Number of active coaching sessions'
)

loop_iterations = Histogram(
    'reasoning_loop_iterations',
    'Number of iterations in reasoning loops',
    ['success']
)

# Alert thresholds (configure in monitoring system)
ALERT_THRESHOLDS = {
    'agent_error_rate': 0.05,  # 5% error rate
    'avg_execution_time': 10.0,  # 10 seconds
    'max_loop_iterations': 15,  # Prevent infinite loops
    'token_usage_spike': 1.5  # 150% of baseline
}
```

---

## 🏗️ Modularization Recipe

### Step 1: Identify Bounded Contexts

**Map Your Domains**:
```python
# domains.py - Define clear boundaries
COACHING_DOMAINS = {
    "strength": {
        "responsibilities": ["exercise_selection", "program_design", "progression"],
        "tools": ["strength_assessment", "exercise_database", "program_generator"],
        "data_access": ["user_strength_history", "exercise_preferences"]
    },
    "nutrition": {
        "responsibilities": ["meal_planning", "dietary_analysis", "supplementation"],
        "tools": ["nutrition_database", "meal_planner", "calorie_calculator"],
        "data_access": ["dietary_restrictions", "nutrition_history"]
    },
    "recovery": {
        "responsibilities": ["sleep_optimization", "stress_management", "injury_prevention"],
        "tools": ["sleep_tracker", "stress_assessment", "mobility_planner"],
        "data_access": ["recovery_metrics", "sleep_data", "stress_indicators"]
    }
}
```

### Step 2: Create Dedicated Agent Modules

**File Structure**:
```
agents/
├── __init__.py
├── base_agent.py              # Common agent interface
├── strength_agent.py          # Strength coaching logic
├── nutrition_agent.py         # Nutrition coaching logic  
├── recovery_agent.py          # Recovery coaching logic
├── parent_router.py           # Main routing logic
└── aggregation_agent.py       # Response synthesis
```

**Base Agent Pattern**:
```python
# agents/base_agent.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List
from pydantic import BaseModel

class BaseAgent(ABC):
    """Base class for all coaching agents"""
    
    def __init__(self, name: str, tools: List[Any]):
        self.name = name
        self.tools = tools
        self.permissions = self._get_permissions()
    
    @abstractmethod
    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process agent-specific logic"""
        pass
    
    @abstractmethod
    def _get_permissions(self) -> List[str]:
        """Define what data this agent can access"""
        pass
    
    def validate_input(self, state: Dict[str, Any]) -> bool:
        """Validate input meets agent requirements"""
        return "user_query" in state
```

**Concrete Agent Implementation**:
```python
# agents/strength_agent.py
from .base_agent import BaseAgent
from ..tools.strength_tools import StrengthAssessmentTool, ProgramGeneratorTool

class StrengthAgent(BaseAgent):
    def __init__(self):
        tools = [StrengthAssessmentTool(), ProgramGeneratorTool()]
        super().__init__("strength_coach", tools)
    
    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Handle strength training requests"""
        
        # Validate permissions
        if not self._check_permissions(state):
            return {"error": "Insufficient permissions"}
        
        # Extract strength-specific context
        user_query = state.get("user_query", "")
        experience = state.get("user_profile", {}).get("experience", "beginner")
        
        # Use domain-specific tools
        assessment = await self.tools[0].invoke({
            "query": user_query,
            "experience": experience
        })
        
        program = await self.tools[1].invoke({
            "assessment": assessment,
            "preferences": state.get("preferences", {})
        })
        
        return {
            "strength_response": program,
            "assessment_data": assessment,
            "specialist_completed": True
        }
    
    def _get_permissions(self) -> List[str]:
        return ["fitness_data", "user_profile", "exercise_preferences"]
    
    def _check_permissions(self, state: Dict[str, Any]) -> bool:
        # Implement permission checking logic
        return True  # Simplified for example

# Export for use in graph
strength_agent = StrengthAgent()
```

### Step 3: Implement Parent Router

```python
# agents/parent_router.py
from typing import Dict, Any, Optional
from .strength_agent import strength_agent
from .nutrition_agent import nutrition_agent
from .recovery_agent import recovery_agent

class ParentRouter:
    def __init__(self):
        self.agents = {
            "strength_coach": strength_agent,
            "nutrition_coach": nutrition_agent, 
            "recovery_coach": recovery_agent
        }
        self.routing_history = []
    
    async def route(self, state: Dict[str, Any]) -> str:
        """Determine which agent should handle the request"""
        
        user_query = state.get("user_query", "").lower()
        completed_specialists = state.get("completed_specialists", [])
        
        # Simple keyword-based routing (can be enhanced with ML)
        routing_rules = {
            "strength_coach": ["strength", "muscle", "lifting", "weights", "exercise"],
            "nutrition_coach": ["nutrition", "diet", "meal", "food", "calories"],
            "recovery_coach": ["recovery", "sleep", "rest", "stress", "injury"]
        }
        
        # Find matching agents that haven't been used yet
        candidates = []
        for agent_name, keywords in routing_rules.items():
            if (agent_name not in completed_specialists and 
                any(keyword in user_query for keyword in keywords)):
                candidates.append(agent_name)
        
        # Default routing logic
        if candidates:
            next_agent = candidates[0]  # Take first match
            self.routing_history.append(next_agent)
            return next_agent
        elif not completed_specialists:
            # No matches, route to general strength coach as default
            return "strength_coach"
        else:
            # All relevant specialists completed
            return "aggregate_responses"

parent_router = ParentRouter()
```

---

## 🔧 Cursor Rules & Linting

### In-Code Comment Blocks

Place this at the top of agent files to guide AI IDEs:

```python
"""
─────────── CURSOR RULES ───────────
1. PARENT vs CHILD AGENTS
   • Parent modules (routers) may only import agent classes from /agents
   • Child modules (agents) may only import tools from /tools
   • No cross-agent imports (strength_agent cannot import nutrition_agent)

2. NAMING CONVENTIONS
   • Every agent file must export: <AgentName>Agent class
   • Agent classes must inherit from BaseAgent
   • Tool files must export: <ToolName>Tool class

3. SCHEMA VALIDATION
   • Each agent must declare InputSchema and OutputSchema using Pydantic
   • All state mutations must be validated against schemas
   • No direct dict manipulation without validation

4. TESTING REQUIREMENTS
   • Agent tests live in tests/agents/<agent_name>_test.py
   • Must mock all external tool calls
   • Must verify input/output schema compliance

5. ERROR HANDLING
   • All agent methods must handle exceptions gracefully
   • Return structured error objects, never raise in agent logic
   • Log errors with structured context

6. PERFORMANCE
   • Use async/await for all I/O operations
   • Implement timeouts for external calls
   • Cache expensive computations with TTL
─────────────────────────────────────
"""
```

### ESLint/Ruff Configuration

```python
# pyproject.toml additions
[tool.ruff]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings  
    "F",  # pyflakes
    "I",  # isort
    "N",  # pep8-naming
    "UP", # pyupgrade
]

[tool.ruff.per-file-ignores]
# Allow broader imports in __init__.py files
"__init__.py" = ["F401"]

# Custom rules for agent architecture
[tool.ruff.flake8-import-conventions.aliases]
# Enforce consistent imports
"athlea_langgraph.agents" = "agents"
"athlea_langgraph.tools" = "tools"

# Custom plugin configuration for architectural rules
[tool.custom-rules]
# Prevent cross-layer imports
forbidden-imports = [
    {
        pattern = "agents/*.py",
        forbidden = ["graphs.*", "other_agents.*"],
        message = "Agent modules must not import graphs or other agents"
    },
    {
        pattern = "graphs/*.py", 
        forbidden = ["tools.*"],
        message = "Graph modules must import agents, not tools directly"
    }
]
```

### Pre-commit Hooks

```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: agent-architecture-check
        name: Agent Architecture Validation
        entry: python scripts/validate_architecture.py
        language: python
        pass_filenames: false
        always_run: true
        
      - id: schema-validation
        name: Validate Agent Schemas
        entry: python scripts/validate_schemas.py
        language: python
        files: agents/.*\.py$
        
      - id: test-coverage-agents
        name: Ensure Agent Test Coverage
        entry: python scripts/check_agent_tests.py
        language: python
        files: agents/.*\.py$
```

---

## 📋 Implementation Checklist

### ✅ Modularization Checklist

- [ ] **Map domains**: Identify all sub-agents needed
- [ ] **Define schemas**: Create Pydantic models for each agent's I/O
- [ ] **Implement agents**: Build each agent in separate module  
- [ ] **Create router**: Build lightweight parent dispatcher
- [ ] **Add observability**: Log all agent transitions
- [ ] **Write tests**: Unit tests for each agent + integration tests
- [ ] **Performance baseline**: Measure before/after modularization

### ✅ Three-Layer Hardening Checklist

**Tool Layer**:
- [ ] Schema validation on all external responses
- [ ] Timeout/retry/circuit-breaker for all external calls
- [ ] Mock external dependencies in tests
- [ ] Metrics for tool performance and error rates

**Reasoning Layer**:
- [ ] Step-limit enforcement (max iterations)
- [ ] Loop detection and prevention
- [ ] Explicit exit functions for all workflows
- [ ] Simulation tests with canned tool responses

**Action Layer**:
- [ ] Try/catch around all external calls
- [ ] Session state management
- [ ] Resource cleanup (connections, files)
- [ ] Structured error responses

### ✅ Production Readiness Checklist

- [ ] **Monitoring**: Prometheus metrics + structured logging
- [ ] **Alerting**: Dashboard alerts for key thresholds
- [ ] **Testing**: Unit + integration + chaos tests
- [ ] **Documentation**: API contracts + runbooks
- [ ] **Security**: Input sanitization + access controls
- [ ] **Performance**: Load testing + optimization
- [ ] **Deployment**: Blue/green deployment strategy

---

## 🚀 Next Steps

1. **Start Small**: Pick one domain (e.g., strength coaching) and modularize it first
2. **Measure Impact**: Compare response quality and system reliability before/after
3. **Iterate**: Gradually break down remaining monolithic components
4. **Scale**: Add new domains as separate agents without touching existing code
5. **Optimize**: Use metrics to identify bottlenecks and optimization opportunities

---

**Bottom Line**: By treating each layer—Tool, Reasoning, Action—as a separate subsystem with its own contracts, tests, and observability, you prevent small glitches from cascading into production outages. Modularity + rigorous testing + structured monitoring = robust agents that hold up under real-world pressure. 