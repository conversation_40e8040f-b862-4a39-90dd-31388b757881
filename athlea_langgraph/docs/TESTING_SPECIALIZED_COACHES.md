# Testing Specialized Coaches with Memory-Enhanced Coaching

This guide explains how to test the specialized coaches functionality integrated with the memory-enhanced coaching system.

## Overview

The specialized coaches system provides domain-specific expertise across different fitness areas:

- **Strength Coach**: Resistance training, powerlifting, bodybuilding
- **Running Coach**: Running training, endurance, race preparation  
- **Cardio Coach**: Cardiovascular fitness, HIIT, aerobic training
- **Cycling Coach**: Road cycling, mountain biking, bike fitting
- **Nutrition Coach**: Sports nutrition, meal planning, dietary optimization
- **Recovery Coach**: Rest, regeneration, injury prevention
- **Mental Coach**: Sports psychology, motivation, habit formation

Each coach has access to specialized tools and provides expert advice in their domain while maintaining memory and personalization across sessions.

## Prerequisites

1. **MongoDB Database**: Set up a MongoDB instance for memory storage
2. **Environment Variables**: Configure required API keys
3. **Dependencies**: Install all required packages

### Required Environment Variables

```bash
# MongoDB for memory storage (required)
export MONGODB_URI="mongodb://localhost:27017/athlea_coaching"

# Optional: API keys for enhanced tool functionality
export AIRTABLE_API_KEY="your_airtable_api_key"
export GOOGLE_MAPS_API_KEY="your_google_maps_api_key"
export AZURE_MAPS_API_KEY="your_azure_maps_api_key"
export AZURE_SEARCH_ENDPOINT="your_azure_search_endpoint"
export AZURE_SEARCH_API_KEY="your_azure_search_api_key"
export AZURE_OPENAI_API_KEY="your_azure_openai_api_key"
export AZURE_OPENAI_ENDPOINT="your_azure_openai_endpoint"
```

### Install Dependencies

```bash
cd python-langgraph
pip install -r requirements.txt
# or if using poetry
poetry install
```

## Test Scripts

### 1. Quick Specialist Test

Run a quick test to verify specialists are working:

```bash
python test_memory_coaching_with_specialists.py
```

This script:
- Tests individual coach imports
- Verifies tools manager initialization
- Checks tool availability
- Runs the original memory coaching demo
- Demonstrates specialist routing
- Shows cross-session memory with specialists

### 2. Comprehensive Test Suite

Run the full test suite:

```bash
python test_specialized_coaches.py
```

This comprehensive test includes:
- Individual specialized coach node testing
- Tool integration verification
- Memory-enhanced coaching with specialist routing
- Cross-session memory persistence
- Personalized responses validation

### 3. Original Memory Coaching Demo

Run the original memory coaching example:

```bash
python -m athlea_langgraph.example_memory_coaching
```

## What to Expect

### Successful Test Output

When tests run successfully, you should see:

```
🔧 Quick Specialist Test
==============================
✅ Specialist coaches imported successfully
✅ Tools manager initialized
✅ Strength coach tools: 3 available
✅ Nutrition coach tools: 3 available
✅ Quick test passed - specialists are ready!

🏃‍♂️ Memory Coaching with Specialized Coaches Demo
============================================================

1️⃣ Running Original Memory Features Demo
----------------------------------------
🏃‍♂️ Memory-Enhanced Coaching Demo
==================================================
✓ Demo profile created for demo_user_001

📝 Conversation 1: First interaction - system should load user profile
User: Hi! I want to start a new workout routine. I've been sedentary for a while.
----------------------------------------
🤖 Coach Response:
Hello! I'm excited to help you start your fitness journey...
👥 Specialists consulted: strength, nutrition
🧠 Memory Context: 1 relevant memories found
```

### Tool Integration

Each specialist coach has access to domain-specific tools:

- **Strength Coach**: Airtable exercise databases, session generation, Azure search
- **Cardio/Running Coach**: Google Maps elevation, Azure Maps, Airtable programs
- **Cycling Coach**: Elevation data, route planning, cycling equipment databases
- **Nutrition Coach**: Nutrition databases, meal planning, research tools
- **Recovery Coach**: Recovery protocols, session generation, research
- **Mental Coach**: Mental training techniques, habit formation tools

### Memory Integration

The system demonstrates:

1. **User Profile Persistence**: User information saved across sessions
2. **Contextual Memory**: Relevant memories retrieved for each conversation
3. **Interaction History**: Previous conversations inform current responses
4. **Specialist Continuity**: Different specialists maintain context
5. **Cross-Session Learning**: System remembers and builds on previous sessions

## Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   ```
   ❌ Error: MONGODB_URI environment variable not set
   ```
   **Solution**: Set the MONGODB_URI environment variable

2. **Tool Initialization Warnings**
   ```
   ⚠ Warning: Some tools failed to initialize
   ```
   **Solution**: Check API keys for external services (optional for basic functionality)

3. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'athlea_langgraph'
   ```
   **Solution**: Ensure you're running from the correct directory and dependencies are installed

4. **Azure OpenAI Errors**
   ```
   Error: Azure OpenAI credentials not configured
   ```
   **Solution**: Set Azure OpenAI environment variables

### Debug Mode

For more detailed logging, set the log level:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Understanding the Output

### Specialist Routing

The system should route queries to appropriate specialists:

- Strength training questions → Strength Coach
- Running/cardio questions → Running/Cardio Coach
- Nutrition questions → Nutrition Coach
- Recovery questions → Recovery Coach
- Motivation questions → Mental Coach

### Memory Context

Each response shows:
- Number of relevant memories found
- Which specialists were consulted
- Memory integration in responses

### Tool Usage

When tools are available, coaches may make tool calls:
- Airtable searches for exercise/nutrition data
- Maps tools for route planning
- Session generation for workout creation

## Next Steps

After successful testing:

1. **Integrate with your application**: Use the coaching graph in your app
2. **Customize specialists**: Modify prompts and tools for your use case
3. **Add new specialists**: Create additional domain experts
4. **Enhance memory**: Add more sophisticated memory patterns
5. **Scale tools**: Add more external API integrations

## Support

If you encounter issues:

1. Check the logs for detailed error messages
2. Verify all environment variables are set
3. Ensure MongoDB is running and accessible
4. Test individual components separately
5. Review the specialized_coaches.py implementation

The specialized coaches system provides a robust foundation for domain-specific AI coaching with memory and personalization capabilities. 