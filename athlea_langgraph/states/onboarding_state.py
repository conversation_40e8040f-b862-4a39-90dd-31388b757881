import uuid
from datetime import datetime
from typing import Any, Dict, List, Literal, Optional, Union

from langchain_core.messages import BaseMessage, AIMessage, HumanMessage
from pydantic import BaseModel, Field
from typing_extensions import Annotated, TypedDict
from langgraph.graph import add_messages, MessagesState

# Define the possible stages of the onboarding process
OnboardingStage = Literal[
    "initial",
    "greeting",
    "collecting_goals",
    "summarizing_history",
    "generating_plan",
    "presenting_plan",
    "complete",
    "error",
]

# Define the fields we need to collect
OnboardingField = Literal["goal", "experienceLevel", "timeCommitment", "equipment"]


class UserGoals(BaseModel):
    """Structure for user goals - matches frontend UserGoals interface"""

    exists: bool = Field(
        default=False, description="Flag indicating if goals have been captured"
    )
    list: List[str] = Field(
        default_factory=list, description="List of user's fitness goals"
    )


class SummaryItem(BaseModel):
    """Structure for user history summary items - matches frontend SummaryItem interface"""

    category: str = Field(
        description="Category of information (e.g., 'Training Frequency', 'Dietary Habits')"
    )
    details: str = Field(description="The summarized detail")
    isImportant: bool = Field(
        default=False, description="Flag to highlight important information"
    )


class SportSuggestion(BaseModel):
    """Structure for sport suggestions"""

    label: str = Field(description="Display label for the sport")
    value: str = Field(description="Value identifier for the sport")


# --- Simplified Training Plan Schema to match frontend's PlanDetails ---


class DomainMetrics(BaseModel):
    techniques_used: List[str]
    resources: List[str]
    primary_metrics: List[str]
    average_weekly_time: str
    sessions_per_week: Union[int, str]


class DisciplineSpecifics(BaseModel):
    specificDetails: DomainMetrics


class PlanPhase(BaseModel):
    phaseName: str
    duration: str
    weeks: str
    focus: str
    description: str
    rationale: str
    skillsTraining: Optional[str] = None
    fitnessTraining: Optional[str] = None
    strengthTraining: Optional[str] = None
    mindsetTraining: Optional[str] = None
    nutritionRecommendations: Optional[str] = None
    recoveryRecommendations: Optional[str] = None
    resources: Optional[str] = None
    disciplineSpecifics: Dict[
        Literal["Running", "Cycling", "Strength", "Recovery", "Nutrition"],
        Optional[DisciplineSpecifics],
    ]


class ExampleSession(BaseModel):
    SessionName: str
    sessionType: str
    Duration: str
    SessionDescription: str


class PlanDetails(BaseModel):
    planId: str
    name: str
    description: str
    duration: str
    gender: Optional[str] = None
    level: Optional[str] = None
    ageGroup: Optional[str] = None
    planType: Literal["Running", "Cycling", "Strength", "Nutrition", "Recovery"]
    disciplines: List[
        Literal["Running", "Cycling", "Strength", "Nutrition", "Recovery"]
    ]
    weeklyVolume: Optional[str] = None
    rationale: str
    phases: List[PlanPhase]
    exampleSessions: Optional[List[ExampleSession]] = None


class SidebarStateData(BaseModel):
    """Sidebar state for tracking progress - matches frontend SidebarStateData interface"""

    current_stage: str = Field(default="initial")
    goals: UserGoals = Field(default_factory=UserGoals)
    summary_items: Optional[List[SummaryItem]] = Field(default_factory=list)
    generated_plan: Optional[Union[PlanDetails, Dict[str, Any]]] = Field(default=None)
    sport_suggestions: Optional[List[SportSuggestion]] = Field(default=None)
    selected_sport: Optional[str] = Field(default=None)
    selected_sports: List[str] = Field(default_factory=list)
    weekly_plan: Optional[Dict[str, Any]] = Field(
        default=None
    )  # For session generation
    uploaded_documents: Optional[List[Dict[str, Any]]] = Field(
        default_factory=list,
        description="List of uploaded document metadata for display in sidebar",
    )
    key_insights: Optional[Dict[str, str]] = Field(
        default_factory=dict,
        description="Key insights extracted from uploaded files as key-value pairs",
    )

    class Config:
        extra = "allow"  # Allow additional fields to be set dynamically


class OnboardingState(MessagesState):
    """
    Main onboarding state interface - matches frontend OnboardingState

    IMPORTANT: This state extends MessagesState to enable Chat Mode support in LangGraph Studio.
    Chat mode is only supported for graphs whose state includes or extends MessagesState.
    """

    # Core fields - messages inherited from MessagesState
    # messages: Annotated[List[BaseMessage], add_messages] - inherited from MessagesState

    user_id: str

    # Conversation history from MongoDB for routing decisions
    conversation_history: Optional[List[Dict[str, Any]]] = Field(
        default_factory=list,
        description="Full conversation history loaded from MongoDB for routing decisions",
    )

    # Onboarding specific fields
    current_question_field: Optional[OnboardingField]
    onboarding_stage: OnboardingStage

    # User profile information
    goal: Optional[str]
    experience_level: Optional[str]
    time_commitment: Optional[str]
    equipment: Optional[str]

    # Tracking for sidebar UI
    sidebar_data: Optional[SidebarStateData]

    # Control flags - match frontend exactly
    has_enough_info: bool
    needs_input: bool
    requires_input: bool
    input_prompt: Optional[str]
    current_task_description: Optional[str]

    # Generated content
    generated_plan: Optional[Union[PlanDetails, Dict[str, Any]]]
    sport_suggestions: Optional[List[SportSuggestion]]

    # User interaction
    user_input: Optional[str]
    resume_value: Optional[str]
    user_name: Optional[str]

    # System fields
    error: Optional[str]
    info_gathered: bool
    pending_goal_clarification: bool
    system_prompt: str

    # LangGraph internal field for interrupt handling
    _langgraph_interrupt: Optional[Any]


def create_initial_onboarding_state(user_id: str) -> OnboardingState:
    """Helper function to create initial onboarding state - matches frontend logic"""
    return OnboardingState(
        messages=[],
        user_id=user_id,
        conversation_history=[],
        current_question_field=None,
        onboarding_stage="initial",
        goal=None,
        experience_level=None,
        time_commitment=None,
        equipment=None,
        sidebar_data=SidebarStateData(),
        has_enough_info=False,
        needs_input=False,
        requires_input=False,
        input_prompt=None,
        current_task_description=None,
        generated_plan=None,
        sport_suggestions=None,
        user_input=None,
        resume_value=None,
        user_name=None,
        error=None,
        info_gathered=False,
        pending_goal_clarification=False,
        system_prompt="",
        _langgraph_interrupt=None,
    )


# Common sport suggestions - match frontend exactly
COMMON_SPORT_SUGGESTIONS = [
    SportSuggestion(label="🏃 Running", value="Running"),
    SportSuggestion(label="🚲 Cycling", value="Cycling"),
    SportSuggestion(label="🏋️ Strength", value="Strength Training"),
    SportSuggestion(label="🧘 General Fitness", value="General Fitness"),
    SportSuggestion(label="🏊 Swimming", value="Swimming"),
    SportSuggestion(label="🎾 Tennis", value="Tennis"),
]

# Valid sport values for quick lookup
VALID_SPORT_VALUES = {suggestion.value for suggestion in COMMON_SPORT_SUGGESTIONS}


# Utility functions for state manipulation - match frontend logic
def add_human_message(state: OnboardingState, content: str) -> OnboardingState:
    """Add human message to state"""
    new_state = state.copy()
    new_state["messages"] = state["messages"] + [HumanMessage(content=content)]
    new_state["user_input"] = content
    new_state["resume_value"] = None
    return new_state


def add_ai_message(state: OnboardingState, content: str) -> OnboardingState:
    """Add AI message to state"""
    new_state = state.copy()
    new_state["messages"] = state["messages"] + [AIMessage(content=content)]
    return new_state


def set_goals(state: OnboardingState, goals: List[str]) -> OnboardingState:
    """Set goals in sidebar data"""
    new_state = state.copy()
    current_sidebar = state.get("sidebar_data") or SidebarStateData()

    # Update sidebar data
    updated_sidebar = SidebarStateData(
        current_stage=current_sidebar.current_stage,
        goals=UserGoals(exists=len(goals) > 0, list=goals),
        summary_items=current_sidebar.summary_items,
        generated_plan=current_sidebar.generated_plan,
        sport_suggestions=current_sidebar.sport_suggestions,
        selected_sport=current_sidebar.selected_sport,
        selected_sports=current_sidebar.selected_sports,
    )

    new_state["sidebar_data"] = updated_sidebar
    return new_state


def add_summary_item(state: OnboardingState, item: SummaryItem) -> OnboardingState:
    """Add summary item to sidebar data"""
    new_state = state.copy()
    current_sidebar = state.get("sidebar_data") or SidebarStateData()
    current_items = current_sidebar.summary_items or []

    # Update sidebar data
    updated_sidebar = SidebarStateData(
        current_stage=current_sidebar.current_stage,
        goals=current_sidebar.goals,
        summary_items=current_items + [item],
        generated_plan=current_sidebar.generated_plan,
        sport_suggestions=current_sidebar.sport_suggestions,
        selected_sport=current_sidebar.selected_sport,
        selected_sports=current_sidebar.selected_sports,
    )

    new_state["sidebar_data"] = updated_sidebar
    return new_state


def add_selected_sport(state: OnboardingState, sport_value: str) -> OnboardingState:
    """Add selected sport to sidebar data"""
    new_state = state.copy()
    current_sidebar = state.get("sidebar_data") or SidebarStateData()
    current_sports = current_sidebar.selected_sports or []

    # Avoid duplicates
    if sport_value not in current_sports:
        updated_sports = current_sports + [sport_value]
    else:
        updated_sports = current_sports

    # Update sidebar data
    updated_sidebar = SidebarStateData(
        current_stage=current_sidebar.current_stage,
        goals=current_sidebar.goals,
        summary_items=current_sidebar.summary_items,
        generated_plan=current_sidebar.generated_plan,
        sport_suggestions=current_sidebar.sport_suggestions,
        selected_sport=sport_value,  # Set as current selected sport
        selected_sports=updated_sports,
    )

    new_state["sidebar_data"] = updated_sidebar
    return new_state
