"""
ReWOO (Reasoning Without Observation) state definition for Athlea coaching system.

Implements the Plan-Worker-Solver pattern for parallel agent coordination
without observation loops, enabling efficient multi-domain coaching responses.
"""

from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.messages import BaseMessage
from typing_extensions import TypedDict

from .state import AgentState, messages_reducer


class Task(TypedDict):
    """
    Individual task definition for ReWOO workers.

    Represents a specific coaching task to be executed by a worker agent.
    """

    task_id: str
    task_type: str  # "strength", "nutrition", "cardio", "recovery", "mental", "cycling"
    description: str
    dependencies: List[str]  # Task IDs that must complete first
    worker_agent: str
    priority: int  # 1-10, higher is more important
    estimated_duration: Optional[int]  # seconds


class WorkerResult(TypedDict):
    """
    Result from a ReWOO worker agent execution.

    Contains the worker's output and metadata for synthesis.
    """

    task_id: str
    worker_agent: str
    success: bool
    result: str
    confidence_score: float  # 0.0-1.0
    execution_time: float  # seconds
    tools_used: List[str]
    warnings: List[str]
    error_message: Optional[str]


class ExecutionPlan(TypedDict):
    """
    Complete execution plan generated by the ReWOO planner.

    Defines how to coordinate multiple coaching agents in parallel.
    """

    plan_id: str
    query_analysis: str
    required_domains: List[str]
    tasks: List[Task]
    execution_strategy: str  # "parallel", "sequential", "hybrid"
    estimated_total_time: int  # seconds
    confidence: float  # 0.0-1.0


class ReWOOState(TypedDict):
    """
    Enhanced AgentState with ReWOO parallel coordination capabilities.

    Extends base coaching state with Plan-Worker-Solver workflow support
    for sophisticated multi-agent reasoning without observation loops.
    """

    # Core AgentState fields with proper annotations
    messages: Annotated[List[BaseMessage], messages_reducer]
    user_query: Optional[str]
    user_profile: Optional[Dict[str, Any]]
    routing_decision: Optional[Union[str, List[str]]]
    pending_agents: Optional[List[str]]
    plan: Optional[List[str]]
    current_step: Optional[int]
    domain_contributions: Dict[str, str]
    required_domains: List[str]
    completed_domains: List[str]
    aggregated_plan: Optional[str]
    proceed_to_generation: bool

    # ReWOO-specific fields
    rewoo_mode: bool  # Whether ReWOO is active for this session
    execution_plan: Optional[ExecutionPlan]
    active_tasks: List[Task]
    worker_results: Dict[str, WorkerResult]  # task_id -> result
    parallel_execution_status: Dict[
        str, str
    ]  # task_id -> "pending"|"running"|"completed"|"failed"
    synthesis_reasoning: Optional[str]
    coordination_metadata: Dict[str, Any]

    # Performance tracking
    planning_start_time: Optional[float]
    execution_start_time: Optional[float]
    synthesis_start_time: Optional[float]
    total_execution_time: Optional[float]

    # Integration with Phase 1 reflection
    reflection_enabled: bool
    post_rewoo_reflection: bool  # Apply reflection to final synthesized response


class ReWOOConfig(TypedDict):
    """
    Configuration for ReWOO execution behavior.

    Controls how the Plan-Worker-Solver pattern operates.
    """

    max_parallel_workers: int  # Maximum concurrent worker agents
    task_timeout: int  # Seconds before worker task times out
    enable_task_dependencies: bool  # Whether to respect task dependencies
    synthesis_strategy: str  # "comprehensive", "prioritized", "domain_weighted"
    fallback_to_sequential: bool  # Fall back if parallel execution fails
    reflection_integration: bool  # Apply Phase 1 reflection to final output
    quality_threshold: float  # Minimum confidence for worker results
    enable_caching: bool  # Cache execution plans for similar queries


def create_initial_rewoo_state(
    user_query: str,
    user_profile: Optional[Dict[str, Any]] = None,
    config: Optional[ReWOOConfig] = None,
) -> ReWOOState:
    """
    Create initial ReWOO state for a new coordination session.

    Args:
        user_query: User's coaching request
        user_profile: User profile and preferences
        config: ReWOO configuration settings

    Returns:
        Initial ReWOOState with default values
    """

    return ReWOOState(
        {
            # Core state
            "messages": [],
            "user_query": user_query,
            "user_profile": user_profile or {},
            "routing_decision": None,
            "pending_agents": [],
            "plan": [],
            "current_step": 0,
            "domain_contributions": {},
            "required_domains": [],
            "completed_domains": [],
            "aggregated_plan": None,
            "proceed_to_generation": False,
            # ReWOO fields
            "rewoo_mode": True,
            "execution_plan": None,
            "active_tasks": [],
            "worker_results": {},
            "parallel_execution_status": {},
            "synthesis_reasoning": None,
            "coordination_metadata": {
                "config": config or create_default_rewoo_config(),
                "session_id": None,
                "created_at": None,
            },
            # Performance tracking
            "planning_start_time": None,
            "execution_start_time": None,
            "synthesis_start_time": None,
            "total_execution_time": None,
            # Reflection integration
            "reflection_enabled": (
                config.get("reflection_integration", False) if config else False
            ),
            "post_rewoo_reflection": False,
        }
    )


def create_default_rewoo_config() -> ReWOOConfig:
    """
    Create default ReWOO configuration for standard coaching scenarios.

    Returns:
        ReWOOConfig with production-ready defaults
    """

    return ReWOOConfig(
        {
            "max_parallel_workers": 4,  # Strength, nutrition, cardio, recovery
            "task_timeout": 30,  # 30 seconds per task
            "enable_task_dependencies": True,
            "synthesis_strategy": "comprehensive",
            "fallback_to_sequential": True,
            "reflection_integration": True,  # Use Phase 1 reflection
            "quality_threshold": 0.7,
            "enable_caching": True,
        }
    )


def enhance_state_with_rewoo(
    base_state: Dict[str, Any], config: Optional[ReWOOConfig] = None
) -> ReWOOState:
    """
    Enhance existing AgentState with ReWOO capabilities.

    Args:
        base_state: Existing agent state
        config: ReWOO configuration

    Returns:
        Enhanced state with ReWOO fields
    """

    rewoo_fields = {
        "rewoo_mode": True,
        "execution_plan": None,
        "active_tasks": [],
        "worker_results": {},
        "parallel_execution_status": {},
        "synthesis_reasoning": None,
        "coordination_metadata": {
            "config": config or create_default_rewoo_config(),
        },
        "planning_start_time": None,
        "execution_start_time": None,
        "synthesis_start_time": None,
        "total_execution_time": None,
        "reflection_enabled": (
            config.get("reflection_integration", False) if config else False
        ),
        "post_rewoo_reflection": False,
    }

    return ReWOOState({**base_state, **rewoo_fields})


# Reducer functions for ReWOO state management
def worker_results_reducer(
    existing: Dict[str, WorkerResult], new: Dict[str, WorkerResult]
) -> Dict[str, WorkerResult]:
    """Merge worker results, with new results taking precedence."""
    return {**existing, **new}


def parallel_execution_status_reducer(
    existing: Dict[str, str], new: Dict[str, str]
) -> Dict[str, str]:
    """Update parallel execution status tracking."""
    return {**existing, **new}


def coordination_metadata_reducer(
    existing: Dict[str, Any], new: Dict[str, Any]
) -> Dict[str, Any]:
    """Merge coordination metadata, preserving important fields."""
    return {**existing, **new}
