"""
Web Search State Management

State classes for managing web search, scraping, and research workflows.
Provides structured state tracking for multi-agent web research processes.
"""

from typing import Dict, List, Optional, Any, Annotated
from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict

from .state import AgentState


class WebSearchResult(BaseModel):
    """Individual web search result."""

    title: str
    url: str
    snippet: str
    position: int
    source_domain: str
    relevance_score: Optional[float] = None


class ScrapedWebContent(BaseModel):
    """Scraped content from a webpage."""

    url: str
    title: str
    content: str
    content_length: int
    extracted_links: Optional[List[str]] = None
    status: str  # "success", "error", "garbled"
    error_message: Optional[str] = None
    scrape_timestamp: Optional[str] = None


class WebSearchPlan(BaseModel):
    """Search plan generated by planner agent."""

    search_terms: List[str]
    search_strategy: str
    expected_sources: List[str]
    content_focus: str
    max_results_per_query: int = 10


class WebSearchState(AgentState):
    """
    State for web search workflow processes.

    Tracks the complete workflow from research question through
    search planning, execution, content scraping, and synthesis.
    """

    # Core research context
    research_question: str
    research_context: Optional[str]
    research_type: str

    # Search planning
    search_plan: Optional[WebSearchPlan]
    search_queries: List[str]

    # Search results
    search_results: List[WebSearchResult]
    selected_urls: List[str]

    # Scraped content
    scraped_contents: List[ScrapedWebContent]
    successful_scrapes: List[ScrapedWebContent]

    # Analysis and synthesis
    content_analysis: Optional[str]
    research_summary: Optional[str]
    key_findings: List[str]
    sources_cited: List[str]

    # Workflow control
    max_search_results: int
    max_pages_to_scrape: int

    # Quality and validation
    quality_score: Optional[float]
    validation_status: str
    validation_notes: List[str]

    # Agent messages (for LangGraph compatibility)
    planner_messages: Annotated[List[BaseMessage], add_messages]
    searcher_messages: Annotated[List[BaseMessage], add_messages]
    scraper_messages: Annotated[List[BaseMessage], add_messages]
    analyzer_messages: Annotated[List[BaseMessage], add_messages]
    synthesizer_messages: Annotated[List[BaseMessage], add_messages]

    # Error handling
    search_errors: List[str]
    scraping_errors: List[str]


# Helper functions for state management
def add_search_result(state: WebSearchState, result: WebSearchResult) -> WebSearchState:
    """Add a search result to the state."""
    new_state = state.copy()
    new_state["search_results"].append(result)
    return new_state


def add_scraped_content(
    state: WebSearchState, content: ScrapedWebContent
) -> WebSearchState:
    """Add scraped content to the state."""
    new_state = state.copy()
    new_state["scraped_contents"].append(content)
    if content.status == "success":
        new_state["successful_scrapes"].append(content)
    return new_state


def get_successful_content(state: WebSearchState) -> List[ScrapedWebContent]:
    """Get only successfully scraped content."""
    return [
        content for content in state["scraped_contents"] if content.status == "success"
    ]


def get_total_content_length(state: WebSearchState) -> int:
    """Get total length of all successfully scraped content."""
    return sum(content.content_length for content in get_successful_content(state))


def get_unique_domains(state: WebSearchState) -> List[str]:
    """Get list of unique domains from search results."""
    domains = set()
    for result in state["search_results"]:
        if result.source_domain:
            domains.add(result.source_domain)
    return list(domains)


def update_workflow_step(state: WebSearchState, step: str) -> WebSearchState:
    """Update the current workflow step."""
    new_state = state.copy()
    new_state["current_step"] = step
    return new_state


def is_research_complete(state: WebSearchState) -> bool:
    """Check if research workflow is complete."""
    return (
        state["research_summary"] is not None
        and len(state["successful_scrapes"]) > 0
        and state["current_step"] == "complete"
    )


def get_research_metrics(state: WebSearchState) -> Dict[str, Any]:
    """Get metrics about the research process."""
    return {
        "total_search_results": len(state["search_results"]),
        "total_scraped_pages": len(state["scraped_contents"]),
        "successful_scrapes": len(state["successful_scrapes"]),
        "total_content_length": get_total_content_length(state),
        "unique_domains": len(get_unique_domains(state)),
        "search_queries_executed": len(state["search_queries"]),
        "current_step": state["current_step"],
        "quality_score": state["quality_score"],
        "validation_status": state["validation_status"],
    }


class WebSearchWorkflowState(WebSearchState):
    """
    Extended state for complex web search workflows.

    Includes additional fields for multi-step research processes,
    iterative refinement, and collaborative research scenarios.
    """

    # Workflow management
    workflow_id: str = Field(default="", description="Unique workflow identifier")
    parent_workflow_id: Optional[str] = Field(
        default=None, description="Parent workflow if this is a sub-research"
    )
    sub_workflows: List[str] = Field(
        default_factory=list, description="Child workflow IDs"
    )

    # Iterative refinement
    iteration_count: int = Field(default=1, description="Current iteration number")
    max_iterations: int = Field(default=3, description="Maximum allowed iterations")
    refinement_criteria: List[str] = Field(
        default_factory=list, description="Criteria for refinement"
    )

    # Collaborative features
    assigned_agents: List[str] = Field(
        default_factory=list, description="Agents assigned to this research"
    )
    agent_specializations: Dict[str, str] = Field(
        default_factory=dict, description="Agent specialization mapping"
    )

    # Advanced analysis
    topic_categories: List[str] = Field(
        default_factory=list, description="Identified topic categories"
    )
    content_themes: List[str] = Field(
        default_factory=list, description="Major content themes"
    )
    information_gaps: List[str] = Field(
        default_factory=list, description="Identified information gaps"
    )
    follow_up_questions: List[str] = Field(
        default_factory=list, description="Generated follow-up questions"
    )

    # Research quality
    source_reliability_scores: Dict[str, float] = Field(
        default_factory=dict, description="Source reliability scores"
    )
    information_freshness: Dict[str, str] = Field(
        default_factory=dict, description="Information freshness indicators"
    )
    bias_indicators: List[str] = Field(
        default_factory=list, description="Potential bias indicators"
    )

    def should_continue_iteration(self) -> bool:
        """Determine if another iteration is warranted."""
        if self.iteration_count >= self.max_iterations:
            return False

        # Check if we have enough quality content
        if len(self.successful_scrapes) < 3:
            return True

        # Check if information gaps exist
        if len(self.information_gaps) > 0:
            return True

        # Check quality score
        if self.quality_score and self.quality_score < 0.7:
            return True

        return False

    def prepare_next_iteration(self) -> None:
        """Prepare state for next iteration."""
        self.iteration_count += 1
        self.current_step = "planning"

        # Generate refined search queries based on gaps
        if self.information_gaps:
            additional_queries = [
                f"{self.research_question} {gap}" for gap in self.information_gaps[:3]
            ]
            self.search_queries.extend(additional_queries)


# Default state configuration
DEFAULT_WEB_SEARCH_STATE = {
    # Base AgentState fields
    "messages": [],
    "user_query": None,
    "user_profile": None,
    "routing_decision": None,
    "pending_agents": None,
    "plan": None,
    "current_step": None,
    "domain_contributions": {},
    "required_domains": [],
    "completed_domains": [],
    "aggregated_plan": None,
    "proceed_to_generation": False,
    "current_plan": None,
    "is_onboarding": False,
    "strength_response": None,
    "running_response": None,
    "cardio_response": None,
    "cycling_response": None,
    "nutrition_response": None,
    "recovery_response": None,
    "mental_response": None,
    "reasoning_output": None,
    "clarification_output": None,
    "aggregated_response": None,
    # Web search specific fields
    "research_question": "",
    "research_context": None,
    "research_type": "general",
    "search_plan": None,
    "search_queries": [],
    "search_results": [],
    "selected_urls": [],
    "scraped_contents": [],
    "successful_scrapes": [],
    "content_analysis": None,
    "research_summary": None,
    "key_findings": [],
    "sources_cited": [],
    "max_search_results": 10,
    "max_pages_to_scrape": 5,
    "quality_score": None,
    "validation_status": "pending",
    "validation_notes": [],
    "planner_messages": [],
    "searcher_messages": [],
    "scraper_messages": [],
    "analyzer_messages": [],
    "synthesizer_messages": [],
    "search_errors": [],
    "scraping_errors": [],
}
