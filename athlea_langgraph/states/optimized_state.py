"""
Optimized State for the Optimized Coaching Graph

This state is specifically designed for the optimized coaching graph to ensure
proper state management and field reducers that support the Intelligence Hub
and Smart Executor state transfer requirements.
"""

from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages, MessagesState
from typing_extensions import TypedDict

from .state import messages_reducer


# Optimized state reducers for proper state management
def optimized_routing_decision_reducer(
    left: Optional[str], right: Optional[str]
) -> Optional[str]:
    """Reducer for routing decisions - always use the latest value."""
    return right if right is not None else left


def optimized_primary_coach_reducer(
    left: Optional[str], right: Optional[str]
) -> Optional[str]:
    """Reducer for primary coach - always use the latest value."""
    return right if right is not None else left


def optimized_required_coaches_reducer(
    left: Optional[List[str]], right: Optional[List[str]]
) -> Optional[List[str]]:
    """Reducer for required coaches - always use the latest value."""
    return right if right is not None else left


def optimized_coach_responses_reducer(
    left: Optional[Dict[str, str]], right: Optional[Dict[str, str]]
) -> Dict[str, str]:
    """Reducer for coach responses - merge responses."""
    if left is None:
        left = {}
    if right is None:
        right = {}
    return {**left, **right}


def optimized_execution_steps_reducer(
    left: Optional[List[str]], right: Optional[List[str]]
) -> List[str]:
    """Reducer for execution steps - append new steps but avoid duplicates."""
    if left is None:
        left = []
    if right is None:
        right = []

    # CRITICAL: Only append steps that aren't already in left to prevent loops
    result = left.copy()
    for step in right:
        if step not in result:
            result.append(step)

    return result


def optimized_user_profile_reducer(
    left: Optional[Dict[str, Any]], right: Optional[Dict[str, Any]]
) -> Optional[Dict[str, Any]]:
    """Reducer for user profile - merge profiles."""
    if right is None:
        return left
    if left is None:
        return right
    return {**left, **right}


def optimized_debug_info_reducer(
    left: Optional[Dict[str, Any]], right: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """Reducer for debug info - always use the latest value."""
    if right is None:
        return left if left is not None else {}
    return right


class OptimizedCoachingState(MessagesState):
    """
    Optimized state for the optimized coaching graph.

    IMPORTANT: This state extends MessagesState to enable Chat Mode support in LangGraph Studio.
    Chat mode is only supported for graphs whose state includes or extends MessagesState.

    This state includes proper reducers for all fields to ensure
    reliable state transfer between nodes.
    """

    # Core conversation state - inherited from MessagesState
    # messages: Annotated[List[BaseMessage], add_messages] - inherited from MessagesState

    user_query: str
    user_profile: Annotated[Optional[Dict[str, Any]], optimized_user_profile_reducer]

    # Reasoning output from reasoning node (critical for Intelligence Hub)
    reasoning_output: Optional[str]

    # Intelligence Hub assessment fields
    routing_decision: Annotated[Optional[str], optimized_routing_decision_reducer]
    intent_classification: Optional[str]
    required_coaches: Annotated[Optional[List[str]], optimized_required_coaches_reducer]
    complexity_level: Optional[str]
    complexity_score: Optional[float]
    knowledge_retrieval_needed: Optional[bool]
    retrieval_type: Optional[str]  # CRITICAL: Missing field for GraphRAG
    primary_coach: Annotated[Optional[str], optimized_primary_coach_reducer]
    confidence_score: Optional[
        float
    ]  # CRITICAL: Missing field for assessment confidence
    assessment_reasoning: Optional[
        str
    ]  # CRITICAL: Missing field for assessment reasoning (renamed to avoid node conflict)

    # Execution tracking
    current_node: Optional[str]
    execution_steps: Annotated[Optional[List[str]], optimized_execution_steps_reducer]
    execution_path: Optional[str]

    # Coach responses and outputs
    coach_responses: Annotated[
        Optional[Dict[str, str]], optimized_coach_responses_reducer
    ]
    final_response: Optional[str]
    specialist_response: Optional[
        str
    ]  # CRITICAL: Missing field that aggregation node expects
    aggregated_response: Optional[str]
    clarification_output: Optional[str]

    # GraphRAG and knowledge retrieval
    knowledge_context: Optional[str]
    retrieved_documents: Optional[List[Dict[str, Any]]]

    # Metadata and debugging
    intelligence_assessment: Optional[Dict[str, Any]]
    debug_info: Annotated[Optional[Dict[str, Any]], optimized_debug_info_reducer]

    # Thread and user management
    thread_id: Optional[str]
    user_id: Optional[str]

    # Configuration settings that need to be accessible to nodes
    enable_human_feedback: Optional[
        bool
    ]  # CRITICAL: Add this field so aggregation node can access it


# Type alias for backward compatibility
OptimizedState = OptimizedCoachingState
