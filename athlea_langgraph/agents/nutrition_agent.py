"""
Nutrition Agent - Modular ReAct Implementation

Specialized agent for nutritional coaching, dietary planning, and meal optimization.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.nutrition import (
    calculate_daily_calories,
    calculate_macro_targets,
    generate_meal_plan,
    search_recipes,
    get_recipe_recommendations,
)

# Additional tool access capabilities
from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool
from ..tools.external.session_generation import SessionGenerationTool
from langchain_core.tools import Tool

from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class NutritionAgent(BaseReActAgent):
    """Specialized agent for nutrition coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the nutrition agent with domain-specific tools."""
        # Fallback prompt
        fallback_prompt = """You are a Nutrition Coach specializing in dietary planning, meal optimization, and nutritional guidance for fitness and health goals.

Your expertise includes:
- Macronutrient and micronutrient planning
- Meal timing and frequency optimization
- Sport-specific nutrition strategies
- Weight management (gain/loss/maintenance)
- Supplement recommendations
- Hydration strategies
- Nutrition for recovery and performance

You have access to specialized tools for nutrition calculations, meal planning, and recipe recommendations.
Based on the user's query, you MUST decide whether to use one of your tools or answer directly.

Your available tools are:
- `calculate_daily_calories`: Use this tool when a user wants to calculate their daily caloric needs based on their goals, activity level, and personal metrics.
- `calculate_macro_targets`: Use this tool when a user wants to calculate their macronutrient targets (protein, carbs, fats) based on their goals and caloric needs.
- `generate_meal_plan`: Use this tool when a user wants a comprehensive meal plan with detailed nutritional information and specific dietary requirements.
- `search_recipes`: Use this tool when a user wants to find recipes based on specific criteria like ingredients, dietary restrictions, or nutritional goals.
- `get_recipe_recommendations`: Use this tool when a user wants personalized recipe recommendations based on their preferences and nutritional needs.

Always provide evidence-based nutritional advice and consider the user's goals, dietary preferences, restrictions, and lifestyle factors."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="nutrition_agent",
            domain="nutrition",
            system_prompt=fallback_prompt,  # Will be updated lazily
            tools=[],  # Will be populated by get_domain_tools()
            permissions=["nutrition_assessment", "meal_planning", "macro_calculation"],
            max_iterations=10,
            temperature=0.7,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self) -> str:
        """Load the system prompt lazily"""
        if not self._prompt_loaded:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("nutrition_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded nutrition coach prompt from file")
            except Exception as e:
                logger.error(f"Failed to load nutrition coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        Instantiates and returns the specific tools for the nutrition agent.
        This makes the agent self-contained and independent of a central tool manager.
        Now includes broader tool access: Azure Search, session generation, and web search.
        """
        if not self._tools_loaded:
            # Domain-specific nutrition tools
            nutrition_tools = [
                calculate_daily_calories,
                calculate_macro_targets,
                generate_meal_plan,
                search_recipes,
                get_recipe_recommendations,
            ]
            
            # Add broader tool access capabilities
            try:
                # Azure Search for research and evidence-based information
                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based nutrition information.
                    
                    Use this tool for:
                    - Nutrition science and research studies
                    - Supplement research and safety information
                    - Dietary guidelines and recommendations
                    - Sports nutrition strategies
                    - Food science and metabolism research
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke
                )
                nutrition_tools.append(azure_search_langchain)
                
                # Session Generation for structured nutrition plans
                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_nutrition_session",
                    description="""Generate structured nutrition plans and meal sessions.
                    
                    Use this tool to create:
                    - Meal planning sessions
                    - Nutrition education programs
                    - Supplement protocols
                    - Hydration strategies
                    - Recovery nutrition plans
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session
                )
                nutrition_tools.append(session_gen_langchain)
                
                # Web Search through MCP for current information
                from ..tools.web_search_tool import WebSearchTool
                web_search_tool = WebSearchTool()
                nutrition_tools.append(web_search_tool)
                
                logger.info("Successfully added broader tool access capabilities to nutrition agent")
                
            except Exception as e:
                logger.warning(f"Failed to add some broader tools to nutrition agent: {e}")

            # Update the agent's tools
            self.tools = nutrition_tools
            # Update the ReAct executor's tools if it exists
            if hasattr(self, "react_executor") and self.react_executor:
                self.react_executor.tools = nutrition_tools

            logger.info(
                f"Loaded {len(nutrition_tools)} tools for nutrition agent: {[t.name for t in nutrition_tools]}"
            )
            self._tools_loaded = True

        return self.tools
    


    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for nutrition coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process nutrition requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing
        await self._load_system_prompt()

        return await super().process(state, config)


# Create the nutrition agent instance
nutrition_agent = NutritionAgent()


async def nutrition_agent_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for nutrition coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with nutrition agent response
    """
    logger.info("--- Running Nutrition Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not nutrition_agent.tools:
            await nutrition_agent.get_domain_tools()

        # Process the request
        result = await nutrition_agent.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "nutrition_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Nutrition agent completed with {len(nutrition_agent.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in nutrition agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your nutrition request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "nutrition_agent",
            "error": str(e),
        }
