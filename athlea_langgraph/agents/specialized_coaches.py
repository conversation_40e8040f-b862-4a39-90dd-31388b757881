"""\
Specialized Coaching Agents

Python translation of the specialized coaching agents from the TypeScript implementation.
Each coach provides domain-specific expertise and tools.

ARCHITECTURAL PATTERN - Questions/Clarifications vs Tools:

✅ CORRECT: Tools handle ACTIONS and DATA
- calculate_training_metrics() - calculates BMI, heart rate zones
- estimate_caloric_needs() - calculates nutrition requirements
- fitness_advice() - provides domain knowledge
- create_basic_plan() - generates structured plans

✅ CORRECT: Nodes handle CONVERSATION FLOW
- clarification_node() - generates clarifying questions using LLM
- user_input_node() - interrupts flow to collect user feedback (using interrupt())
- aggregation_node() - combines responses and manages conversation

❌ INCORRECT (previously): Tools that pretend to ask questions
- gather_user_input() - returned strings, didn't actually gather input
- questions() - returned formatted text, no flow control

Best Practices:
1. Tools = External APIs, calculations, data retrieval, actions
2. Nodes = Conversation control, response generation, routing decisions
3. Use interrupt() in nodes for actual user input collection
4. Never use tools for conversation flow management

This follows conversational AI best practices where tools handle domain actions
while nodes manage the dialogue flow and user interaction patterns.
"""

import json
from typing import Any, Dict, List, Optional

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.tools import tool

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states import AgentState
from athlea_langgraph.utils.prompt_loader import PromptLoader

# Initialize prompt loader for versioned prompts
_prompt_loader = PromptLoader()


# Basic tools that coaches can use
@tool
def calculate_training_metrics(
    weight: float, height: float, age: int, activity_level: str = "moderate"
) -> str:
    """
    Calculate basic training metrics like BMI, target heart rate zones.

    Args:
        weight: Weight in kg
        height: Height in cm
        age: Age in years
        activity_level: Activity level (sedentary, light, moderate, active, very_active)

    Returns:
        Calculated training metrics
    """
    # Calculate BMI
    height_m = height / 100
    bmi = weight / (height_m**2)

    # Calculate target heart rate zones
    max_hr = 220 - age
    zone1_lower = int(max_hr * 0.5)  # 50-60% - Recovery
    zone1_upper = int(max_hr * 0.6)
    zone2_lower = int(max_hr * 0.6)  # 60-70% - Aerobic base
    zone2_upper = int(max_hr * 0.7)
    zone3_lower = int(max_hr * 0.7)  # 70-80% - Aerobic
    zone3_upper = int(max_hr * 0.8)
    zone4_lower = int(max_hr * 0.8)  # 80-90% - Threshold
    zone4_upper = int(max_hr * 0.9)
    zone5_lower = int(max_hr * 0.9)  # 90-100% - Neuromuscular

    return f"""Training Metrics:
    
BMI: {bmi:.1f}
Max Heart Rate: {max_hr} bpm

Heart Rate Training Zones:
• Zone 1 (Recovery): {zone1_lower}-{zone1_upper} bpm
• Zone 2 (Aerobic Base): {zone2_lower}-{zone2_upper} bpm  
• Zone 3 (Aerobic): {zone3_lower}-{zone3_upper} bpm
• Zone 4 (Threshold): {zone4_lower}-{zone4_upper} bpm
• Zone 5 (Neuromuscular): {zone5_lower}+ bpm

Activity Level: {activity_level}"""


@tool
def estimate_caloric_needs(
    weight: float,
    height: float,
    age: int,
    gender: str,
    activity_level: str,
    goal: str = "maintain",
) -> str:
    """
    Estimate daily caloric needs based on user parameters.

    Args:
        weight: Weight in kg
        height: Height in cm
        age: Age in years
        gender: 'male' or 'female'
        activity_level: sedentary, light, moderate, active, very_active
        goal: maintain, lose, gain

    Returns:
        Estimated caloric needs and macronutrient breakdown
    """
    # Calculate BMR using Mifflin-St Jeor Equation
    if gender.lower() == "male":
        bmr = 10 * weight + 6.25 * height - 5 * age + 5
    else:
        bmr = 10 * weight + 6.25 * height - 5 * age - 161

    # Activity multipliers
    activity_multipliers = {
        "sedentary": 1.2,
        "light": 1.375,
        "moderate": 1.55,
        "active": 1.725,
        "very_active": 1.9,
    }

    tdee = bmr * activity_multipliers.get(activity_level.lower(), 1.55)

    # Adjust for goal
    if goal.lower() == "lose":
        target_calories = tdee - 500  # 1 lb/week loss
        goal_desc = "weight loss (1 lb/week)"
    elif goal.lower() == "gain":
        target_calories = tdee + 500  # 1 lb/week gain
        goal_desc = "weight gain (1 lb/week)"
    else:
        target_calories = tdee
        goal_desc = "maintenance"

    # Macronutrient breakdown (moderate approach)
    protein_cals = target_calories * 0.25  # 25% protein
    fat_cals = target_calories * 0.30  # 30% fat
    carb_cals = target_calories * 0.45  # 45% carbs

    protein_g = protein_cals / 4
    fat_g = fat_cals / 9
    carb_g = carb_cals / 4

    return f"""Caloric Needs Estimate:

BMR (Basal Metabolic Rate): {bmr:.0f} calories
TDEE (Total Daily Energy Expenditure): {tdee:.0f} calories
Target Calories for {goal_desc}: {target_calories:.0f} calories

Suggested Macronutrient Breakdown:
• Protein: {protein_g:.0f}g ({protein_cals:.0f} calories, 25%)
• Carbohydrates: {carb_g:.0f}g ({carb_cals:.0f} calories, 45%)  
• Fats: {fat_g:.0f}g ({fat_cals:.0f} calories, 30%)

Note: These are estimates. Individual needs may vary based on metabolism, medical conditions, and training intensity."""


@tool
def fitness_advice(topic: str, level: str = "beginner", focus_area: str = "") -> str:
    """
    Provide general fitness advice on a specific topic.

    Args:
        topic: The fitness topic to provide advice on
        level: The user's experience level (beginner, intermediate, advanced)
        focus_area: Specific area of focus within the topic

    Returns:
        General fitness advice
    """
    advice_templates = {
        "strength": {
            "beginner": "Start with bodyweight exercises like push-ups, squats, and planks. Focus on proper form over heavy weights.",
            "intermediate": "Incorporate compound movements like deadlifts, squats, and bench press. Aim for progressive overload.",
            "advanced": "Consider periodization, advanced techniques like drop sets, and sport-specific movements.",
        },
        "cardio": {
            "beginner": "Start with 20-30 minutes of moderate activity 3-4 times per week. Walking, light jogging, or cycling are great options.",
            "intermediate": "Include interval training and aim for 150 minutes of moderate activity or 75 minutes of vigorous activity per week.",
            "advanced": "Incorporate high-intensity interval training (HIIT) and sport-specific cardio protocols.",
        },
        "running": {
            "beginner": "Start with a run-walk program. Gradually increase running time while decreasing walking intervals.",
            "intermediate": "Focus on building your aerobic base with easy runs, adding one tempo run per week.",
            "advanced": "Include speed work, hill training, and periodized training for specific race goals.",
        },
        "nutrition": {
            "beginner": "Focus on whole foods, adequate protein (0.8-1g per kg bodyweight), and staying hydrated.",
            "intermediate": "Time your nutrition around workouts. Consider macro tracking if you have specific goals.",
            "advanced": "Fine-tune nutrient timing, consider supplements, and adjust based on training periodization.",
        },
        "recovery": {
            "beginner": "Prioritize 7-9 hours of sleep and take at least one rest day per week.",
            "intermediate": "Include active recovery, stretching, and stress management techniques.",
            "advanced": "Use advanced recovery modalities like massage, compression, and periodized recovery protocols.",
        },
        "mental": {
            "beginner": "Set realistic goals and celebrate small wins. Practice positive self-talk.",
            "intermediate": "Develop pre-performance routines and visualization techniques.",
            "advanced": "Master stress management, flow states, and competition psychology techniques.",
        },
    }

    topic_lower = topic.lower()
    level_lower = level.lower()

    if topic_lower in advice_templates and level_lower in advice_templates[topic_lower]:
        base_advice = advice_templates[topic_lower][level_lower]
        if focus_area:
            return f"{base_advice}\n\nFor {focus_area} specifically: Focus on consistency and gradual progression in this area."
        return base_advice

    return f"Here's some general advice for {topic}: Focus on consistency, proper form, and gradual progression. Consider working with a qualified coach for personalized guidance."


@tool
def create_basic_plan(
    goal: str, duration: str = "4 weeks", experience: str = "beginner"
) -> str:
    """
    Create a basic training plan outline.

    Args:
        goal: The training goal (strength, endurance, weight loss, etc.)
        duration: How long the plan should be
        experience: Experience level of the user

    Returns:
        A basic plan outline
    """
    return f"""Here's a basic {duration} {goal} plan for a {experience}:

Week 1-2: Foundation Phase
- Focus on form and movement patterns
- Gradually increase training frequency
- Establish consistent habits

Week 3-4: Progression Phase
- Increase intensity or volume by 10-15%
- Refine technique
- Monitor recovery and adjust as needed

Remember: This is a general outline. For a detailed, personalized plan, please provide more information about your current fitness level, available time, and specific preferences."""


# DEPRECATED: Old hardcoded prompts - now replaced with versioned JSON prompts
# These are kept for backward compatibility but are no longer used
STRENGTH_COACH_PROMPT = (
    """DEPRECATED: This prompt is now loaded from versioned JSON files"""
)
CARDIO_COACH_PROMPT = (
    """DEPRECATED: This prompt is now loaded from versioned JSON files"""
)
CYCLING_COACH_PROMPT = (
    """DEPRECATED: This prompt is now loaded from versioned JSON files"""
)
NUTRITION_COACH_PROMPT = (
    """DEPRECATED: This prompt is now loaded from versioned JSON files"""
)
RECOVERY_COACH_PROMPT = (
    """DEPRECATED: This prompt is now loaded from versioned JSON files"""
)
MENTAL_COACH_PROMPT = (
    """DEPRECATED: This prompt is now loaded from versioned JSON files"""
)


class SpecializedCoachToolsManager:
    """Manages tool distribution to specialized coaches based on domain expertise."""

    def __init__(self):
        self._initialized = False
        self._basic_tools = [
            calculate_training_metrics,
            estimate_caloric_needs,
            fitness_advice,
            create_basic_plan,
        ]

    async def initialize_tools(self):
        """Initialize all available tools."""
        print("✅ Tools manager initialized with basic coaching tools")
        self._initialized = True

    def get_strength_coach_tools(self) -> List[Any]:
        """Get tools specific to strength coaching."""
        return self._basic_tools

    def get_cardio_coach_tools(self) -> List[Any]:
        """Get tools specific to cardio coaching."""
        return self._basic_tools

    def get_cycling_coach_tools(self) -> List[Any]:
        """Get tools specific to cycling coaching."""
        return self._basic_tools

    def get_nutrition_coach_tools(self) -> List[Any]:
        """Get tools specific to nutrition coaching."""
        return self._basic_tools

    def get_recovery_coach_tools(self) -> List[Any]:
        """Get tools specific to recovery coaching."""
        return self._basic_tools

    def get_mental_coach_tools(self) -> List[Any]:
        """Get tools specific to mental coaching."""
        return self._basic_tools


# Global tools manager instance
_tools_manager = SpecializedCoachToolsManager()


async def get_tools_manager() -> SpecializedCoachToolsManager:
    """Get the initialized tools manager."""
    if not _tools_manager._initialized:
        await _tools_manager.initialize_tools()
    return _tools_manager


async def create_specialized_coach_node(
    coach_name: str, coach_prompt_id: str, get_tools_func: callable
):
    """
    Create a specialized coach node function with domain-specific tools.

    Args:
        coach_name: Name of the coach (e.g., "strength_coach")
        coach_prompt_id: ID of the versioned prompt to load (e.g., "strength_coach")
        get_tools_func: Function that returns tools for this coach

    Returns:
        Async function that can be used as a node in the graph
    """
    tools_manager = await get_tools_manager()
    tools = get_tools_func(tools_manager)

    # Load versioned prompt from JSON file
    try:
        prompt_config = _prompt_loader.load_prompt(coach_prompt_id)
        coach_prompt = prompt_config.prompt.system
        model_config = prompt_config.variables
        print(
            f"✅ Loaded versioned prompt for {coach_name} v{prompt_config.metadata.version}"
        )
    except Exception as e:
        print(f"⚠️ Failed to load versioned prompt for {coach_name}: {e}")
        # Fallback to hardcoded prompt if versioned prompt fails
        coach_prompt = f"You are a {coach_name} coach. Please provide helpful advice."
        model_config = None

    async def coach_node(
        state: AgentState, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Specialized coach node implementation with tool support.

        Args:
            state: Current graph state
            config: Optional configuration

        Returns:
            Updated state with coach response
        """

        print(f"--- Running {coach_name} Node ---")

        # Extract state information
        user_profile = config.get("userProfile") or state.get("user_profile")
        messages = state.get("messages", [])

        # Create the coach LLM with versioned model configuration
        if model_config:
            coach_llm = create_azure_chat_openai(
                temperature=model_config.temperature, max_tokens=model_config.max_tokens
            )
        else:
            # Fallback configuration
            coach_llm = create_azure_chat_openai(temperature=0.7, max_tokens=4000)

        # Bind tools to the LLM if available
        if tools:
            try:
                coach_llm = coach_llm.bind_tools(tools)
                print(
                    f"  > {coach_name} has {len(tools)} tools available: {[getattr(t, 'name', 'unknown') for t in tools]}"
                )
            except Exception as e:
                print(f"  > Warning: Failed to bind tools to {coach_name}: {e}")

        # Prepare the system message with coach prompt and user profile
        system_content = coach_prompt
        if user_profile:
            system_content += f"\n\nUser Profile:\n{json.dumps(user_profile, indent=2)}"

        # Prepare conversation history for context
        conversation_messages = [SystemMessage(content=system_content)]

        # Add recent conversation history (last 10 messages to avoid token limits)
        recent_messages = messages[-10:] if len(messages) > 10 else messages
        for msg in recent_messages:
            if isinstance(msg, (HumanMessage, AIMessage, ToolMessage)):
                conversation_messages.append(msg)

        try:
            print(f"  > Calling {coach_name} LLM...")
            response = await coach_llm.ainvoke(conversation_messages)

            # Handle tool calls if present
            if hasattr(response, "tool_calls") and response.tool_calls:
                print(f"  > {coach_name} made {len(response.tool_calls)} tool calls")

                # For now, return the response with tool calls
                response_message = AIMessage(
                    content=response.content or "I'm using my tools to help you...",
                    name=coach_name,
                    tool_calls=response.tool_calls,
                )

                return {"messages": [response_message]}

            # Handle regular response
            if isinstance(response.content, str) and response.content.strip():
                coach_response = response.content.strip()
                print(f"  > {coach_name} response: {coach_response[:150]}...")

                response_message = AIMessage(content=coach_response, name=coach_name)
                return {"messages": [response_message]}
            else:
                print(f"  > {coach_name} returned empty response")
                return {
                    "messages": [
                        AIMessage(
                            content="I apologize, but I'm having trouble generating a response right now. Please try again.",
                            name=coach_name,
                        )
                    ]
                }

        except Exception as error:
            print(f"Error in {coach_name}: {error}")
            return {
                "messages": [
                    AIMessage(
                        content=f"I apologize, but I encountered an error while processing your request. Please try again.",
                        name=coach_name,
                    )
                ]
            }

    return coach_node


async def create_all_specialized_coaches() -> Dict[str, Any]:
    """
    Create all specialized coaching agents with their domain-specific tools.

    Returns:
        Dictionary mapping coach names to their node functions
    """

    return {
        "strength_coach": await create_specialized_coach_node(
            "strength_coach",
            "strength_power_coach",  # Load from athlea_langgraph/prompts/coaches/strength_coach.json (metadata.name is strength_power_coach)
            lambda tm: tm.get_strength_coach_tools(),
        ),
        "running_coach": await create_specialized_coach_node(
            "running_coach",
            "running_coach",  # Load from athlea_langgraph/prompts/coaches/running_coach.json
            lambda tm: tm.get_cardio_coach_tools(),
        ),
        "cardio_coach": await create_specialized_coach_node(
            "cardio_coach",
            "endurance_coach",  # Load from athlea_langgraph/prompts/coaches/endurance_coach.json
            lambda tm: tm.get_cardio_coach_tools(),
        ),
        "cycling_coach": await create_specialized_coach_node(
            "cycling_coach",
            "cycling_coach",  # Load from athlea_langgraph/prompts/coaches/cycling_coach.json
            lambda tm: tm.get_cycling_coach_tools(),
        ),
        "nutrition_coach": await create_specialized_coach_node(
            "nutrition_coach",
            "nutrition_hydration_coach",  # Load from athlea_langgraph/prompts/coaches/nutrition_coach.json (metadata.name is nutrition_hydration_coach)
            lambda tm: tm.get_nutrition_coach_tools(),
        ),
        "recovery_coach": await create_specialized_coach_node(
            "recovery_coach",
            "injury_prevention_recovery_coach",  # Load from athlea_langgraph/prompts/coaches/recovery_coach.json (metadata.name is injury_prevention_recovery_coach)
            lambda tm: tm.get_recovery_coach_tools(),
        ),
        "mental_coach": await create_specialized_coach_node(
            "mental_coach",
            "psychology_mindset_coach",  # Load from athlea_langgraph/prompts/coaches/mental_coach.json (metadata.name is psychology_mindset_coach)
            lambda tm: tm.get_mental_coach_tools(),
        ),
    }


# Individual coach node functions for backward compatibility
async def strength_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Strength coach node with specialized tools."""
    coach_node_func = await create_specialized_coach_node(
        "strength_coach",
        "strength_power_coach",  # Use versioned prompt ID
        lambda tm: tm.get_strength_coach_tools(),
    )

    return await coach_node_func(state, config)


async def running_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Running coach node with specialized tools."""
    coach_node_func = await create_specialized_coach_node(
        "running_coach",
        "running_coach",  # Use versioned prompt ID
        lambda tm: tm.get_cardio_coach_tools(),
    )

    return await coach_node_func(state, config)


async def cardio_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Cardio coach node with specialized tools."""
    coach_node_func = await create_specialized_coach_node(
        "cardio_coach",
        "endurance_coach",  # Use versioned prompt ID
        lambda tm: tm.get_cardio_coach_tools(),
    )

    return await coach_node_func(state, config)


async def cycling_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Cycling coach node with specialized tools."""
    print(
        f"DEBUG: cycling_coach_node called. state keys: {state.keys() if isinstance(state, dict) else 'not a dict state'}"
    )

    # create_specialized_coach_node itself returns the actual node function (coach_node)
    # It's an async function because it *might* await things internally (like get_tools_manager),
    # but its return value is the callable node.
    coach_node_func = await create_specialized_coach_node(
        "cycling_coach",
        "cycling_coach",
        lambda tm: tm.get_cycling_coach_tools(),  # Use versioned prompt ID
    )
    # At this point, coach_node_func *is* the inner 'coach_node' function.

    print(
        f"DEBUG: Type of coach_node_func in cycling_coach_node: {type(coach_node_func)}"
    )

    # Now call the actual node function, which is also async
    return_value = await coach_node_func(state, config)
    print(f"DEBUG: cycling_coach_node returning type: {type(return_value)}")
    print(f"DEBUG: cycling_coach_node returning value: {str(return_value)[:500]}")
    return return_value


async def nutrition_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Nutrition coach node with specialized tools."""
    coach_node_func = await create_specialized_coach_node(
        "nutrition_coach",
        "nutrition_hydration_coach",  # Use versioned prompt ID
        lambda tm: tm.get_nutrition_coach_tools(),
    )

    return await coach_node_func(state, config)


async def recovery_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Recovery coach node with specialized tools."""
    coach_node_func = await create_specialized_coach_node(
        "recovery_coach",
        "injury_prevention_recovery_coach",  # Use versioned prompt ID
        lambda tm: tm.get_recovery_coach_tools(),
    )

    return await coach_node_func(state, config)


async def mental_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Mental coach node with specialized tools."""
    coach_node_func = await create_specialized_coach_node(
        "mental_coach",
        "psychology_mindset_coach",  # Use versioned prompt ID
        lambda tm: tm.get_mental_coach_tools(),
    )

    return await coach_node_func(state, config)


async def select_specialized_coach_node(state: AgentState) -> Dict[str, str]:
    """
    Router node that determines which specialist coach to call based on the plan.
    This should work with the planning system, not use hardcoded keywords.
    """
    print("--- Specialized Coach Router ---")

    # Get the plan from the planning node
    plan = state.get("plan", [])
    current_step = state.get("current_step", 0)

    # Determine the next node based on the plan
    if plan and current_step < len(plan):
        next_node = plan[current_step]
        print(f"Following plan step {current_step}: {next_node}")
    else:
        # If no plan or we've completed all steps, default to head_coach
        next_node = "head_coach"
        print("No plan available or plan completed, defaulting to head_coach")

    print(f"--- Specialized Coach Router --- Decided next node: {next_node}")

    # Return the routing decision as part of the state
    return {"routing_decision": next_node}


async def specialized_coaches_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    print("--- Specialized Coaches Node (Dispatcher) ---")
    routing_decision = state.get("routing_decision")

    if not routing_decision or not isinstance(routing_decision, str):
        print("No valid routing decision found. Defaulting to clarification.")
        return {
            "messages": state.get("messages", [])
            + [
                AIMessage(
                    content="I'd like to help you better. Could you provide more details about what you're looking for?",
                    name="head_coach",
                )
            ]
        }

    print(f"Routing to: {routing_decision}")

    # Handle clarification case - route back to head coach for clarifying questions
    if routing_decision == "clarification":
        print("Routing to clarification - will use head coach clarification logic")
        from .head_coach import clarification_node

        return await clarification_node(state, config)

    # Handle head_coach case
    elif routing_decision == "head_coach":
        print("Routing back to head coach")
        from .head_coach import head_coach_node

        return await head_coach_node(state, config)

    # Handle specialist coaches
    elif routing_decision == "strength_coach":
        return await strength_coach_node(state, config)
    elif routing_decision == "running_coach":
        return await running_coach_node(state, config)
    elif routing_decision == "cycling_coach":
        return await cycling_coach_node(state, config)
    elif routing_decision == "nutrition_coach":
        return await nutrition_coach_node(state, config)
    elif routing_decision == "recovery_coach":
        return await recovery_coach_node(state, config)
    elif routing_decision == "mental_coach":
        return await mental_coach_node(state, config)
    else:
        print(f"Unknown routing decision: {routing_decision}. Using clarification.")
        from .head_coach import clarification_node

        return await clarification_node(state, config)


async def create_airtable_mcp_tools():
    """Create Airtable MCP tools with circuit breaker pattern."""
    # Temporarily disabled
    print("⚠ Airtable tools temporarily disabled")
    return []


# Individual graph creation functions for LangGraph Studio - temporarily disabled
# from langgraph.graph import StateGraph, END
# from athlea_langgraph.states import AgentState


# async def create_strength_coach_graph(config: Optional[Dict[str, Any]] = None):
#     """Create a standalone strength coach graph for LangGraph Studio."""
#     # Temporarily disabled due to tools dependency
#     pass

# All other graph creation functions temporarily disabled...
