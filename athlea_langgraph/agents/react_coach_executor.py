"""
ReAct Coach Executor

Implementation of the ReAct (Reasoning and Acting) pattern for specialized coaching agents.
This follows the industry standard Thought → Action → Observation cycle to properly handle
tool execution and multi-step reasoning.

Based on the ReAct pattern from <PERSON> et al. and implementations in frameworks like CrewAI.
"""

import json
import logging
import re
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
import asyncio

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.tools import BaseTool

from ..services.azure_openai_service import create_azure_chat_openai

logger = logging.getLogger(__name__)


class ReActCoachExecutor:
    """
    ReAct pattern executor for specialized coaching agents.

    Implements the Thought → Action → Observation cycle:
    1. Thought: Agent reasons about what to do next
    2. Action: Agent calls a specific tool with input
    3. Observation: Tool result is fed back to the agent
    4. Repeat until Final Answer is reached
    """

    def __init__(
        self,
        coach_name: str,
        coach_prompt: str,
        tools: List[BaseTool],
        max_iterations: int = 10,
        temperature: float = 0.7,
    ):
        self.coach_name = coach_name
        self.coach_prompt = coach_prompt
        self.tools = tools
        self.max_iterations = max_iterations
        self.temperature = temperature
        self.tool_call_history = []  # Track tool calls made during execution

        # Create tool mapping for easy lookup
        self.tool_map = {tool.name: tool for tool in tools}

        # Create LLM
        self.llm = create_azure_chat_openai(
            temperature=temperature, max_tokens=4000, streaming=True
        )

    def _create_react_prompt(
        self, user_profile: Optional[Dict[str, Any]] = None
    ) -> str:
        """Use the coach prompt provided by the domain agent as the source of truth."""

        # The domain agent has already crafted a domain-specific prompt with ReAct instructions
        # We should use that as-is, not override it with a generic template
        if not self.coach_prompt:
            # Fallback only if no prompt was provided
            return """You are a helpful coach. Use the ReAct format:
Action: [tool_name]
Action Input: [tool input as JSON]"""

        # Add tool descriptions to the domain-specific prompt
        if self.tools:
            available_tools = []
            for tool in self.tools:
                available_tools.append(f"- {tool.name}: {tool.description}")

            tools_section = "\n".join(available_tools)

            # Append tool information to the domain agent's prompt
            enhanced_prompt = f"""{self.coach_prompt}

**AVAILABLE TOOLS:**
{tools_section}

Remember to use the exact ReAct JSON format specified above when calling tools."""

            return enhanced_prompt
        else:
            # No tools available, use the domain prompt as-is
            return self.coach_prompt

    def _parse_action(self, text: str) -> Optional[Tuple[str, str]]:
        """
        Parse action and action input from LLM response.
        Supports both traditional ReAct format and JSON ReAct format.

        Returns:
            Tuple of (action_name, action_input) or None if no action found
        """
        # First, try to parse JSON format used by domain agents
        try:
            # Look for JSON blocks in the response
            json_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            json_matches = re.findall(json_pattern, text, re.DOTALL)

            for json_str in json_matches:
                try:
                    parsed = json.loads(json_str)
                    if isinstance(parsed, dict) and "action" in parsed:
                        action_name = parsed["action"]
                        action_input = parsed.get("action_input", {})

                        # Convert action_input to JSON string if it's a dict
                        if isinstance(action_input, dict):
                            action_input_str = json.dumps(action_input)
                        else:
                            action_input_str = str(action_input)

                        logger.info(f"Parsed JSON ReAct format - Action: {action_name}")
                        return action_name, action_input_str
                except json.JSONDecodeError:
                    continue
        except Exception as e:
            logger.debug(f"JSON parsing failed: {e}, trying traditional format")

        # Fall back to traditional ReAct format
        action_pattern = r"Action:\s*([^\n]+)"
        # Improved input pattern to handle nested JSON and multi-line content
        input_pattern = r"Action Input:\s*({(?:[^{}]|{[^{}]*})*}|\[(?:[^\[\]]|\[[^\[\]]*\])*\]|[^\n]+)"

        action_match = re.search(action_pattern, text, re.IGNORECASE)
        input_match = re.search(input_pattern, text, re.IGNORECASE | re.DOTALL)

        if action_match and input_match:
            action_name = action_match.group(1).strip()
            action_input = input_match.group(1).strip()

            # Additional validation for JSON structure
            if action_input.startswith("{") and not action_input.endswith("}"):
                # Try to find the complete JSON object using brace counting
                action_input = self._extract_complete_json(text, input_match.start(1))

            logger.info(f"Parsed traditional ReAct format - Action: {action_name}")
            return action_name, action_input

        return None

    def _extract_complete_json(self, text: str, start_pos: int) -> str:
        """
        Extract a complete JSON object starting from the given position.
        Uses brace counting to ensure we get the complete object.
        """
        if start_pos >= len(text) or text[start_pos] != "{":
            return text[start_pos : start_pos + 100]  # Fallback

        brace_count = 0
        i = start_pos
        in_string = False
        escaped = False

        while i < len(text):
            char = text[i]

            if escaped:
                escaped = False
            elif char == "\\":
                escaped = True
            elif char == '"' and not escaped:
                in_string = not in_string
            elif not in_string:
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        return text[start_pos : i + 1]

            i += 1

        # If we couldn't find a complete object, return what we have
        return text[start_pos:i]

    async def _execute_tool(self, tool_name: str, tool_input: str) -> str:
        """Execute a tool and return the result."""
        if tool_name not in self.tool_map:
            return f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tool_map.keys())}"

        tool = self.tool_map[tool_name]

        try:
            # Parse and preprocess tool input
            parsed_input = self._parse_and_preprocess_input(tool_name, tool_input)

            # Debug logging
            logger.info(f"Tool {tool_name} - Raw input: {tool_input[:200]}...")
            logger.info(f"Tool {tool_name} - Parsed input: {parsed_input}")

            # Track the tool call
            tool_call_record = {
                "tool_name": tool_name,
                "raw_input": tool_input,
                "parsed_input": parsed_input,
                "timestamp": str(datetime.now()),
            }

            # Execute the tool (try async first, then sync)
            try:
                if hasattr(tool, "ainvoke"):
                    result = await tool.ainvoke(parsed_input)
                elif hasattr(tool, "_arun"):
                    result = await tool._arun(**parsed_input)
                else:
                    result = tool.invoke(parsed_input)
            except Exception as async_error:
                # Fallback to sync execution
                logger.warning(
                    f"Async execution failed for {tool_name}, trying sync: {async_error}"
                )
                result = tool.invoke(parsed_input)

            # Record the result using our enhanced tracking
            self.track_tool_call(tool_name, success=True, result=result)

            # Also maintain the old format for compatibility
            tool_call_record["result"] = str(result)
            tool_call_record["success"] = True
            self.tool_call_history.append(tool_call_record)

            return str(result)

        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            error_message = f"Error executing {tool_name}: {str(e)}"

            # Record the failed tool call using our enhanced tracking
            self.track_tool_call(tool_name, success=False, error=str(e))

            # Also maintain the old format for compatibility
            tool_call_record = {
                "tool_name": tool_name,
                "raw_input": tool_input,
                "error": str(e),
                "success": False,
                "timestamp": str(datetime.now()),
            }
            self.tool_call_history.append(tool_call_record)

            return error_message

    def _parse_and_preprocess_input(
        self, tool_name: str, tool_input: str
    ) -> Dict[str, Any]:
        """Parse and preprocess tool input to handle common formatting issues."""
        logger.debug(f"[{tool_name}] Raw tool input: {repr(tool_input)}")

        # Check for already wrapped query format and unwrap if needed
        if tool_input.strip().startswith('{"query":') and tool_input.strip().endswith(
            "}"
        ):
            try:
                wrapped_input = json.loads(tool_input)
                if "query" in wrapped_input and isinstance(wrapped_input["query"], str):
                    logger.warning(
                        f"[{tool_name}] Input was already wrapped in query format, unwrapping..."
                    )
                    # Try to parse the query value as JSON
                    try:
                        unwrapped = json.loads(wrapped_input["query"])
                        if isinstance(unwrapped, dict):
                            logger.info(
                                f"[{tool_name}] Successfully unwrapped nested JSON"
                            )
                            tool_input = wrapped_input["query"]
                        else:
                            logger.debug(
                                f"[{tool_name}] Query value is not a dict, keeping wrapped format"
                            )
                    except json.JSONDecodeError:
                        logger.debug(
                            f"[{tool_name}] Query value is not valid JSON, keeping wrapped format"
                        )
            except json.JSONDecodeError:
                logger.debug(
                    f"[{tool_name}] Failed to parse wrapped input, proceeding normally"
                )

        try:
            # First, try to parse as JSON
            parsed_input = json.loads(tool_input)
            logger.debug(
                f"[{tool_name}] JSON parsing successful, type: {type(parsed_input)}"
            )

            # If it's a dict, use it directly (don't wrap in query)
            if isinstance(parsed_input, dict):
                logger.debug(
                    f"[{tool_name}] Input is dict, applying tool-specific preprocessing"
                )
                # Apply tool-specific preprocessing
                parsed_input = self._preprocess_tool_specific_input(
                    tool_name, parsed_input
                )
                logger.debug(f"[{tool_name}] Final processed input: {parsed_input}")
                return parsed_input
            else:
                # Only wrap non-dict values in query
                logger.debug(
                    f"[{tool_name}] Input is not dict, wrapping in query format"
                )
                result = {"query": str(parsed_input)}
                logger.debug(f"[{tool_name}] Wrapped result: {result}")
                return result

        except json.JSONDecodeError as e:
            logger.debug(
                f"[{tool_name}] JSON parsing failed: {e}, trying to fix common issues"
            )
            # If JSON parsing fails, try to fix common issues
            fixed_input = self._fix_common_json_issues(tool_input)
            logger.debug(f"[{tool_name}] Fixed input: {repr(fixed_input)}")

            try:
                parsed_input = json.loads(fixed_input)
                logger.debug(
                    f"[{tool_name}] Fixed JSON parsing successful, type: {type(parsed_input)}"
                )
                if isinstance(parsed_input, dict):
                    # Apply tool-specific preprocessing
                    parsed_input = self._preprocess_tool_specific_input(
                        tool_name, parsed_input
                    )
                    logger.debug(
                        f"[{tool_name}] Final processed fixed input: {parsed_input}"
                    )
                    return parsed_input
                else:
                    logger.debug(
                        f"[{tool_name}] Fixed input is not dict, wrapping in query format"
                    )
                    result = {"query": str(parsed_input)}
                    logger.debug(f"[{tool_name}] Wrapped fixed result: {result}")
                    return result

            except json.JSONDecodeError as e2:
                logger.debug(
                    f"[{tool_name}] Fixed JSON parsing also failed: {e2}, treating as string"
                )
                # If all else fails, treat as string input
                result = {"query": tool_input}
                logger.debug(f"[{tool_name}] Final string fallback result: {result}")
                return result

    def _fix_common_json_issues(self, tool_input: str) -> str:
        """Fix common JSON formatting issues."""
        # Remove any leading/trailing whitespace
        fixed = tool_input.strip()

        # Fix single quotes to double quotes
        fixed = fixed.replace("'", '"')

        # Fix common comma-separated string to array conversion
        # Look for patterns like: "equipment": "dumbbells, barbell, bench"
        import re

        # Pattern to find string values that look like comma-separated lists
        pattern = r'"([^"]+)":\s*"([^"]+,\s*[^"]+)"'

        def replace_comma_separated(match):
            key = match.group(1)
            value = match.group(2)

            # Convert comma-separated string to JSON array
            items = [item.strip() for item in value.split(",")]
            json_array = json.dumps(items)
            return f'"{key}": {json_array}'

        fixed = re.sub(pattern, replace_comma_separated, fixed)

        # Fix empty list [] when it should be empty object {}
        # This is common for preferences field
        fixed = re.sub(r'"preferences":\s*\[\s*\]', '"preferences": {}', fixed)

        return fixed

    def _preprocess_tool_specific_input(
        self, tool_name: str, parsed_input: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply tool-specific preprocessing to handle known input patterns."""

        if tool_name == "session_generation":
            # Handle session generation specific preprocessing

            # Transform from SessionGenerationToolInput to SessionGenerationInput format
            if "duration_minutes" in parsed_input:
                parsed_input["duration"] = parsed_input.pop("duration_minutes")

            # Convert string equipment to list if needed (then remove as it's not in SessionGenerationInput)
            if "equipment" in parsed_input and isinstance(
                parsed_input["equipment"], str
            ):
                equipment_str = parsed_input["equipment"]
                if "," in equipment_str:
                    parsed_input["equipment"] = [
                        item.strip() for item in equipment_str.split(",")
                    ]
                else:
                    parsed_input["equipment"] = [equipment_str.strip()]

            # Convert string goals to list if needed (then remove as it's not in SessionGenerationInput)
            if "goals" in parsed_input and isinstance(parsed_input["goals"], str):
                goals_str = parsed_input["goals"]
                if "," in goals_str:
                    parsed_input["goals"] = [
                        item.strip() for item in goals_str.split(",")
                    ]
                else:
                    parsed_input["goals"] = [goals_str.strip()]

            # Ensure preferences is a dict (then remove as it's not in SessionGenerationInput)
            if "preferences" in parsed_input:
                if (
                    parsed_input["preferences"] is None
                    or parsed_input["preferences"] == []
                ):
                    parsed_input["preferences"] = {}
                elif isinstance(parsed_input["preferences"], str):
                    # Try to parse as JSON, otherwise create empty dict
                    try:
                        parsed_input["preferences"] = json.loads(
                            parsed_input["preferences"]
                        )
                    except:
                        parsed_input["preferences"] = {}

            # Remove fields that don't exist in SessionGenerationInput schema
            # (These were processed above for validation but aren't part of the target schema)
            fields_to_remove = ["equipment", "goals", "experience_level", "preferences"]
            for field in fields_to_remove:
                parsed_input.pop(field, None)

            # Set default values for required fields if missing
            if "command" not in parsed_input:
                # Try to infer from context or set a default
                parsed_input["command"] = "strength"  # Default fallback

            # Add date field if missing (required by SessionGenerationInput)
            if "date" not in parsed_input:
                parsed_input["date"] = datetime.now().strftime("%Y-%m-%d")

        elif tool_name == "search_strength_exercises":
            # Handle strength exercise search specific preprocessing

            import json

            # The tool expects a JSON string input, not a dictionary
            # This is the key fix for the schema mismatch issue

            # Ensure muscle_groups is a list and equipment is handled properly
            if "muscle_groups" in parsed_input and isinstance(
                parsed_input["muscle_groups"], str
            ):
                parsed_input["muscle_groups"] = [parsed_input["muscle_groups"]]

            if "equipment" in parsed_input and isinstance(
                parsed_input["equipment"], str
            ):
                parsed_input["equipment"] = [parsed_input["equipment"]]

            # Handle difficulty mapping
            if "difficulty" in parsed_input:
                parsed_input["difficulty_level"] = parsed_input.pop("difficulty")

            # Ensure required fields have defaults
            if "muscle_groups" not in parsed_input and "equipment" not in parsed_input:
                parsed_input["muscle_groups"] = ["chest"]

            # The critical fix: Convert the entire dict to JSON string
            # because the LangChain tool expects a single string parameter
            try:
                # Return the processed dict as JSON string
                return json.dumps(parsed_input)
            except Exception as e:
                logger.warning(f"[{tool_name}] Failed to convert to JSON: {e}")
                # Return the dict as-is if JSON conversion fails
                return parsed_input

        elif tool_name == "generate_mobility_protocol":
            # Handle mobility protocol specific preprocessing

            # Map target_area to target_areas (singular to plural)
            if "target_area" in parsed_input:
                target_area = parsed_input.pop("target_area")

                # Map common areas to valid enum values
                area_mapping = {
                    "hip": "upper legs",
                    "hips": "upper legs",
                    "lower_body": "upper legs",
                    "upper_body": "shoulders",
                    "spine": "back",
                    "lower_spine": "lower_back",
                }

                mapped_area = area_mapping.get(target_area.lower(), target_area.lower())
                parsed_input["target_areas"] = [mapped_area]

            # Ensure activity_type is valid
            if "activity_type" not in parsed_input:
                parsed_input["activity_type"] = "general_fitness"
            elif parsed_input["activity_type"] == "mobility":
                parsed_input["activity_type"] = "general_fitness"

        elif tool_name == "airtable_mcp":
            # Handle airtable specific preprocessing

            # Ensure operation is set
            if "operation" not in parsed_input:
                parsed_input["operation"] = "search_records"  # Default operation

            # Handle maxRecords as string
            if "maxRecords" in parsed_input and isinstance(
                parsed_input["maxRecords"], str
            ):
                try:
                    parsed_input["maxRecords"] = int(parsed_input["maxRecords"])
                except ValueError:
                    parsed_input["maxRecords"] = 10  # Default

        return parsed_input

    def _is_final_answer(self, text: str) -> bool:
        """Check if the response contains a final answer."""
        return "Final Answer:" in text

    def _extract_final_answer(self, text: str) -> str:
        """Extract the final answer from the response and clean it up."""
        # Look for "Final Answer:" pattern first
        final_answer_pattern = r"Final Answer:\s*(.+)"
        match = re.search(final_answer_pattern, text, re.IGNORECASE | re.DOTALL)

        if match:
            final_answer = match.group(1).strip()
        else:
            # If no "Final Answer:" pattern, use the whole text
            final_answer = text

        # Clean up any remaining ReAct formatting artifacts
        final_answer = self._clean_react_artifacts(final_answer)

        return final_answer

    def _clean_react_artifacts(self, text: str) -> str:
        """Remove any ReAct formatting artifacts from the final response."""
        # Remove any remaining "Thought:", "Action:", "Action Input:", "Observation:", "Final Answer:" patterns
        patterns_to_remove = [
            r"Thought:\s*[^\n]*\n?",
            r"Action:\s*[^\n]*\n?",
            r"Action Input:\s*[^\n]*\n?",
            r"Observation:\s*[^\n]*\n?",
            r"Final Answer:\s*",  # Remove the "Final Answer:" prefix itself
            r"I now have all the information needed.*?\n",  # Remove transitional phrases
            r"Based on the.*?search results.*?\n",  # Remove search result references
            r"According to the.*?information.*?\n",  # Remove information references
        ]

        cleaned_text = text
        for pattern in patterns_to_remove:
            cleaned_text = re.sub(
                pattern, "", cleaned_text, flags=re.IGNORECASE | re.MULTILINE
            )

        # Remove any lines that look like internal reasoning
        lines = cleaned_text.split("\n")
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            # Skip lines that look like internal reasoning or formatting
            if (
                line.startswith(
                    ("Thought:", "Action:", "Observation:", "Final Answer:")
                )
                or line.lower().startswith(
                    ("i need to", "let me", "i should", "first i will")
                )
                or line in ["", "---", "***"]
            ):
                continue
            cleaned_lines.append(line)

        # Rejoin and clean up extra whitespace
        cleaned_text = "\n".join(cleaned_lines)
        cleaned_text = re.sub(
            r"\n\s*\n", "\n\n", cleaned_text
        )  # Multiple newlines to double
        cleaned_text = cleaned_text.strip()

        # If the result is empty or too short, provide a fallback
        if len(cleaned_text.strip()) < 10:
            cleaned_text = "I'd be happy to help you with that. Could you provide a bit more detail about what you're looking for?"

        return cleaned_text

    async def execute(
        self,
        user_message: str,
        conversation_history: List[BaseMessage],
        user_profile: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Execute the ReAct pattern for the given user message.

        LangGraph will automatically handle token streaming when astream_events v2 is used
        and the LLM has streaming=True.

        Args:
            user_message: The user's query
            conversation_history: Previous conversation messages
            user_profile: User profile information

        Returns:
            Dictionary with response and execution details
        """
        logger.info(f"--- Starting ReAct execution for {self.coach_name} ---")

        # Create system prompt
        system_prompt = self._create_react_prompt(user_profile)
        conversation_messages = [SystemMessage(content=system_prompt)]

        # Add conversation history
        if conversation_history:
            conversation_messages.extend(conversation_history)

        # Add the current user message
        conversation_messages.append(HumanMessage(content=user_message))

        max_iterations = self.max_iterations
        accumulated_response = ""

        for iteration in range(max_iterations):
            logger.info(f"ReAct iteration {iteration + 1}/{max_iterations}")

            try:
                # Use invoke instead of astream - LangGraph will handle the streaming automatically
                # when astream_events v2 is used and streaming=True is set on the LLM
                response = await self.llm.ainvoke(conversation_messages)

                if not response or not hasattr(response, "content"):
                    logger.warning(f"Invalid response from LLM: {response}")
                    continue

                response_content = response.content

                # Process the complete response for ReAct pattern analysis
                if self._is_final_answer(response_content):
                    final_answer = self._extract_final_answer(response_content)
                    logger.info(f"Final answer reached: {final_answer[:100]}...")

                    return {
                        "response": final_answer,
                        "iterations_used": iteration + 1,
                        "success": True,
                        "tool_calls": self.tool_call_history.copy(),
                        "messages": conversation_messages,
                    }

                # Parse action if not final answer
                action_result = self._parse_action(response_content)

                if action_result:
                    action_name, action_input = action_result

                    if action_name == "Final Answer":
                        # Clean the final answer from action parsing as well
                        final_answer = self._clean_react_artifacts(action_input)
                        logger.info(f"Final answer via action: {final_answer[:100]}...")

                        return {
                            "response": final_answer,
                            "iterations_used": iteration + 1,
                            "success": True,
                            "tool_calls": self.tool_call_history.copy(),
                            "messages": conversation_messages,
                        }

                    # Execute tool if action is found
                    tool_result = await self._execute_tool(action_name, action_input)

                    # Add tool interaction to conversation
                    conversation_messages.append(AIMessage(content=response_content))
                    conversation_messages.append(
                        HumanMessage(content=f"Observation: {tool_result}")
                    )

                    accumulated_response += f"\n\nUsed {action_name}: {tool_result}"
                    continue

                # If no action found, treat as final answer
                logger.info(
                    f"No action found, treating as final answer: {response_content[:100]}..."
                )

                # Clean the response content to remove any ReAct artifacts
                cleaned_response = self._clean_react_artifacts(response_content)

                return {
                    "response": cleaned_response,
                    "iterations_used": iteration + 1,
                    "success": True,
                    "tool_calls": self.tool_call_history.copy(),
                    "messages": conversation_messages,
                }

            except Exception as e:
                logger.error(f"Error in iteration {iteration + 1}: {e}")
                if iteration == max_iterations - 1:
                    # Final iteration - return error response
                    error_response = (
                        f"I apologize, but I encountered an error: {str(e)}"
                    )
                    return {
                        "response": error_response,
                        "iterations_used": iteration + 1,
                        "success": False,
                        "error": str(e),
                        "messages": conversation_messages,
                    }
                # Continue to next iteration
                continue

        # Max iterations reached
        fallback_response = (
            accumulated_response
            or f"I've analyzed your question about {user_message} but need more specific information to provide a complete answer."
        )

        # Clean the fallback response as well
        fallback_response = self._clean_react_artifacts(fallback_response)

        return {
            "response": fallback_response,
            "iterations_used": max_iterations,
            "success": False,
            "reason": "max_iterations_reached",
            "messages": conversation_messages,
        }

    def get_tool_call_history(self) -> List[Dict[str, Any]]:
        """Get the history of tool calls made during execution."""
        # For now, return empty list since we don't track this yet
        # This can be enhanced later to track actual tool calls
        return []

    def track_tool_call(
        self, tool_name: str, success: bool, result: Any = None, error: str = None
    ):
        """Track a tool call for monitoring and debugging."""
        call_record = {
            "timestamp": asyncio.get_event_loop().time(),
            "tool_name": tool_name,
            "success": success,
            "result_length": len(str(result)) if result else 0,
            "error": error,
            "coach_name": self.coach_name,
        }
        self.tool_call_history.append(call_record)

        # Also update any domain agent counters if they exist
        domain_agent_name = f"{self.coach_name.replace('_coach', '')}_agent"
        try:
            # Try to access the domain agent and update its counters
            import sys

            if hasattr(sys.modules.get("athlea_langgraph.agents"), domain_agent_name):
                domain_agent = getattr(
                    sys.modules["athlea_langgraph.agents"], domain_agent_name
                )
                if hasattr(domain_agent, "tool_calls_count"):
                    domain_agent.tool_calls_count += 1
                if hasattr(domain_agent, "last_tools_called"):
                    domain_agent.last_tools_called.append(tool_name)
                    # Keep only last 10 tool calls
                    if len(domain_agent.last_tools_called) > 10:
                        domain_agent.last_tools_called = domain_agent.last_tools_called[
                            -10:
                        ]
        except Exception:
            pass  # Ignore errors in domain agent tracking


async def create_react_coach_node(
    coach_name: str,
    coach_prompt: str,
    tools: List[BaseTool],
    max_iterations: int = 10,
):
    """
    Create a ReAct-enabled coach node function.

    Args:
        coach_name: Name of the coach
        coach_prompt: System prompt for the coach
        tools: List of tools available to the coach
        max_iterations: Maximum ReAct iterations

    Returns:
        Async function that can be used as a node in the graph
    """

    executor = ReActCoachExecutor(
        coach_name=coach_name,
        coach_prompt=coach_prompt,
        tools=tools,
        max_iterations=max_iterations,
    )

    async def react_coach_node(
        state, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """ReAct-enabled coach node implementation."""

        logger.info(f"--- Running ReAct {coach_name} Node ---")

        # Extract configuration
        config = config or {}
        user_profile = config.get("userProfile") or state.get("user_profile")
        messages = state.get("messages", [])

        # Get the latest user message
        user_message = None
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                user_message = msg.content
                break

        if not user_message:
            return {
                "messages": [
                    AIMessage(
                        content="I didn't receive a clear question. How can I help you?",
                        name=coach_name,
                    )
                ]
            }

        try:
            # Execute ReAct pattern
            result = await executor.execute(
                user_message=user_message,
                conversation_history=messages,
                user_profile=user_profile,
            )

            if result["success"]:
                response_message = AIMessage(
                    content=result["final_answer"],
                    name=coach_name,
                )

                # Add ReAct trace to response metadata if needed for debugging
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        f"ReAct trace for {coach_name}: {result['react_trace']}"
                    )

                return {"messages": [response_message]}
            else:
                error_message = AIMessage(
                    content=f"I apologize, but I encountered an error while processing your request: {result.get('error', 'Unknown error')}",
                    name=coach_name,
                )
                return {"messages": [error_message]}

        except Exception as e:
            logger.error(f"Error in ReAct {coach_name} node: {e}")
            error_message = AIMessage(
                content="I apologize, but I encountered an unexpected error. Please try again.",
                name=coach_name,
            )
            return {"messages": [error_message]}

    return react_coach_node
