"""
GraphRAG Integration Nodes for Athlea Coaching System

This module implements GraphRAG (Graph-based Retrieval-Augmented Generation) nodes
that integrate with Azure Cognitive Search and Cosmos DB Gremlin for enhanced
knowledge retrieval in the coaching system.

Based on Neo4j GraphRAG patterns adapted for Athlea's multi-agent architecture.
"""

import logging
import re
from typing import Any, Dict, List, Optional

from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from ..utils.prompt_loader import PromptLoader

logger = logging.getLogger(__name__)


class GraphRAGState(BaseModel):
    """Extended state fields for GraphRAG functionality."""

    # Knowledge retrieval assessment
    needs_knowledge_retrieval: Optional[bool] = None
    knowledge_confidence: Optional[float] = None
    knowledge_assessment_reasoning: Optional[str] = None

    # GraphRAG query and routing
    graphrag_query: Optional[str] = None
    retrieval_type: Optional[str] = None  # "vector", "graph", "hybrid", "none"
    query_complexity: Optional[str] = None  # "simple", "moderate", "complex"

    # Retrieved knowledge
    graphrag_results: Dict[str, Any] = Field(default_factory=dict)
    vector_results: List[Dict[str, Any]] = Field(default_factory=list)
    graph_results: List[Dict[str, Any]] = Field(default_factory=list)
    knowledge_context: Optional[str] = None
    knowledge_sources: List[str] = Field(default_factory=list)

    # Query decomposition (for complex queries)
    decomposed_queries: List[str] = Field(default_factory=list)
    query_synthesis: Optional[str] = None


class GraphRAGRetriever:
    """
    GraphRAG retrieval system integrating Azure Cognitive Search and Cosmos DB Gremlin.

    This class implements the retrieval logic similar to the Neo4j GraphRAG example
    but adapted for Athlea's Azure-based infrastructure.
    """

    def __init__(self):
        self.prompt_loader = PromptLoader("athlea_langgraph/prompts")
        self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.1, streaming=False)

    async def assess_knowledge_needs(
        self, user_query: str, coach_domain: str = "fitness"
    ) -> Dict[str, Any]:
        """
        Dynamically assess if the user query requires external knowledge retrieval.

        Uses LLM-based assessment without keyword checking for intelligent evaluation.
        """
        try:
            # Load knowledge assessment prompt
            knowledge_prompt_config = await self.prompt_loader.load_prompt(
                "rag/knowledge_assessment"
            )

            # Format the assessment prompt with just the domain and query
            prompt = knowledge_prompt_config.prompt.system.format(
                coach_domain=coach_domain,
                user_query=user_query,
            )

            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            assessment_text = response.content

            logger.info(
                f"🧠 GRAPHRAG: [ASSESSMENT] LLM Response: {assessment_text[:200]}..."
            )

            # Parse the response
            needs_retrieval = False
            confidence = 0.5
            reasoning = "Assessment completed"

            lines = assessment_text.split("\n")
            for line in lines:
                line = line.strip()
                if line.startswith("NEEDS_KNOWLEDGE:"):
                    needs_retrieval = line.split(":")[1].strip().lower() == "true"
                elif line.startswith("CONFIDENCE:"):
                    try:
                        confidence = float(line.split(":")[1].strip())
                    except ValueError:
                        pass
                elif line.startswith("REASONING:"):
                    reasoning = line.split(":", 1)[1].strip()

            logger.info(f"🎯 GRAPHRAG: [ASSESSMENT] Parsed result:")
            logger.info(f"  - Needs Knowledge: {needs_retrieval}")
            logger.info(f"  - Confidence: {confidence}")
            logger.info(f"  - Reasoning: {reasoning[:100]}...")

            return {
                "needs_knowledge_retrieval": needs_retrieval,
                "knowledge_confidence": confidence,
                "knowledge_assessment_reasoning": reasoning,
            }

        except Exception as e:
            logger.error(f"Error in knowledge assessment: {e}")
            return {
                "needs_knowledge_retrieval": False,
                "knowledge_confidence": 0.3,
                "knowledge_assessment_reasoning": f"Error in assessment: {str(e)}",
            }

    async def route_retrieval_type(
        self, user_query: str, coach_domain: str = "fitness"
    ) -> str:
        """
        Dynamically route the query to appropriate retrieval type using LLM analysis.

        Returns "vector", "graph", or "hybrid" based on intelligent query analysis.
        """
        try:
            # Use a simple LLM prompt to determine retrieval type
            routing_prompt = f"""You are a retrieval router for a fitness coaching knowledge system. 

Analyze this query and determine the best retrieval strategy:

"{user_query}"

Choose one of these retrieval types:

**VECTOR**: For semantic similarity searches, finding similar content, examples, or general information
- Example: "Show me exercises similar to squats"
- Example: "What are good cardio alternatives?"

**GRAPH**: For relationship-based queries, specific facts, structured data retrieval
- Example: "What muscles does the deadlift target?"
- Example: "List all exercises for chest development"  

**HYBRID**: For complex queries requiring both semantic search and relationship traversal
- Example: "Design a comprehensive strength program for beginners"
- Example: "Create an evidence-based nutrition plan for weight loss"

Respond with only one word: VECTOR, GRAPH, or HYBRID"""

            response = await self.llm.ainvoke([HumanMessage(content=routing_prompt)])
            retrieval_type = response.content.strip().upper()

            # Validate response and map to lowercase
            if retrieval_type in ["VECTOR", "GRAPH", "HYBRID"]:
                return retrieval_type.lower()
            else:
                # Fallback based on query characteristics
                query_words = len(user_query.split())
                if query_words > 15:
                    return "hybrid"
                elif query_words > 8:
                    return "vector"
                else:
                    return "vector"

        except Exception as e:
            logger.error(f"Error in retrieval routing: {e}")
            # Simple fallback based on query length
            return "hybrid" if len(user_query.split()) > 10 else "vector"

    async def decompose_query(
        self, user_query: str, coach_domain: str = "fitness"
    ) -> List[str]:
        """
        Decompose complex queries into sub-queries for better retrieval.

        Similar to the Neo4j example's query decomposition.
        """
        try:
            # Load query decomposition prompt
            decomposition_prompt_config = await self.prompt_loader.load_prompt(
                "rag/query_decomposition"
            )

            prompt = decomposition_prompt_config.prompt.system.format(
                coach_domain=coach_domain, user_query=user_query
            )

            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            decomposition_text = response.content

            # Parse sub-queries (expect numbered list)
            sub_queries = []
            lines = decomposition_text.split("\n")
            for line in lines:
                # Look for numbered items like "1. ", "2. ", etc.
                if re.match(r"^\d+\.\s+", line.strip()):
                    query = re.sub(r"^\d+\.\s+", "", line.strip())
                    if query:
                        sub_queries.append(query)

            # Fallback: if no numbered items found, return original query
            if not sub_queries:
                sub_queries = [user_query]

            return sub_queries

        except Exception as e:
            logger.error(f"Error in query decomposition: {e}")
            return [user_query]  # Fallback to original query

    async def vector_search(
        self, query: str, coach_domain: str = "fitness"
    ) -> List[Dict[str, Any]]:
        """
        Perform vector search using Azure Cognitive Search.
        """
        logger.info(f"🔍 Vector search for: {query} in domain: {coach_domain}")

        try:
            # Import Azure Search tools
            from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool

            # Create Azure Search retriever
            azure_search = AzureSearchRetrieverTool()

            # Perform the search using the correct method
            search_result = await azure_search.invoke(
                {
                    "query": query,
                    "index_name": "docs-chunks-index-v2",
                    "top_k": 10,
                    "vector_search": False,
                    "hybrid_search": False,
                    "filter_expression": None,
                }
            )

            if search_result.success and search_result.results:
                results = []
                for result in search_result.results[:5]:  # Limit to top 5
                    formatted_result = {
                        "content": result.content or "",
                        "source": result.source or "Unknown",
                        "title": result.title or "",
                        "url": result.url or "",
                        "relevance_score": result.score or 0.0,
                        "metadata": {
                            "type": "vector_search",
                            "domain": coach_domain,
                            "chunk_id": result.chunk_id or "",
                            "created_at": (
                                getattr(result.metadata, "created_at", "")
                                if result.metadata
                                else ""
                            ),
                        },
                    }
                    results.append(formatted_result)

                logger.info(
                    f"✅ Vector search successful: {len(results)} results found"
                )
                return results
            else:
                if not search_result.success:
                    logger.warning(f"Vector search failed: {search_result.message}")
                else:
                    logger.warning("Vector search returned no results")
                return []

        except Exception as e:
            logger.error(f"❌ Vector search failed: {e}")
            # DO NOT fall back to mock data - raise the error
            raise Exception(f"Vector search service unavailable: {str(e)}")

    async def graph_search(
        self, query: str, coach_domain: str = "fitness"
    ) -> List[Dict[str, Any]]:
        """
        Perform graph-based traversal using Cosmos DB Gremlin.

        Integrates with your existing Cosmos DB Gremlin setup.
        """
        logger.info(f"🕸️ Graph search for: {query} in domain: {coach_domain}")

        try:
            # Import your Gremlin service
            from ..services.gremlin_service import get_gremlin_service
            from ..services.gremlin_service import GremlinQueryInput

            # Get Gremlin service instance
            gremlin_service = get_gremlin_service()

            # Extract key entities from the query for graph traversal
            entities = self._extract_entities_from_query(query)

            if not entities:
                logger.info(
                    "No entities found in query, performing general graph search"
                )
                entities = ["exercise", "training", "fitness"]  # Fallback entities

            # Perform graph searches for each entity
            all_results = []

            for entity in entities[:3]:  # Limit to 3 entities to avoid too many queries
                try:
                    # Search for entity relationships
                    from athlea_langgraph.services.gremlin_service import (
                        GremlinQueryInput,
                    )

                    query_input = GremlinQueryInput(
                        query=f"""
                        g.V().has('name', '{entity}').as('entity')
                         .project('entity_details', 'relationships')
                         .by(valueMap(true))
                         .by(
                             bothE().limit(5).as('edge')
                             .otherV().as('connected_node')
                             .select('edge', 'connected_node')
                             .by(valueMap(true))
                             .by(valueMap(true))
                             .fold()
                         )
                         .limit(3)
                        """
                    )

                    result = await gremlin_service.execute_query(query_input)

                    if result.success and result.results:
                        # Convert Gremlin results to GraphRAG format
                        for gremlin_result in result.results:
                            converted_result = {
                                "entity": entity,
                                "relationships": self._format_gremlin_relationships(
                                    gremlin_result
                                ),
                                "source": "Cosmos DB Knowledge Graph",
                                "confidence": 0.90,
                                "metadata": {
                                    "type": "graph_traversal",
                                    "domain": coach_domain,
                                    "entity_searched": entity,
                                    "gremlin_query": query_input.query,
                                },
                            }
                            all_results.append(converted_result)

                        logger.info(
                            f"✅ Graph search for '{entity}': {len(result.results)} results"
                        )
                    else:
                        logger.info(f"No graph results found for entity: {entity}")

                except Exception as entity_error:
                    logger.warning(
                        f"Graph search failed for entity '{entity}': {entity_error}"
                    )
                    continue

            if all_results:
                logger.info(
                    f"✅ Graph search successful: {len(all_results)} total results found"
                )
                return all_results
            else:
                # Try a general search if entity searches failed
                return await self._general_graph_search(
                    gremlin_service, query, coach_domain
                )

        except Exception as e:
            logger.error(f"❌ Graph search failed: {e}")
            # DO NOT fall back to mock data - return empty results with clear indication
            logger.warning("Graph search service unavailable - returning empty results")
            return []

    def _extract_entities_from_query(self, query: str) -> List[str]:
        """Extract potential entity names from the query for graph search."""
        # Simple entity extraction - in production you might use NER
        fitness_keywords = [
            "squat",
            "deadlift",
            "bench press",
            "pull-up",
            "push-up",
            "running",
            "cycling",
            "swimming",
            "yoga",
            "pilates",
            "cardio",
            "strength",
            "hiit",
            "crossfit",
            "powerlifting",
            "bodybuilding",
            "endurance",
            "flexibility",
            "mobility",
            "nutrition",
            "protein",
            "carbohydrates",
            "fats",
            "vitamins",
            "recovery",
            "sleep",
            "rest",
            "massage",
            "stretching",
            "injury",
            "prevention",
            "rehabilitation",
            "physical therapy",
            "FIFA 11+",
            "ACL",
            "ankle sprain",
            "hamstring",
            "quadriceps",
        ]

        query_lower = query.lower()
        found_entities = []

        for keyword in fitness_keywords:
            if keyword.lower() in query_lower:
                found_entities.append(keyword)

        return found_entities[:5]  # Limit to 5 entities

    def _format_gremlin_relationships(self, gremlin_result: Any) -> str:
        """Format Gremlin query results into readable relationship text."""
        try:
            if isinstance(gremlin_result, dict):
                if "relationships" in gremlin_result:
                    relationships = gremlin_result["relationships"]
                    if isinstance(relationships, list) and relationships:
                        # Format the relationships into readable text
                        formatted = []
                        for rel in relationships[:3]:  # Limit to 3 relationships
                            if isinstance(rel, dict):
                                edge_info = rel.get("edge", {})
                                node_info = rel.get("connected_node", {})

                                edge_label = (
                                    edge_info.get("label", ["RELATED"])[0]
                                    if isinstance(edge_info.get("label"), list)
                                    else edge_info.get("label", "RELATED")
                                )
                                node_name = (
                                    node_info.get("name", ["Unknown"])[0]
                                    if isinstance(node_info.get("name"), list)
                                    else node_info.get("name", "Unknown")
                                )

                                formatted.append(f"{edge_label}: {node_name}")

                        return "; ".join(formatted)

                # If no relationships structure, return a summary of the result
                entity_details = gremlin_result.get("entity_details", {})
                entity_name = (
                    entity_details.get("name", ["Unknown"])[0]
                    if isinstance(entity_details.get("name"), list)
                    else entity_details.get("name", "Unknown")
                )
                return f"Entity found: {entity_name}"

            return str(gremlin_result)[:200]  # Fallback to string representation
        except Exception as e:
            logger.warning(f"Error formatting Gremlin relationships: {e}")
            return "Unable to format relationship data"

    async def _general_graph_search(
        self, gremlin_service, query: str, coach_domain: str
    ) -> List[Dict[str, Any]]:
        """Perform a general graph search when entity-specific searches fail."""
        try:
            from athlea_langgraph.services.gremlin_service import GremlinQueryInput

            # General query to get some sample data from the graph
            query_input = GremlinQueryInput(
                query="""
                g.V().limit(5).project('type', 'properties', 'connections')
                 .by(label())
                 .by(valueMap(true))
                 .by(bothE().limit(3).otherV().valueMap('name').fold())
                """
            )

            result = await gremlin_service.execute_query(query_input)

            if result.success and result.results:
                converted_results = []
                for gremlin_result in result.results:
                    converted_result = {
                        "relationships": f"General graph data: {str(gremlin_result)[:100]}...",
                        "source": "Cosmos DB Knowledge Graph",
                        "confidence": 0.70,
                        "metadata": {
                            "type": "general_graph_search",
                            "domain": coach_domain,
                            "query": query,
                        },
                    }
                    converted_results.append(converted_result)

                logger.info(
                    f"✅ General graph search: {len(converted_results)} results"
                )
                return converted_results
            else:
                logger.warning("General graph search returned no results")
                return []

        except Exception as e:
            logger.error(f"General graph search failed: {e}")
            return []

    # Mock data functions removed - we should never return fake data in production

    async def hybrid_search(
        self, query: str, coach_domain: str = "fitness"
    ) -> Dict[str, Any]:
        """
        Perform combined vector and graph search for comprehensive results.
        """
        logger.info(f"🔗 Hybrid search for: {query} in domain: {coach_domain}")

        # Run both searches in parallel for maximum efficiency
        try:
            import asyncio

            vector_task = self.vector_search(query, coach_domain)
            graph_task = self.graph_search(query, coach_domain)

            vector_results, graph_results = await asyncio.gather(
                vector_task, graph_task, return_exceptions=True
            )

            # Handle exceptions gracefully
            if isinstance(vector_results, Exception):
                logger.error(f"Vector search failed: {vector_results}")
                vector_results = []

            if isinstance(graph_results, Exception):
                logger.error(f"Graph search failed: {graph_results}")
                graph_results = []

            # Ensure results are lists
            if not isinstance(vector_results, list):
                vector_results = []
            if not isinstance(graph_results, list):
                graph_results = []

            # Log the successful hybrid search
            total_results = len(vector_results) + len(graph_results)
            logger.info(
                f"✅ Hybrid search complete: {len(vector_results)} vector + {len(graph_results)} graph = {total_results} total results"
            )

            return {
                "vector_results": vector_results,
                "graph_results": graph_results,
                "total_results": total_results,
                "hybrid_score": 0.88,
                "search_query": query,
                "coach_domain": coach_domain,
            }

        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return {
                "vector_results": [],
                "graph_results": [],
                "total_results": 0,
                "hybrid_score": 0.0,
                "error": str(e),
            }

    async def synthesize_knowledge(
        self,
        vector_results: List[Dict[str, Any]],
        graph_results: List[Dict[str, Any]],
        user_query: str,
        coach_domain: str = "fitness",
    ) -> str:
        """
        Synthesize retrieved knowledge into coherent context for coaching.
        """
        # If no results found, return clear indication instead of generating content
        if not vector_results and not graph_results:
            logger.warning(f"🚫 GRAPHRAG: No search results found for synthesis")
            return (
                f"No relevant research data found in knowledge base for: {user_query}"
            )

        try:
            # Load knowledge synthesis prompt
            synthesis_prompt_config = await self.prompt_loader.load_prompt(
                "rag/knowledge_synthesis"
            )

            # Prepare content for synthesis - only include non-empty results
            vector_content = "\n".join(
                [
                    f"- {result.get('content', '')}"
                    for result in vector_results[:3]
                    if result.get("content", "").strip()
                ]
            )

            graph_content = "\n".join(
                [
                    f"- {result.get('relationships', '')}"
                    for result in graph_results[:3]
                    if result.get("relationships", "").strip()
                ]
            )

            # If content is empty after filtering, don't synthesize
            if not vector_content.strip() and not graph_content.strip():
                logger.warning(
                    f"🚫 GRAPHRAG: All search results were empty after filtering"
                )
                return (
                    f"Search completed but no relevant content found for: {user_query}"
                )

            prompt = synthesis_prompt_config.prompt.system.format(
                coach_domain=coach_domain,
                user_query=user_query,
                vector_content=vector_content,
                graph_content=graph_content,
            )

            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            return response.content

        except Exception as e:
            logger.error(f"❌ GRAPHRAG: Error in knowledge synthesis: {e}")
            return f"Error synthesizing knowledge: {str(e)}"


# Global retriever instance
_graphrag_retriever = None


async def get_graphrag_retriever() -> GraphRAGRetriever:
    """Get the global GraphRAG retriever instance."""
    global _graphrag_retriever
    if _graphrag_retriever is None:
        _graphrag_retriever = GraphRAGRetriever()
    return _graphrag_retriever


async def knowledge_assessment_node(state) -> Dict[str, Any]:
    """
    GraphRAG Node 1: Assess if the user query requires external knowledge retrieval.

    This node determines whether the coaching query would benefit from external
    knowledge sources beyond the coach's base training.
    """
    logger.info(
        "🧠 GRAPHRAG: [KNOWLEDGE_ASSESSMENT] Starting knowledge needs assessment"
    )

    # Handle both dict and Pydantic model state
    if isinstance(state, dict):
        state["current_node"] = "knowledge_assessment"
        state["execution_steps"] = state.get("execution_steps", []) + [
            "knowledge_assessment"
        ]
        user_query = state.get("user_query", "")
        # Use primary_coach from Intelligence Hub, not routing_decision
        primary_coach = state.get("primary_coach")
    else:
        state.current_node = "knowledge_assessment"
        state.execution_steps.append("knowledge_assessment")
        user_query = getattr(state, "user_query", "")
        # Use primary_coach from Intelligence Hub, not routing_decision
        primary_coach = getattr(state, "primary_coach", None)

    # Extract user query if not available
    if not user_query:
        messages = (
            state.get("messages", [])
            if isinstance(state, dict)
            else getattr(state, "messages", [])
        )
        for msg in reversed(messages):
            if (
                hasattr(msg, "content")
                and hasattr(msg, "__class__")
                and "Human" in msg.__class__.__name__
            ):
                user_query = msg.content
                break

    # Map routing decision to coach domain
    coach_domain_map = {
        "strength_coach": "strength_coach",
        "cardio_coach": "cardio_coach",
        "cycling_coach": "cycling_coach",
        "nutrition_coach": "nutrition_coach",
        "recovery_coach": "recovery_coach",
        "mental_coach": "mental_coach",
    }
    # Use primary_coach from Intelligence Hub instead of routing_decision
    if primary_coach and primary_coach in coach_domain_map:
        coach_domain = coach_domain_map[primary_coach]
        logger.info(
            f"🎯 GRAPHRAG: [KNOWLEDGE_ASSESSMENT] Using Intelligence Hub coach: {primary_coach}"
        )
    else:
        # Only fallback if no primary_coach is set
        coach_domain = "strength_coach"
        logger.warning(
            f"🎯 GRAPHRAG: [KNOWLEDGE_ASSESSMENT] No valid primary_coach ({primary_coach}), falling back to strength_coach"
        )

    logger.info(
        f"📋 GRAPHRAG: [KNOWLEDGE_ASSESSMENT] Assessing query in domain: {coach_domain}"
    )
    logger.info(
        f"📝 GRAPHRAG: [KNOWLEDGE_ASSESSMENT] Query: {user_query[:100]}{'...' if len(user_query) > 100 else ''}"
    )

    try:
        retriever = await get_graphrag_retriever()
        assessment = await retriever.assess_knowledge_needs(user_query, coach_domain)

        logger.info(f"✅ GRAPHRAG: [KNOWLEDGE_ASSESSMENT] Assessment complete:")
        logger.info(f"  - Needs retrieval: {assessment['needs_knowledge_retrieval']}")
        logger.info(f"  - Confidence: {assessment['knowledge_confidence']}")

        return {
            "current_node": "knowledge_assessment",
            "needs_knowledge_retrieval": assessment["needs_knowledge_retrieval"],
            "knowledge_confidence": assessment["knowledge_confidence"],
            "knowledge_assessment_reasoning": assessment[
                "knowledge_assessment_reasoning"
            ],
            "debug_info": {
                "node": "knowledge_assessment",
                "domain": coach_domain,
                "needs_retrieval": assessment["needs_knowledge_retrieval"],
                "confidence": assessment["knowledge_confidence"],
            },
        }

    except Exception as e:
        logger.error(f"❌ GRAPHRAG: [KNOWLEDGE_ASSESSMENT] Error: {e}")
        return {
            "current_node": "knowledge_assessment",
            "needs_knowledge_retrieval": False,
            "knowledge_confidence": 0.3,
            "debug_info": {"node": "knowledge_assessment", "error": str(e)},
        }


async def graphrag_retrieval_node(state) -> Dict[str, Any]:
    """
    GraphRAG Node 2: Perform knowledge retrieval using vector and graph search.

    This node implements the retrieval logic similar to the Neo4j example,
    routing between vector search, graph queries, and hybrid approaches.
    """
    logger.info("🔍 GRAPHRAG: [RETRIEVAL] Starting GraphRAG knowledge retrieval")

    # Handle both dict and Pydantic model state
    if isinstance(state, dict):
        state["current_node"] = "graphrag_retrieval"
        state["execution_steps"] = state.get("execution_steps", []) + [
            "graphrag_retrieval"
        ]
        user_query = state.get("user_query", "")
        # Use primary_coach from Intelligence Hub, not routing_decision
        primary_coach = state.get("primary_coach")
        needs_retrieval = state.get("needs_knowledge_retrieval", True)
    else:
        state.current_node = "graphrag_retrieval"
        state.execution_steps.append("graphrag_retrieval")
        user_query = getattr(state, "user_query", "")
        # Use primary_coach from Intelligence Hub, not routing_decision
        primary_coach = getattr(state, "primary_coach", None)
        needs_retrieval = getattr(state, "needs_knowledge_retrieval", True)

    # Preserve the needs_retrieval flag to pass it through
    persisted_needs_retrieval = needs_retrieval

    # Skip retrieval if not needed
    if not needs_retrieval:
        logger.info("⏭️ GRAPHRAG: [RETRIEVAL] Skipping retrieval - not needed")
        return {
            "current_node": "graphrag_retrieval",
            "retrieval_type": "none",
            "knowledge_context": "No external knowledge required",
            "needs_knowledge_retrieval": persisted_needs_retrieval,
            "debug_info": {
                "node": "graphrag_retrieval",
                "action": "skipped",
                "reason": "not_needed",
            },
        }

    # Map primary_coach to coach domain - preserve Intelligence Hub's decision
    coach_domain_map = {
        "strength_coach": "strength_coach",
        "cardio_coach": "cardio_coach",
        "cycling_coach": "cycling_coach",
        "nutrition_coach": "nutrition_coach",
        "recovery_coach": "recovery_coach",
        "mental_coach": "mental_coach",
    }

    # Use primary_coach from Intelligence Hub instead of routing_decision
    if primary_coach and primary_coach in coach_domain_map:
        coach_domain = coach_domain_map[primary_coach]
        logger.info(
            f"🎯 GRAPHRAG: [RETRIEVAL] Using Intelligence Hub coach: {primary_coach}"
        )
    else:
        # Only fallback if no primary_coach is set
        coach_domain = "strength_coach"
        logger.warning(
            f"🎯 GRAPHRAG: [RETRIEVAL] No valid primary_coach ({primary_coach}), falling back to strength_coach"
        )

    logger.info(f"🎯 GRAPHRAG: [RETRIEVAL] Retrieving for domain: {coach_domain}")
    logger.info(
        f"📝 GRAPHRAG: [RETRIEVAL] Query: {user_query[:100]}{'...' if len(user_query) > 100 else ''}"
    )

    try:
        retriever = await get_graphrag_retriever()

        # Step 1: Route to appropriate retrieval type
        retrieval_type = await retriever.route_retrieval_type(user_query, coach_domain)
        logger.info(f"🔀 GRAPHRAG: [RETRIEVAL] Routing to: {retrieval_type}")

        # Step 2: Decompose query if complex
        decomposed_queries = await retriever.decompose_query(user_query, coach_domain)
        logger.info(
            f"🧩 GRAPHRAG: [RETRIEVAL] Decomposed into {len(decomposed_queries)} queries"
        )

        # Step 3: Perform retrieval based on type
        vector_results = []
        graph_results = []

        if retrieval_type in ["vector", "hybrid"]:
            for query in decomposed_queries:
                results = await retriever.vector_search(query, coach_domain)
                vector_results.extend(results)

        if retrieval_type in ["graph", "hybrid"]:
            for query in decomposed_queries:
                results = await retriever.graph_search(query, coach_domain)
                graph_results.extend(results)

        # Step 4: Synthesize knowledge
        knowledge_context = await retriever.synthesize_knowledge(
            vector_results, graph_results, user_query, coach_domain
        )

        # Step 5: Collect sources
        knowledge_sources = []
        for result in vector_results + graph_results:
            source = result.get("source", "Unknown")
            if source not in knowledge_sources:
                knowledge_sources.append(source)

        logger.info(f"✅ GRAPHRAG: [RETRIEVAL] Retrieval complete:")
        logger.info(f"  - Type: {retrieval_type}")
        logger.info(f"  - Vector results: {len(vector_results)}")
        logger.info(f"  - Graph results: {len(graph_results)}")
        logger.info(f"  - Knowledge context length: {len(knowledge_context)} chars")

        return {
            "current_node": "graphrag_retrieval",
            "retrieval_type": retrieval_type,
            "decomposed_queries": decomposed_queries,
            "vector_results": vector_results,
            "graph_results": graph_results,
            "knowledge_context": knowledge_context,
            "knowledge_sources": knowledge_sources,
            "graphrag_results": {
                "type": retrieval_type,
                "vector_count": len(vector_results),
                "graph_count": len(graph_results),
                "context_length": len(knowledge_context),
            },
            "debug_info": {
                "node": "graphrag_retrieval",
                "retrieval_type": retrieval_type,
                "queries_count": len(decomposed_queries),
                "total_results": len(vector_results) + len(graph_results),
            },
            "needs_knowledge_retrieval": persisted_needs_retrieval,
        }

    except Exception as e:
        logger.error(f"❌ GRAPHRAG: [RETRIEVAL] Error: {e}")
        return {
            "current_node": "graphrag_retrieval",
            "retrieval_type": "error",
            "knowledge_context": f"Error retrieving knowledge: {str(e)}",
            "needs_knowledge_retrieval": persisted_needs_retrieval,
            "debug_info": {"node": "graphrag_retrieval", "error": str(e)},
        }
