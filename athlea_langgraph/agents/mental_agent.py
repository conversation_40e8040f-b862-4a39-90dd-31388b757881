"""
Mental Agent - Modular ReAct Implementation

Specialized agent for mental training, sports psychology, motivation, and mindset coaching.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.mental import (
    MentalStateAssessmentTool,
    StressLevelTracker,
    MoodPatternAnalyzer,
    GoalTracker,
)

# Additional tool access capabilities
from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool
from ..tools.external.session_generation import SessionGenerationTool
from langchain_core.tools import Tool

from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class MentalAgent(BaseReActAgent):
    """Specialized agent for mental training coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the mental agent with domain-specific tools."""
        # Fallback prompt
        fallback_prompt = """You are a Mental Training Coach specializing in sports psychology and mindset development.

Your expertise includes:
- Goal setting and motivation
- Stress and anxiety management
- Confidence building
- Focus and concentration techniques
- Habit formation
- Mental resilience and toughness

You have access to specialized tools for mental health assessment and tracking.
Based on the user's query, you MUST decide whether to use one of your tools or answer directly.

Your available tools are:
- `MentalStateAssessmentTool`: Use this tool when a user wants to assess their current mental state, emotional well-being, or psychological readiness for training.
- `StressLevelTracker`: Use this tool when a user mentions stress, anxiety, or wants to track their stress levels over time.
- `MoodPatternAnalyzer`: Use this tool when a user wants to analyze mood patterns, emotional fluctuations, or understand mood-performance relationships.
- `GoalTracker`: Use this tool when a user wants to set, track, or modify their fitness and mental health goals.

Help users develop the mental skills needed to achieve their fitness goals and maintain long-term success."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="mental_agent",
            domain="mental_training",
            system_prompt=fallback_prompt,  # Will be updated lazily
            tools=[],  # Will be populated by get_domain_tools()
            permissions=["mental_assessment", "goal_setting", "motivation_coaching"],
            max_iterations=10,
            temperature=0.7,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self) -> str:
        """Load the system prompt lazily"""
        if not self._prompt_loaded:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("mental_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded mental coach prompt from file")
            except Exception as e:
                logger.error(f"Failed to load mental coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        Instantiates and returns the specific tools for the mental agent.
        This makes the agent self-contained and independent of a central tool manager.
        Now includes broader tool access: Azure Search, session generation, and web search.
        """
        if not self._tools_loaded:
            # Domain-specific mental tools
            mental_tools = [
                MentalStateAssessmentTool(),
                StressLevelTracker(),
                MoodPatternAnalyzer(),
                GoalTracker(),
            ]
            
            # Add broader tool access capabilities
            try:
                # Azure Search for research and evidence-based information
                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based mental performance information.
                    
                    Use this tool for:
                    - Sports psychology research and techniques
                    - Mental training methodologies
                    - Stress management strategies
                    - Performance psychology studies
                    - Mindfulness and meditation research
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke
                )
                mental_tools.append(azure_search_langchain)
                
                # Session Generation for structured mental training plans
                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_mental_session",
                    description="""Generate structured mental training sessions and programs.
                    
                    Use this tool to create:
                    - Mindfulness and meditation sessions
                    - Visualization training programs
                    - Stress management protocols
                    - Goal-setting workshops
                    - Mental performance routines
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session
                )
                mental_tools.append(session_gen_langchain)
                
                # Web Search through MCP for current information
                from ..tools.web_search_tool import WebSearchTool
                web_search_tool = WebSearchTool()
                mental_tools.append(web_search_tool)
                
                logger.info("Successfully added broader tool access capabilities to mental agent")
                
            except Exception as e:
                logger.warning(f"Failed to add some broader tools to mental agent: {e}")

            # Update the agent's tools
            self.tools = mental_tools
            # Update the ReAct executor's tools if it exists
            if hasattr(self, "react_executor") and self.react_executor:
                self.react_executor.tools = mental_tools

            logger.info(
                f"Loaded {len(mental_tools)} tools for mental agent: {[t.name for t in mental_tools]}"
            )
            self._tools_loaded = True

        return self.tools
    


    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for mental coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process mental training requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing
        await self._load_system_prompt()

        return await super().process(state, config)


# Create the mental agent instance
mental_agent = MentalAgent()


async def mental_agent_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for mental training coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with mental agent response
    """
    logger.info("--- Running Mental Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not mental_agent.tools:
            await mental_agent.get_domain_tools()

        # Process the request
        result = await mental_agent.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "mental_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Mental agent completed with {len(mental_agent.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in mental agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your mental training request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "mental_agent",
            "error": str(e),
        }
