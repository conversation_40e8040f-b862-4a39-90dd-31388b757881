"""
Recovery Agent - Modular ReAct Implementation

Specialized agent for recovery, regeneration, sleep optimization, and injury prevention.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.recovery import (
    generate_mobility_protocol,
    optimize_sleep,
    assess_wellness,
    create_graphrag_tool,
)
from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class RecoveryAgent(BaseReActAgent):
    """Specialized agent for recovery coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the recovery agent with domain-specific tools."""
        # Fallback prompt with strong tool calling instructions
        fallback_prompt = """You are a world-class Recovery Coach. Your primary function is to provide precise, evidence-based advice on physical and mental recovery by leveraging your specialized tools.

**CRITICAL INSTRUCTIONS:**
1.  **Assess the User's Query:** Analyze the user's question to determine if one of your tools can provide a precise, data-driven answer.
2.  **Prioritize Tool Usage:** For any request related to generating mobility protocols, optimizing sleep, assessing wellness, or retrieving scientific research, you MUST use the appropriate tool. Do not answer from general knowledge if a tool is available.
3.  **Use Tools Correctly:**
    - To generate a mobility routine, use `generate_mobility_protocol`.
    - To get sleep optimization advice, use `optimize_sleep`.
    - To assess overall wellness, use `assess_wellness`.
    - For research questions, use `azure_search_retriever`.
    - For product reviews or current news, use `web_search`.
4.  **Engage in Conversation:** If a tool is not required, respond naturally and conversationally. If you need more information to use a tool effectively, ask the user clarifying questions.

Your goal is to be a helpful and accurate recovery coach, using your tools to provide the best possible guidance."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="recovery_agent",
            domain="recovery",
            system_prompt=fallback_prompt,
            tools=[],
            permissions=[
                "mobility_protocols",
                "sleep_optimization",
                "wellness_assessment",
            ],
            max_iterations=10,
            temperature=0.2,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self) -> str:
        """Load the system prompt lazily"""
        if not self._prompt_loaded:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("recovery_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded recovery coach prompt from file")
            except Exception as e:
                logger.error(f"Failed to load recovery coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        Instantiates and returns the specific tools for the recovery agent.
        This makes the agent self-contained and independent of a central tool manager.
        """
        if not self._tools_loaded:
            # Domain-specific recovery tools
            recovery_tools = [
                generate_mobility_protocol,
                optimize_sleep,
                assess_wellness,
            ]

            # Add shared tools that all agents should have

            # 1. GraphRAG tool for research
            recovery_tools.append(create_graphrag_tool())

            # 2. Session Generation Graph tool (use graph-based architecture instead of basic tool)
            try:
                from ..tools.external.session_generation_graph_tool import (
                    create_session_generation_graph_tool,
                )

                session_graph_tool = create_session_generation_graph_tool()
                recovery_tools.append(session_graph_tool)
            except Exception as e:
                logger.warning(f"Failed to load session generation graph tool: {e}")
                # Fallback to basic tool if graph tool fails
                try:
                    from ..tools.external.session_generation import (
                        SessionGenerationTool,
                    )
                    from langchain_core.tools import BaseTool

                    class SessionGenerationLangChainTool(BaseTool):
                        name: str = "session_generation"
                        description: str = """Generate structured recovery sessions and wellness protocols.
                        Use this tool to create detailed recovery programs, mobility routines, and wellness sessions.
                        Input should be a JSON string with session parameters including goals, duration, and focus areas."""

                        def _run(self, session_params: str) -> str:
                            try:
                                import json

                                session_tool = SessionGenerationTool()
                                params = json.loads(session_params)
                                result = session_tool.generate_session(params)
                                return json.dumps(result, indent=2)
                            except Exception as e:
                                return f"Session generation failed: {str(e)}"

                        async def _arun(self, session_params: str) -> str:
                            return self._run(session_params)

                    recovery_tools.append(SessionGenerationLangChainTool())
                except Exception as fallback_e:
                    logger.warning(
                        f"Failed to load fallback session generation tool: {fallback_e}"
                    )

            # 3. Azure Search tool for research and knowledge retrieval
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain_tool = azure_search_tool.to_langchain_tool()
                recovery_tools.append(azure_search_langchain_tool)
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # 4. Maps Workflow tool for location-based services (finding spas, wellness centers, etc.)
            try:
                from ..tools.external.maps_workflow import create_maps_workflow_tool

                maps_workflow_tool = await create_maps_workflow_tool()
                recovery_tools.append(maps_workflow_tool)
            except Exception as e:
                logger.warning(f"Failed to load maps workflow tool: {e}")

            # 5. Web Search tool for current information
            try:
                from langchain_community.tools import DuckDuckGoSearchRun

                web_search_tool = DuckDuckGoSearchRun(
                    name="web_search",
                    description="Search the web for current recovery research, techniques, and wellness information. Use for recent studies, recovery methods, or product reviews.",
                )
                recovery_tools.append(web_search_tool)
            except Exception as e:
                logger.warning(f"Failed to load web search tool: {e}")

            # Update the agent's tools
            self.tools = recovery_tools

            # Tools loaded successfully for direct tool calling
            logger.info(
                f"✅ Loaded {len(recovery_tools)} tools for direct calling: {[t.name for t in recovery_tools]}"
            )

            logger.info(
                f"Loaded {len(recovery_tools)} tools for recovery agent: {[t.name for t in recovery_tools]}"
            )
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for recovery coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process recovery requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing
        await self._load_system_prompt()

        return await super().process(state, config)


# Create the recovery agent instance
recovery_agent = RecoveryAgent()


async def recovery_agent_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for recovery coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with recovery agent response
    """
    logger.info("--- Running Recovery Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not recovery_agent.tools:
            await recovery_agent.get_domain_tools()

        # Process the request
        result = await recovery_agent.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "recovery_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Recovery agent completed with {len(recovery_agent.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in recovery agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your recovery request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "recovery_agent",
            "error": str(e),
        }
