"""
GraphRAG Agent Node for Athlea LangGraph Integration

Specialized agent node that uses GraphRAG for evidence-based reasoning
combining textual research evidence with structured knowledge relationships.
"""

import logging
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_openai import AzureChatOpenAI

from ..services.azure_openai_service import create_azure_chat_openai
from ..services.graphrag_service import get_graphrag_service, GraphRAGQueryInput
from ..tools.external.graphrag_tool import create_graphrag_tool
from ..states import AgentState
from ..utils.prompt_loader import PromptLoader

logger = logging.getLogger(__name__)


async def graphrag_agent_node(state: AgentState) -> AgentState:
    """
    GraphRAG agent node that provides evidence-based responses using knowledge graph.

    This node:
    1. Analyzes user queries for research-relevant questions
    2. Queries both Azure Cognitive Search and Gremlin Graph
    3. Synthesizes findings into evidence-based recommendations
    4. Provides specific protocols, dosages, and conditions

    Args:
        state: Current agent state with messages and metadata

    Returns:
        Updated agent state with GraphRAG-enhanced response
    """
    logger.info("GraphRAG agent node processing request")

    try:
        # Get the latest user message
        messages = state.get("messages", [])
        if not messages:
            logger.warning("No messages in state for GraphRAG processing")
            return state

        last_message = messages[-1]
        if not isinstance(last_message, HumanMessage):
            logger.warning(
                "Last message is not from human, skipping GraphRAG processing"
            )
            return state

        user_query = last_message.content
        logger.info(f"Processing GraphRAG query: {user_query[:100]}...")

        # Check if this is a research-relevant query
        if not _is_research_relevant_query(user_query):
            logger.info("Query not research-relevant, skipping GraphRAG")
            return state

        # Extract entities and parameters from query
        query_params = _extract_query_parameters(user_query, state)

        # Execute GraphRAG query
        graphrag_service = get_graphrag_service()
        result = await graphrag_service.execute_graphrag_query(
            GraphRAGQueryInput(**query_params)
        )

        if not result.success:
            logger.error(f"GraphRAG query failed: {result.message}")
            # Add error message to state but don't fail completely
            error_message = AIMessage(
                content=f"I encountered an issue accessing the research database: {result.message}. Let me try to help with general knowledge instead."
            )
            return {**state, "messages": messages + [error_message]}

        # Generate response using LLM with GraphRAG context
        llm = create_azure_chat_openai()

        # Load GraphRAG system prompt from JSON
        prompt_loader = PromptLoader("athlea_langgraph/prompts")
        import asyncio

        graphrag_prompt_config = await asyncio.to_thread(
            prompt_loader.load_prompt, "rag/graphrag_system_coaching"
        )
        system_prompt = SystemMessage(content=graphrag_prompt_config.prompt.system)

        # Prepare context from GraphRAG results
        context = _format_graphrag_context(result)

        # Create LLM prompt with GraphRAG context
        llm_messages = [
            system_prompt,
            HumanMessage(
                content=f"""
User Query: {user_query}

GraphRAG Research Context:
{context}

Please provide an evidence-based response that:
1. Directly answers the user's question
2. Cites specific research findings
3. Includes actionable protocols with dosages/conditions when available
4. Mentions confidence levels for recommendations
5. Acknowledges limitations or conflicting evidence if present
"""
            ),
        ]

        # Generate response
        response = await llm.ainvoke(llm_messages)

        # Add GraphRAG metadata to the response
        enhanced_content = response.content
        if result.causalities or result.recommendations:
            enhanced_content += "\n\n" + _format_additional_insights(result)

        # Create AI message with enhanced content
        ai_message = AIMessage(
            content=enhanced_content,
            additional_kwargs={
                "graphrag_metadata": {
                    "request_id": result.request_id,
                    "execution_time_ms": result.execution_time_ms,
                    "acs_chunk_count": result.acs_chunk_count,
                    "graph_entity_count": result.graph_entity_count,
                    "causalities_found": len(result.causalities),
                    "recommendations_count": len(result.recommendations),
                }
            },
        )

        logger.info(
            f"GraphRAG response generated successfully (request_id: {result.request_id})"
        )
        return {**state, "messages": messages + [ai_message]}

    except Exception as e:
        logger.error(f"Error in GraphRAG agent node: {e}")
        error_message = AIMessage(
            content="I encountered an issue while accessing the research database. Let me try to help with my general knowledge instead."
        )
        return {**state, "messages": messages + [error_message]}


def _is_research_relevant_query(query: str) -> bool:
    """
    Determine if a query is relevant for GraphRAG research lookup.

    Args:
        query: User query string

    Returns:
        True if query should use GraphRAG, False otherwise
    """
    research_keywords = [
        # Training methods
        "hiit",
        "interval",
        "strength",
        "cardio",
        "endurance",
        "plyometric",
        "pilates",
        "yoga",
        "proprioceptive",
        "balance",
        "coordination",
        # Injury prevention
        "injury",
        "prevention",
        "rehab",
        "rehabilitation",
        "recovery",
        "fifa 11+",
        "fifa",
        "11+",
        "warm up",
        "warmup",
        "cool down",
        # Sports
        "football",
        "soccer",
        "futsal",
        "basketball",
        "tennis",
        "running",
        "cycling",
        "swimming",
        "rowing",
        "volleyball",
        "handball",
        # Body systems
        "muscle",
        "joint",
        "bone",
        "tendon",
        "ligament",
        "cartilage",
        "cardiovascular",
        "respiratory",
        "nervous",
        "endocrine",
        # Research terms
        "study",
        "research",
        "evidence",
        "protocol",
        "dosage",
        "frequency",
        "effectiveness",
        "efficacy",
        "meta-analysis",
        "systematic review",
        # Performance metrics
        "performance",
        "vo2",
        "lactate",
        "power",
        "speed",
        "agility",
        "flexibility",
        "mobility",
        "stability",
        "strength",
    ]

    query_lower = query.lower()
    return any(keyword in query_lower for keyword in research_keywords)


def _extract_query_parameters(query: str, state: AgentState) -> Dict[str, Any]:
    """
    Extract parameters for GraphRAG query from user input and state.

    Args:
        query: User query string
        state: Current agent state

    Returns:
        Dictionary of GraphRAG query parameters
    """
    params = {
        "query": query,
        "include_acs": True,
        "include_graph": True,
        "top_k": 15,  # More results for comprehensive analysis
    }

    # Extract entities mentioned in query
    entities = []
    entity_mappings = {
        "fifa 11+": "FIFA 11+",
        "fifa": "FIFA 11+",
        "11+": "FIFA 11+",
        "hiit": "HIIT",
        "pilates": "PILATES",
        "proprioceptive": "PROPRIOCEPTIVE TRAINING",
        "balance training": "PROPRIOCEPTIVE TRAINING",
        "futsal": "FUTSAL",
        "football": "FOOTBALL",
        "soccer": "FOOTBALL",
    }

    query_lower = query.lower()
    for keyword, entity in entity_mappings.items():
        if keyword in query_lower and entity not in entities:
            entities.append(entity)

    if entities:
        params["entities"] = entities

    # Check if user specified a particular study or DOI
    if "doi" in query_lower or "10.3390" in query:
        # Could extract DOI with regex, for now use test DOI
        params["doi"] = "10.3390/healthcare12141387"

    # Check for document ID in state (if coming from a specific research context)
    if "document_context" in state:
        doc_context = state["document_context"]
        if "document_id" in doc_context:
            params["document_id"] = doc_context["document_id"]

    return params


def _format_graphrag_context(result: Any) -> str:
    """
    Format GraphRAG results into context for LLM prompt.

    Args:
        result: GraphRAG query result

    Returns:
        Formatted context string
    """
    context_parts = []

    # Add research summary
    if result.synthesis:
        context_parts.append(f"RESEARCH SUMMARY:\n{result.synthesis}\n")

    # Add top text evidence
    if result.acs_results and result.acs_chunk_count > 0:
        context_parts.append(
            f"TEXTUAL EVIDENCE ({result.acs_chunk_count} research chunks):"
        )
        try:
            acs_data = result.acs_results
            if isinstance(acs_data, dict) and "results" in acs_data:
                for i, chunk in enumerate(acs_data["results"][:3], 1):  # Top 3
                    if "content" in chunk:
                        content = (
                            chunk["content"][:400] + "..."
                            if len(chunk["content"]) > 400
                            else chunk["content"]
                        )
                        source = chunk.get("source", "Unknown source")
                        score = chunk.get("score", "N/A")
                        context_parts.append(
                            f"Evidence {i} (Relevance: {score}, Source: {source}):\n{content}\n"
                        )
        except Exception as e:
            logger.warning(f"Error formatting ACS results: {e}")

    # Add causal relationships
    if result.causalities:
        context_parts.append("CAUSAL RELATIONSHIPS:")
        for causality in result.causalities:
            relationship = causality.get("relationship", "UNKNOWN")
            source_entity = causality.get("source_entity", "Unknown")
            target_entity = causality.get("target_entity", "Unknown")
            confidence = causality.get("confidence", 0)
            condition = causality.get("condition", "")
            dosage = causality.get("dosage", "")

            causal_text = f"- {source_entity} {relationship} {target_entity}"
            if confidence > 0:
                causal_text += f" (Confidence: {confidence:.1f})"
            if condition:
                causal_text += f"\n  Condition: {condition}"
            if dosage:
                causal_text += f"\n  Dosage: {dosage}"

            context_parts.append(causal_text)
        context_parts.append("")

    # Add structured relationships summary
    if result.graph_entity_count > 0:
        context_parts.append(
            f"KNOWLEDGE GRAPH: Found {result.graph_entity_count} entities with {result.graph_relationship_count} relationships"
        )

    return "\n".join(context_parts)


def _format_additional_insights(result: Any) -> str:
    """
    Format additional insights from GraphRAG for appending to response.

    Args:
        result: GraphRAG query result

    Returns:
        Formatted additional insights string
    """
    insights = []

    if result.recommendations:
        insights.append("📋 **Key Research-Based Recommendations:**")
        for i, rec in enumerate(result.recommendations, 1):
            insights.append(f"{i}. {rec}")
        insights.append("")

    if result.causalities:
        high_conf_causalities = [
            c for c in result.causalities if c.get("confidence", 0) > 0.8
        ]
        if high_conf_causalities:
            insights.append("🔬 **High-Confidence Research Findings:**")
            for causality in high_conf_causalities[:3]:  # Top 3
                source = causality.get("source_entity", "Unknown")
                relationship = causality.get("relationship", "affects")
                target = causality.get("target_entity", "Unknown")
                conf = causality.get("confidence", 0)
                insights.append(
                    f"- {source} {relationship.lower()} {target} (Confidence: {conf:.1f})"
                )

    return "\n".join(insights) if insights else ""


# Additional utility functions for specialized GraphRAG queries


async def query_injury_prevention(
    injury_type: str, sport: Optional[str] = None
) -> Dict[str, Any]:
    """
    Specialized query for injury prevention protocols.

    Args:
        injury_type: Type of injury (e.g., "ankle sprain", "ACL")
        sport: Specific sport context (optional)

    Returns:
        GraphRAG results focused on injury prevention
    """
    graphrag_service = get_graphrag_service()

    query = f"injury prevention for {injury_type}"
    if sport:
        query += f" in {sport}"

    entities = ["FIFA 11+", "PROPRIOCEPTIVE TRAINING"]
    if "ankle" in injury_type.lower():
        entities.append("ANKLE_INJURY")
    elif "acl" in injury_type.lower() or "knee" in injury_type.lower():
        entities.extend(["ACL_INJURY", "KNEE_INJURY"])

    return await graphrag_service.execute_graphrag_query(
        GraphRAGQueryInput(
            query=query,
            entities=entities,
            include_acs=True,
            include_graph=True,
            top_k=10,
        )
    )


async def query_training_protocol(exercise: str, goal: str) -> Dict[str, Any]:
    """
    Specialized query for training protocols and dosages.

    Args:
        exercise: Exercise or training method
        goal: Training goal (e.g., "strength", "endurance", "flexibility")

    Returns:
        GraphRAG results with specific protocols
    """
    graphrag_service = get_graphrag_service()

    query = f"{exercise} protocol for {goal}"
    entities = []

    if "hiit" in exercise.lower():
        entities.append("HIIT")
    elif "pilates" in exercise.lower():
        entities.append("PILATES")
    elif "strength" in exercise.lower():
        entities.append("STRENGTH_TRAINING")

    return await graphrag_service.execute_graphrag_query(
        GraphRAGQueryInput(
            query=query,
            entities=entities,
            include_acs=True,
            include_graph=True,
            top_k=12,
        )
    )
