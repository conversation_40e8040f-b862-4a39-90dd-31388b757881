"""
Strength Agent - Modular ReAct Implementation

Specialized agent for strength training, resistance training, and muscle building.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.strength import (
    search_strength_exercises,
    get_exercise_progression,
    comprehensive_strength_assessment,
)

# Additional tool access capabilities
from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool
from ..tools.external.session_generation import SessionGenerationTool
from langchain_core.tools import Tool

from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class StrengthAgent(BaseReActAgent):
    """Specialized agent for strength training coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the strength agent with domain-specific tools."""
        # Fallback prompt
        fallback_prompt = """You are a Strength Training Coach specializing in resistance training, powerlifting, bodybuilding, and functional strength development.

Your expertise includes:
- Exercise selection and progression
- Program design for different goals (strength, hypertrophy, power)
- Proper form and technique
- Equipment recommendations
- Injury prevention and modification
- Periodization and programming

You have access to specialized tools for exercise databases, strength assessment, and program generation.
Based on the user's query, you MUST decide whether to use one of your tools or answer directly.

Your available tools are:
- `search_strength_exercises`: Use this tool when a user wants to find exercises based on muscle groups, equipment, exercise type, or difficulty level.
- `get_exercise_progression`: Use this tool when a user wants progression suggestions for a specific exercise or wants to advance their training.
- `comprehensive_strength_assessment`: Use this tool when a user wants a complete strength assessment including movement screening, strength testing, and personalized recommendations.

Always provide evidence-based advice and consider the user's experience level, goals, and any limitations they may have."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="strength_agent",
            domain="strength_training",
            system_prompt=fallback_prompt,  # Will be updated lazily
            tools=[],  # Will be populated by get_domain_tools()
            permissions=["strength_assessment", "program_design", "exercise_selection"],
            max_iterations=10,
            temperature=0.7,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self) -> str:
        """Load the system prompt lazily"""
        if not self._prompt_loaded:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("strength_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded strength coach prompt from file")
            except Exception as e:
                logger.error(f"Failed to load strength coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        Instantiates and returns the specific tools for the strength agent.
        This makes the agent self-contained and independent of a central tool manager.
        Now includes broader tool access: Azure Search, session generation, and web search.
        """
        if not self._tools_loaded:
            # Domain-specific strength tools
            strength_tools = [
                search_strength_exercises,
                get_exercise_progression,
                comprehensive_strength_assessment,
            ]
            
            # Add broader tool access capabilities
            try:
                # Azure Search for research and evidence-based information
                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain = Tool(
                    name="azure_search_retriever",
                    description="""Search Azure Cognitive Search for research, documentation, and evidence-based strength training information.
                    
                    Use this tool for:
                    - Strength training research and methodologies
                    - Exercise biomechanics and technique studies
                    - Program design and periodization research
                    - Injury prevention and rehabilitation studies
                    - Equipment reviews and recommendations
                    
                    Input should be a search query string.""",
                    func=lambda query: "Azure Search requires async execution",
                    coroutine=azure_search_tool.invoke
                )
                strength_tools.append(azure_search_langchain)
                
                # Session Generation for structured strength training plans
                session_gen_tool = SessionGenerationTool()
                session_gen_langchain = Tool(
                    name="generate_strength_session",
                    description="""Generate structured strength training sessions and workout plans.
                    
                    Use this tool to create:
                    - Strength training workouts
                    - Powerlifting sessions
                    - Bodybuilding routines
                    - Functional strength programs
                    - Rehabilitation protocols
                    
                    Input should be session parameters as JSON string.""",
                    func=session_gen_tool.generate_session
                )
                strength_tools.append(session_gen_langchain)
                
                # Web Search through MCP for current information
                from ..tools.web_search_tool import WebSearchTool
                web_search_tool = WebSearchTool()
                strength_tools.append(web_search_tool)
                
                logger.info("Successfully added broader tool access capabilities to strength agent")
                
            except Exception as e:
                logger.warning(f"Failed to add some broader tools to strength agent: {e}")

            # Update the agent's tools
            self.tools = strength_tools
            # Update the ReAct executor's tools if it exists
            if hasattr(self, "react_executor") and self.react_executor:
                self.react_executor.tools = strength_tools

            logger.info(
                f"Loaded {len(strength_tools)} tools for strength agent: {[t.name for t in strength_tools]}"
            )
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for strength coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process strength training requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing
        await self._load_system_prompt()

        return await super().process(state, config)




# Create the strength agent instance
strength_agent = StrengthAgent()


async def strength_agent_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for strength training coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with strength agent response
    """
    logger.info("--- Running Strength Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not strength_agent.tools:
            await strength_agent.get_domain_tools()

        # Process the request
        result = await strength_agent.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "strength_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Strength agent completed with {len(strength_agent.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in strength agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your strength training request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "strength_agent",
            "error": str(e),
        }
