"""
Reflection-Enhanced Coaching Graph for Phase 1 Implementation.

Integrates the LangGraph reflection pattern into the existing coaching workflow
to provide safety-focused critique and iterative improvement of coaching responses.
"""

import logging
from typing import Annotated, Any, Dict, Optional

from langchain_core.messages import BaseMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from athlea_langgraph.agents.head_coach import head_coach_node, head_coach_router
from athlea_langgraph.agents.planning_node import planning_node

# Import existing coaching nodes
from athlea_langgraph.agents.reasoning_node import reasoning_node

# Import new reflection components
from athlea_langgraph.agents.reflection_agent import reflection_node
from athlea_langgraph.agents.reflection_controller import (
    reflection_finalization_node,
    reflection_router,
    safety_gate_router,
)
from athlea_langgraph.agents.response_regenerator import response_regeneration_node
from athlea_langgraph.agents.specialized_coaches import (
    cardio_coach_node,
    mental_coach_node,
    nutrition_coach_node,
    recovery_coach_node,
    strength_coach_node,
)
from athlea_langgraph.states import messages_reducer
from athlea_langgraph.states.reflection_state import (
    ReflectionAgentState,
    create_reflection_config,
    enhance_state_with_reflection,
    reflection_config_reducer,
    reflection_metadata_reducer,
    safety_validation_reducer,
)


def create_reflection_enhanced_state_graph() -> StateGraph:
    """
    Create the state graph with reflection-enhanced state annotation.

    Returns:
        StateGraph configured with ReflectionAgentState
    """

    # Use ReflectionAgentState directly - reducers are defined in the TypedDict
    return StateGraph(ReflectionAgentState)


def add_coaching_nodes(graph: StateGraph) -> None:
    """
    Add the core coaching nodes to the graph.

    Args:
        graph: StateGraph to add nodes to
    """

    # Core coaching workflow nodes
    graph.add_node("reasoning", reasoning_node)
    graph.add_node("planning", planning_node)
    graph.add_node("head_coach", head_coach_node)

    # For demo, only use strength coach to avoid complexity
    graph.add_node("strength_coach", strength_coach_node)


def add_reflection_nodes(graph: StateGraph) -> None:
    """
    Add reflection-specific nodes to the graph.

    Args:
        graph: StateGraph to add reflection nodes to
    """

    # Reflection workflow nodes
    graph.add_node("reflection", reflection_node)
    graph.add_node("regeneration", response_regeneration_node)
    graph.add_node("reflection_finalization", reflection_finalization_node)

    # Reflection routing nodes
    graph.add_node("reflection_router", reflection_router_node)
    graph.add_node("safety_gate", safety_gate_node)


def add_coaching_edges(graph: StateGraph) -> None:
    """
    Add edges for the core coaching workflow.

    Args:
        graph: StateGraph to add coaching edges to
    """

    # Core coaching flow - linear progression
    graph.add_edge(START, "reasoning")
    graph.add_edge("reasoning", "planning")
    graph.add_edge("planning", "head_coach")

    # For demo purposes, let's use a single specialized coach to avoid complexity
    # In production, you would use conditional edges based on routing_decision
    graph.add_edge("head_coach", "strength_coach")

    # Specialized coach goes directly to reflection router
    graph.add_edge("strength_coach", "reflection_router")


def add_reflection_edges(graph: StateGraph) -> None:
    """
    Add edges for the reflection workflow.

    Args:
        graph: StateGraph to add reflection edges to
    """

    # Simplified reflection flow to prevent infinite loops
    # Reflection router decides: reflect -> safety_gate -> finalize
    graph.add_conditional_edges(
        "reflection_router",
        reflection_router,
        {
            "reflect": "reflection",
            "regenerate": "regeneration",
            "finalize": "reflection_finalization",
            # Remove "continue" option that causes loops
        },
    )

    # After reflection, always go to safety gate (no loops back)
    graph.add_edge("reflection", "safety_gate")

    # Safety gate decides whether to regenerate or finalize (no loops back to reflection)
    graph.add_conditional_edges(
        "safety_gate",
        safety_gate_router,
        {"regenerate": "regeneration", "finalize": "reflection_finalization"},
    )

    # After regeneration, go directly to finalization (no more loops)
    graph.add_edge("regeneration", "reflection_finalization")

    # Finalization leads to end
    graph.add_edge("reflection_finalization", END)


# Wrapper node functions for routing integration
async def reflection_router_node(state: ReflectionAgentState) -> Dict[str, Any]:
    """Wrapper node for reflection router logic."""
    # The actual routing happens in the conditional edge
    # This node just passes through the state
    return {}


async def safety_gate_node(state: ReflectionAgentState) -> Dict[str, Any]:
    """Wrapper node for safety gate logic."""
    # The actual routing happens in the conditional edge
    # This node just passes through the state
    return {}


def create_reflection_enhanced_coaching_graph(
    checkpointer=None,
    user_profile: Optional[Dict[str, Any]] = None,
    reflection_config: Optional[Dict[str, Any]] = None,
    enable_reflection: bool = True,
) -> CompiledStateGraph:
    """
    Create a reflection-enhanced coaching graph.

    Args:
        checkpointer: Optional checkpointer for conversation persistence
        user_profile: Optional initial user profile
        reflection_config: Optional reflection configuration
        enable_reflection: Whether to enable reflection (default: True)

    Returns:
        Compiled StateGraph with reflection capabilities
    """

    logger = logging.getLogger(__name__)
    logger.info("Creating reflection-enhanced coaching graph")

    # Create state graph
    graph = create_reflection_enhanced_state_graph()

    # Add nodes
    add_coaching_nodes(graph)
    add_reflection_nodes(graph)

    # Add edges
    add_coaching_edges(graph)
    add_reflection_edges(graph)

    # Compile graph
    compiled_graph = graph.compile(checkpointer=checkpointer or MemorySaver())

    logger.info("Reflection-enhanced coaching graph compiled successfully")
    return compiled_graph


def create_initial_reflection_state(
    user_profile: Optional[Dict[str, Any]] = None,
    reflection_config: Optional[Dict[str, Any]] = None,
    enable_reflection: bool = True,
) -> ReflectionAgentState:
    """
    Create an initial reflection-enhanced state for coaching sessions.

    Args:
        user_profile: Optional user profile
        reflection_config: Optional reflection configuration
        enable_reflection: Whether reflection is enabled

    Returns:
        Initial ReflectionAgentState
    """

    # Create base state
    base_state = {
        "messages": [],
        "user_query": None,
        "user_profile": user_profile,
        "routing_decision": None,
        "pending_agents": None,
        "plan": None,
        "current_step": None,
        "domain_contributions": {},
        "required_domains": [],
        "completed_domains": [],
        "aggregated_plan": None,
        "proceed_to_generation": False,
        "current_plan": None,
        "is_onboarding": False,
        "strength_response": None,
        "running_response": None,
        "cardio_response": None,
        "cycling_response": None,
        "nutrition_response": None,
        "recovery_response": None,
        "mental_response": None,
        "reasoning_output": None,
        "clarification_output": None,
        "aggregated_response": None,
    }

    # Enhance with reflection capabilities
    reflection_state = enhance_state_with_reflection(
        base_state=base_state,
        reflection_config=reflection_config or create_reflection_config(),
    )

    # Set reflection enabled flag
    reflection_state["reflection_enabled"] = enable_reflection

    return reflection_state


# Factory function for different reflection configurations
def create_reflection_coaching_graph_with_config(
    config_type: str = "default",
    checkpointer=None,
    user_profile: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """
    Create reflection-enhanced coaching graph with predefined configurations.

    Args:
        config_type: Type of reflection configuration
                    Options: "default", "safety_focused", "high_quality", "minimal"
        checkpointer: Optional checkpointer
        user_profile: Optional user profile

    Returns:
        Compiled reflection-enhanced coaching graph
    """

    # Predefined configurations
    configs = {
        "default": create_reflection_config(
            max_reflections=2,
            safety_threshold=0.8,
            quality_threshold=0.75,
            enable_safety_validation=True,
        ),
        "safety_focused": create_reflection_config(
            max_reflections=3,
            safety_threshold=0.9,
            quality_threshold=0.7,
            enable_safety_validation=True,
            reflection_criteria=["safety", "contraindications", "injury_prevention"],
        ),
        "high_quality": create_reflection_config(
            max_reflections=3,
            safety_threshold=0.8,
            quality_threshold=0.85,
            enable_safety_validation=True,
            reflection_criteria=[
                "safety",
                "accuracy",
                "completeness",
                "clarity",
                "actionability",
            ],
        ),
        "minimal": create_reflection_config(
            max_reflections=1,
            safety_threshold=0.7,
            quality_threshold=0.7,
            enable_safety_validation=True,
            reflection_criteria=["safety"],
        ),
    }

    reflection_config = configs.get(config_type, configs["default"])

    return create_reflection_enhanced_coaching_graph(
        checkpointer=checkpointer,
        user_profile=user_profile,
        reflection_config=reflection_config,
        enable_reflection=True,
    )


# Example usage and testing functions
async def test_reflection_enhanced_graph():
    """Test function for the reflection-enhanced coaching graph."""

    logger = logging.getLogger(__name__)
    logger.info("Testing reflection-enhanced coaching graph")

    # Create graph
    graph = create_reflection_coaching_graph_with_config("safety_focused")

    # Create test state
    test_user_profile = {
        "name": "Test User",
        "fitness_level": "beginner",
        "goals": ["strength"],
        "limitations": ["knee injury"],
        "medical_conditions": ["hypertension"],
    }

    initial_state = create_initial_reflection_state(
        user_profile=test_user_profile, enable_reflection=True
    )

    # Add test message
    from langchain_core.messages import HumanMessage

    initial_state["messages"] = [
        HumanMessage(
            content="I want to start deadlifting but I have a knee injury. What should I do?"
        )
    ]

    # Test configuration
    config = {"configurable": {"thread_id": "reflection_test_123"}}

    logger.info("Reflection-enhanced graph test setup complete")
    return graph, initial_state, config


if __name__ == "__main__":
    # Simple test
    import asyncio

    async def run_test():
        graph, state, config = await test_reflection_enhanced_graph()
        print("Reflection-enhanced coaching graph created successfully!")
        print(f"Initial state keys: {list(state.keys())}")
        print(f"Reflection enabled: {state.get('reflection_enabled', False)}")

    asyncio.run(run_test())


# Studio-compatible wrapper function for LangGraph Studio
async def _archived_create_studio_reflection_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """
    Studio-compatible wrapper for creating reflection-enhanced coaching graph.

    Args:
        config: Optional dictionary with configuration values for Studio editing

    Returns:
        Compiled StateGraph with reflection capabilities
    """
    logger = logging.getLogger(__name__)  # Define logger within function scope
    logger.info("🚀 Creating studio reflection-enhanced coaching graph")

    if config is None:
        config = {}

    # Extract configuration values or use defaults
    checkpointer = None
    if config.get("mongodb_uri") and config.get("enable_memory", True):
        try:
            from langgraph.checkpoint.mongo import MongoDBSaver

            checkpointer = MongoDBSaver(
                connection_string=config["mongodb_uri"], db_name="athlea_reflection"
            )
        except ImportError:
            logger.warning("MongoDB not available, using MemorySaver")
            checkpointer = MemorySaver()
    else:
        checkpointer = MemorySaver()

    # Create reflection configuration from Studio config
    reflection_config = create_reflection_config(
        max_reflections=config.get("max_reflections", 2),
        safety_threshold=config.get("safety_threshold", 0.8),
        quality_threshold=0.75,
        enable_safety_validation=config.get("enable_safety_validation", True),
        reflection_criteria=config.get(
            "reflection_criteria",
            [
                "safety",
                "contraindications",
                "injury_prevention",
                "evidence_based",
                "personalization",
            ],
        ),
    )

    # Create user profile if provided
    user_profile = None
    if config.get("user_id"):
        user_profile = {"user_id": config["user_id"]}

    return create_reflection_enhanced_coaching_graph(
        checkpointer=checkpointer,
        user_profile=user_profile,
        reflection_config=reflection_config,
        enable_reflection=True,
    )
