"""
Main Coaching Graph with Advanced Memory Integration

Python translation of the main coaching graph from the TypeScript implementation.
Orchestrates all agents and implements the complete coaching workflow with
Phase 2 advanced memory features including:
- Multi-domain memory separation
- Advanced retrieval with hybrid search
- Memory summarization and decay
- Real-time analytics and monitoring
"""

import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

# Try to import PostgresSaver, but handle gracefully if not available
try:
    from langgraph.checkpoint.postgres import PostgresSaver

    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False
    logging.warning(
        "PostgreSQL checkpointer not available. Using memory checkpointer only."
    )

# Import Phase 2 Advanced Memory System
try:
    from athlea_langgraph.memory import (
        AdvancedMemoryManager,
        AdvancedRetrievalPipeline,
        AnalyticsConfig,
        CoachingDomain,
        DecayConfig,
        DomainMemoryManager,
        MemoryAnalyticsEngine,
        MemoryDecayManager,
        MemoryOperationType,
        MemorySummarizationEngine,
        MemoryType,
        RetrievalContext,
        SearchConfig,
        SummarizationConfig,
    )

    ADVANCED_MEMORY_AVAILABLE = True
except ImportError:
    ADVANCED_MEMORY_AVAILABLE = False
    logging.warning(
        "Phase 2 Advanced Memory System not available. Using basic memory only."
    )

from athlea_langgraph.agents.head_coach import (
    clarification_node,
    head_coach_node,
    head_coach_router,
)
from athlea_langgraph.agents.planning_node import planning_node
from athlea_langgraph.agents.reasoning_node import reasoning_node
from athlea_langgraph.agents.specialized_coaches import (
    cardio_coach_node,
    cycling_coach_node,
    mental_coach_node,
    nutrition_coach_node,
    recovery_coach_node,
    running_coach_node,
    strength_coach_node,
)
from athlea_langgraph.states import AgentState, messages_reducer

# Import the new aggregation node
try:
    from athlea_langgraph.agents.aggregation_node_v2 import aggregation_node_v2

    use_v2_aggregation = True
except ImportError:
    from athlea_langgraph.agents.aggregation_node import aggregation_node

    use_v2_aggregation = False
    logging.warning(
        "Using v1 aggregation node. Consider upgrading to v2 for better production features."
    )


# Define the checkpointer based on environment
def get_checkpointer():
    """Get appropriate checkpointer based on environment."""
    if POSTGRES_AVAILABLE and os.getenv("POSTGRES_CONNECTION_STRING"):
        # Use PostgreSQL for production
        return PostgresSaver.from_conn_string(os.getenv("POSTGRES_CONNECTION_STRING"))
    else:
        # Use in-memory for development
        return MemorySaver()


# Advanced Memory System Integration
class AdvancedMemoryCoachingGraph:
    """
    Advanced memory-enhanced coaching graph that integrates Phase 2 memory features.
    """

    def __init__(
        self,
        user_id: str,
        enable_advanced_memory: bool = True,
        memory_config: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the advanced memory coaching graph."""
        self.user_id = user_id
        self.enable_advanced_memory = (
            enable_advanced_memory and ADVANCED_MEMORY_AVAILABLE
        )

        if self.enable_advanced_memory:
            # Initialize advanced memory manager with correct parameters
            config = memory_config or {}
            self.memory_manager = AdvancedMemoryManager(
                environment=config.get("environment", "development"),
                enable_analytics=config.get("enable_analytics", True),
                enable_auto_maintenance=config.get("enable_auto_maintenance", True),
            )
            logging.info(f"Advanced memory system initialized for user {user_id}")
        else:
            self.memory_manager = None
            logging.info("Using basic memory system")

    async def memory_enhanced_reasoning_node(self, state: AgentState) -> Dict[str, Any]:
        """Enhanced reasoning node with advanced memory retrieval."""
        if not self.enable_advanced_memory:
            return await reasoning_node(state)

        logging.info("--- Advanced Memory-Enhanced Reasoning Node ---")

        try:
            # Extract user query from messages
            user_query = ""
            if state.get("messages"):
                last_message = state["messages"][-1]
                if hasattr(last_message, "content"):
                    user_query = last_message.content

            # Perform advanced memory retrieval
            retrieval_context = RetrievalContext(
                query=user_query,
                session_type="coaching",
                user_context=state.get("user_profile", {}),
                max_memories=10,
            )

            relevant_memories = await self.memory_manager.search_memories(
                user_id=self.user_id,
                query=user_query,
                session_type="coaching",
                limit=10,
                use_advanced_ranking=True,
            )

            # Add memory context to state
            memory_context = {
                "relevant_memories": relevant_memories,
                "memory_count": len(relevant_memories),
                "domains_covered": list(
                    set(
                        mem.get("metadata", {}).get("domain", "general")
                        for mem in relevant_memories
                    )
                ),
            }

            # Run original reasoning with enhanced state
            enhanced_state = {**state, "memory_context": memory_context}
            result = await reasoning_node(enhanced_state)

            # Track analytics
            if self.memory_manager.analytics_engine:
                await self.memory_manager.analytics_engine.track_event(
                    user_id=self.user_id,
                    operation_type=MemoryOperationType.SEARCH,
                    duration_ms=100,  # Placeholder
                    success=True,
                    metadata={
                        "memories_retrieved": len(relevant_memories),
                        "query_length": len(user_query),
                        "session_type": "coaching",
                    },
                )

            return result

        except Exception as e:
            logging.error(f"Error in advanced memory reasoning: {e}")
            # Fallback to basic reasoning
            return await reasoning_node(state)

    async def memory_enhanced_aggregation_node(
        self, state: AgentState
    ) -> Dict[str, Any]:
        """Enhanced aggregation node with memory storage and summarization."""
        # Run original aggregation
        if use_v2_aggregation:
            result = await aggregation_node_v2(state)
        else:
            result = await aggregation_node(state)

        if not self.enable_advanced_memory:
            return result

        logging.info("--- Advanced Memory-Enhanced Aggregation Node ---")

        try:
            # Extract conversation for storage
            conversation_content = ""
            if state.get("messages"):
                conversation_content = "\n".join(
                    [
                        f"{msg.type}: {msg.content}"
                        for msg in state["messages"][-5:]  # Last 5 messages
                        if hasattr(msg, "content")
                    ]
                )

            # Store conversation with domain classification
            if conversation_content:
                memory_id = await self.memory_manager.store_memory(
                    user_id=self.user_id,
                    content=conversation_content,
                    memory_type=MemoryType.CONVERSATION,
                    metadata={
                        "session_type": "coaching",
                        "timestamp": datetime.now().isoformat(),
                        "user_profile": state.get("user_profile", {}),
                        "specialist_responses": {
                            k: v
                            for k, v in state.items()
                            if k.endswith("_response") and v
                        },
                    },
                )

                logging.info(f"Stored coaching session memory: {memory_id}")

            # Perform memory maintenance (summarization, decay)
            await self.memory_manager.run_memory_maintenance(user_id=self.user_id)

            return result

        except Exception as e:
            logging.error(f"Error in advanced memory aggregation: {e}")
            return result

    def get_memory_enhanced_nodes(self) -> Dict[str, Any]:
        """Get dictionary of memory-enhanced node functions."""
        if self.enable_advanced_memory:
            return {
                "reasoning": self.memory_enhanced_reasoning_node,
                "aggregate_responses": self.memory_enhanced_aggregation_node,
            }
        else:
            return {
                "reasoning": reasoning_node,
                "aggregate_responses": (
                    aggregation_node_v2 if use_v2_aggregation else aggregation_node
                ),
            }


def get_compiled_coaching_graph(
    checkpointer=None,
    user_profile: Optional[Dict[str, Any]] = None,
    current_plan: Optional[Dict[str, Any]] = None,
    use_streaming: bool = True,
    enable_performance_monitoring: bool = True,
    # Advanced Memory Parameters
    user_id: Optional[str] = None,
    enable_advanced_memory: bool = True,
    memory_config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """
    Create and compile the coaching graph with production-ready features and advanced memory.

    This follows the TypeScript implementation structure with Phase 2 memory enhancements:
    1. Memory-enhanced reasoning node analyzes the request with context retrieval
    2. Planning node creates execution plan
    3. Head coach routes to specialists
    4. Specialists provide responses
    5. Memory-enhanced aggregation node synthesizes, stores memories, and interrupts for HITL

    Args:
        checkpointer: Optional checkpointer for state persistence
        user_profile: Optional user profile data
        current_plan: Optional current training plan
        use_streaming: Enable streaming for better UX (default: True)
        enable_performance_monitoring: Enable performance metrics (default: True)
        user_id: User identifier for advanced memory features
        enable_advanced_memory: Enable Phase 2 advanced memory system (default: True)
        memory_config: Configuration for advanced memory components

    Returns:
        Compiled state graph ready for execution with advanced memory integration
    """
    print("Creating and compiling coaching graph with advanced memory...")

    # Configure logging
    if enable_performance_monitoring:
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )

    # Initialize advanced memory system if enabled
    memory_graph = None
    if enable_advanced_memory and user_id and ADVANCED_MEMORY_AVAILABLE:
        memory_graph = AdvancedMemoryCoachingGraph(
            user_id=user_id,
            enable_advanced_memory=enable_advanced_memory,
            memory_config=memory_config,
        )
        print(f"Advanced memory system enabled for user: {user_id}")
    else:
        if enable_advanced_memory and not user_id:
            logging.warning(
                "Advanced memory requested but no user_id provided. Using basic memory."
            )
        elif enable_advanced_memory and not ADVANCED_MEMORY_AVAILABLE:
            logging.warning(
                "Advanced memory requested but system not available. Using basic memory."
            )

    # Create the graph
    workflow = StateGraph(AgentState)

    # Get memory-enhanced nodes if available
    if memory_graph:
        enhanced_nodes = memory_graph.get_memory_enhanced_nodes()
        workflow.add_node("reasoning", enhanced_nodes["reasoning"])
        workflow.add_node("aggregate_responses", enhanced_nodes["aggregate_responses"])
    else:
        workflow.add_node("reasoning", reasoning_node)
        if use_v2_aggregation:
            workflow.add_node("aggregate_responses", aggregation_node_v2)
        else:
            workflow.add_node("aggregate_responses", aggregation_node)

    # Add all other nodes (unchanged)
    workflow.add_node("planning", planning_node)
    workflow.add_node("head_coach", head_coach_node)
    workflow.add_node("clarification", clarification_node)
    workflow.add_node("strength_coach", strength_coach_node)
    workflow.add_node("running_coach", running_coach_node)
    workflow.add_node("cardio_coach", cardio_coach_node)
    workflow.add_node("cycling_coach", cycling_coach_node)
    workflow.add_node("nutrition_coach", nutrition_coach_node)
    workflow.add_node("recovery_coach", recovery_coach_node)
    workflow.add_node("mental_coach", mental_coach_node)

    # Define the flow (unchanged)
    # Start -> Reasoning -> Planning -> Head Coach
    workflow.add_edge(START, "reasoning")
    workflow.add_edge("reasoning", "planning")
    workflow.add_edge("planning", "head_coach")

    # Head coach uses conditional routing
    workflow.add_conditional_edges(
        "head_coach",
        head_coach_router,
        {
            "strength_coach": "strength_coach",
            "running_coach": "running_coach",
            "cardio_coach": "cardio_coach",
            "cycling_coach": "cycling_coach",
            "nutrition_coach": "nutrition_coach",
            "recovery_coach": "recovery_coach",
            "mental_coach": "mental_coach",
            "clarification": "clarification",
            "aggregate_responses": "aggregate_responses",
        },
    )

    # All specialist coaches go back to head coach for potential multi-specialist routing
    workflow.add_edge("strength_coach", "head_coach")
    workflow.add_edge("running_coach", "head_coach")
    workflow.add_edge("cardio_coach", "head_coach")
    workflow.add_edge("cycling_coach", "head_coach")
    workflow.add_edge("nutrition_coach", "head_coach")
    workflow.add_edge("recovery_coach", "head_coach")
    workflow.add_edge("mental_coach", "head_coach")

    # Clarification goes to END (user needs to provide more info)
    workflow.add_edge("clarification", END)

    # Aggregation goes to END (after HITL interrupt)
    workflow.add_edge("aggregate_responses", END)

    # Use provided checkpointer or get default
    if checkpointer is None:
        checkpointer = get_checkpointer()

    # Compile with checkpointer and configuration
    compiled_graph = workflow.compile(
        checkpointer=checkpointer,
        # Add any additional compilation options here
    )

    memory_status = (
        "advanced" if memory_graph and memory_graph.enable_advanced_memory else "basic"
    )
    print(
        f"Coaching graph compiled successfully (memory: {memory_status}, v2 aggregation: {use_v2_aggregation})."
    )
    return compiled_graph


# Removed run_coaching_session, get_latest_response, get_conversation_summary
# These will be handled by the script calling the graph.


# Convenience Functions for Advanced Memory Integration
def create_memory_enhanced_coaching_graph(
    user_id: str,
    checkpointer=None,
    user_profile: Optional[Dict[str, Any]] = None,
    current_plan: Optional[Dict[str, Any]] = None,
    memory_config: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> CompiledStateGraph:
    """
    Convenience function to create a coaching graph with advanced memory enabled.

    Args:
        user_id: User identifier (required for advanced memory)
        checkpointer: Optional checkpointer for state persistence
        user_profile: Optional user profile data
        current_plan: Optional current training plan
        memory_config: Configuration for advanced memory components
        **kwargs: Additional arguments passed to get_compiled_coaching_graph

    Returns:
        Compiled coaching graph with advanced memory features
    """
    return get_compiled_coaching_graph(
        checkpointer=checkpointer,
        user_profile=user_profile,
        current_plan=current_plan,
        user_id=user_id,
        enable_advanced_memory=True,
        memory_config=memory_config,
        **kwargs,
    )


def create_basic_coaching_graph(
    checkpointer=None,
    user_profile: Optional[Dict[str, Any]] = None,
    current_plan: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> CompiledStateGraph:
    """
    Convenience function to create a coaching graph with basic memory only.

    Args:
        checkpointer: Optional checkpointer for state persistence
        user_profile: Optional user profile data
        current_plan: Optional current training plan
        **kwargs: Additional arguments passed to get_compiled_coaching_graph

    Returns:
        Compiled coaching graph with basic memory features
    """
    return get_compiled_coaching_graph(
        checkpointer=checkpointer,
        user_profile=user_profile,
        current_plan=current_plan,
        enable_advanced_memory=False,
        **kwargs,
    )


async def get_memory_analytics(
    user_id: str, memory_config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Get memory analytics for a specific user.

    Args:
        user_id: User identifier
        memory_config: Optional memory configuration

    Returns:
        Dictionary containing memory analytics and health status
    """
    if not ADVANCED_MEMORY_AVAILABLE:
        return {"error": "Advanced memory system not available"}

    try:
        config = memory_config or {}
        memory_manager = AdvancedMemoryManager(
            environment=config.get("environment", "development"),
            enable_analytics=config.get("enable_analytics", True),
            enable_auto_maintenance=config.get("enable_auto_maintenance", True),
        )

        # Get analytics report
        analytics_report = await memory_manager.get_analytics_report(user_id=user_id)

        # Get health status
        health_status = await memory_manager.get_system_health()

        return {
            "analytics_report": analytics_report,
            "health_status": health_status,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logging.error(f"Error getting memory analytics for user {user_id}: {e}")
        return {"error": str(e)}


# Export the main functions and classes
__all__ = [
    "get_compiled_coaching_graph",
    "create_memory_enhanced_coaching_graph",
    "create_basic_coaching_graph",
    "get_memory_analytics",
    "AdvancedMemoryCoachingGraph",
    "get_checkpointer",
    "ADVANCED_MEMORY_AVAILABLE",
]
