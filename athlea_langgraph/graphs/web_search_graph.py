"""
Web Search LangGraph Workflow

LangGraph implementation for comprehensive web research workflow.
Based on the patterns from john-adeojo/graph_websearch_agent repository.
"""

import logging
from typing import Any, Dict, Optional, List

from langchain_core.language_models import BaseChatModel
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.postgres import <PERSON>gresSaver
from langgraph.checkpoint.memory import MemorySaver

from ..states.web_search_state import WebSearchState, DEFAULT_WEB_SEARCH_STATE
from ..agents.web_search_agents import (
    WebSearchPlannerAgent,
    WebSearchExecutorAgent,
    WebContentScrapingAgent,
    WebContentAnalyzerAgent,
    WebResearchSynthesizerAgent,
)
from ..tools.web_search_tool import (
    WebSearchTool,
    WebScrapingTool,
    SerperSearchTool,
    WebScraperTool,
)
from ..prompt_system import PromptRegistry

logger = logging.getLogger(__name__)


class WebSearchGraph:
    """
    LangGraph implementation for web search and research workflow.

    Implements a multi-agent system that:
    1. Plans research strategy
    2. Executes web searches
    3. Scrapes relevant content
    4. Analyzes content quality
    5. Synthesizes research findings
    """

    def __init__(
        self,
        llm: BaseChatModel,
        serper_api_key: Optional[str] = None,
        checkpoint_saver: Optional[Any] = None,
        prompt_system: Optional[PromptRegistry] = None,
        max_search_results: int = 10,
        max_pages_to_scrape: int = 5,
    ):
        self.llm = llm
        self.serper_api_key = serper_api_key
        self.checkpoint_saver = checkpoint_saver or MemorySaver()
        self.prompt_system = prompt_system or PromptRegistry()
        self.max_search_results = max_search_results
        self.max_pages_to_scrape = max_pages_to_scrape

        # Initialize tools
        self.search_tool = WebSearchTool(api_key=serper_api_key)
        self.scraping_tool = WebScrapingTool()

        # Initialize agents
        self.planner_agent = WebSearchPlannerAgent(llm, prompt_system)
        self.search_agent = WebSearchExecutorAgent(self.search_tool)
        self.scraper_agent = WebContentScrapingAgent(self.scraping_tool)
        self.analyzer_agent = WebContentAnalyzerAgent(llm)
        self.synthesizer_agent = WebResearchSynthesizerAgent(llm)

        # Build the graph
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow."""

        # Create the state graph
        workflow = StateGraph(WebSearchState)

        # Add nodes for each agent
        workflow.add_node("planner", self._planner_node)
        workflow.add_node("searcher", self._searcher_node)
        workflow.add_node("scraper", self._scraper_node)
        workflow.add_node("analyzer", self._analyzer_node)
        workflow.add_node("synthesizer", self._synthesizer_node)
        workflow.add_node("end", self._end_node)

        # Define the workflow edges
        workflow.set_entry_point("planner")
        workflow.add_edge("planner", "searcher")
        workflow.add_edge("searcher", "scraper")
        workflow.add_edge("scraper", "analyzer")
        workflow.add_edge("analyzer", "synthesizer")
        workflow.add_edge("synthesizer", "end")
        workflow.set_finish_point("end")

        return workflow

    async def _planner_node(self, state: WebSearchState) -> WebSearchState:
        """Research planning node."""
        logger.info("Executing planner node")
        try:
            updated_state = await self.planner_agent.plan_research(state)
            logger.info(
                f"Planning complete. Generated {len(updated_state.search_queries)} search queries"
            )
            return updated_state
        except Exception as e:
            logger.error(f"Planner node failed: {str(e)}")
            state.search_errors.append(f"Planning failed: {str(e)}")
            state.current_step = "error"
            return state

    async def _searcher_node(self, state: WebSearchState) -> WebSearchState:
        """Web search execution node."""
        logger.info("Executing searcher node")
        try:
            updated_state = await self.search_agent.execute_searches(state)
            logger.info(
                f"Search complete. Found {len(updated_state.search_results)} total results"
            )
            return updated_state
        except Exception as e:
            logger.error(f"Searcher node failed: {str(e)}")
            state.search_errors.append(f"Search execution failed: {str(e)}")
            state.current_step = "error"
            return state

    async def _scraper_node(self, state: WebSearchState) -> WebSearchState:
        """Content scraping node."""
        logger.info("Executing scraper node")
        try:
            updated_state = await self.scraper_agent.scrape_content(state)
            successful_scrapes = len(updated_state.get_successful_content())
            logger.info(f"Scraping complete. {successful_scrapes} successful scrapes")
            return updated_state
        except Exception as e:
            logger.error(f"Scraper node failed: {str(e)}")
            state.scraping_errors.append(f"Content scraping failed: {str(e)}")
            state.current_step = "error"
            return state

    async def _analyzer_node(self, state: WebSearchState) -> WebSearchState:
        """Content analysis node."""
        logger.info("Executing analyzer node")
        try:
            updated_state = await self.analyzer_agent.analyze_content(state)
            logger.info(
                f"Analysis complete. Quality score: {updated_state.quality_score}"
            )
            return updated_state
        except Exception as e:
            logger.error(f"Analyzer node failed: {str(e)}")
            state.content_analysis = f"Analysis failed: {str(e)}"
            state.current_step = (
                "synthesis"  # Continue to synthesis even if analysis fails
            )
            return state

    async def _synthesizer_node(self, state: WebSearchState) -> WebSearchState:
        """Research synthesis node."""
        logger.info("Executing synthesizer node")
        try:
            updated_state = await self.synthesizer_agent.synthesize_research(state)
            logger.info("Research synthesis complete")
            return updated_state
        except Exception as e:
            logger.error(f"Synthesizer node failed: {str(e)}")
            state.research_summary = f"Synthesis failed: {str(e)}"
            state.current_step = "error"
            return state

    async def _end_node(self, state: WebSearchState) -> WebSearchState:
        """Final node to mark completion."""
        logger.info("Web search workflow completed")
        if state.current_step != "error":
            state.current_step = "complete"
            state.validation_status = "completed"
        return state

    def compile(self) -> Any:
        """Compile the graph with checkpoint support."""
        return self.graph.compile(checkpointer=self.checkpoint_saver)

    async def run_research(
        self,
        research_question: str,
        research_context: Optional[str] = None,
        research_type: str = "general",
        thread_id: Optional[str] = None,
    ) -> WebSearchState:
        """
        Run a complete web research workflow.

        Args:
            research_question: The research question to investigate
            research_context: Additional context for the research
            research_type: Type of research (general, news, academic, etc.)
            thread_id: Thread ID for checkpoint management

        Returns:
            Final state with research results
        """

        # Initialize state
        initial_state = WebSearchState(
            **DEFAULT_WEB_SEARCH_STATE,
            research_question=research_question,
            research_context=research_context,
            research_type=research_type,
            max_search_results=self.max_search_results,
            max_pages_to_scrape=self.max_pages_to_scrape,
        )

        # Compile and run the workflow
        compiled_graph = self.compile()

        config = {"recursion_limit": 50}
        if thread_id:
            config["configurable"] = {"thread_id": thread_id}

        try:
            logger.info(f"Starting web research for: {research_question}")

            # Run the workflow
            final_state = None
            async for state in compiled_graph.astream(initial_state, config=config):
                final_state = state
                logger.debug(f"State update: {state.current_step}")

            if final_state:
                logger.info(
                    f"Web research completed with status: {final_state.current_step}"
                )
                return final_state
            else:
                logger.error("Workflow execution failed - no final state")
                initial_state.current_step = "error"
                initial_state.research_summary = "Workflow execution failed"
                return initial_state

        except Exception as e:
            logger.error(f"Web research workflow failed: {str(e)}")
            initial_state.current_step = "error"
            initial_state.research_summary = f"Workflow failed: {str(e)}"
            return initial_state


class WebSearchGraphFactory:
    """Factory for creating web search graphs with different configurations."""

    @staticmethod
    def create_basic_graph(
        llm: BaseChatModel,
        serper_api_key: Optional[str] = None,
    ) -> WebSearchGraph:
        """Create a basic web search graph."""
        return WebSearchGraph(
            llm=llm,
            serper_api_key=serper_api_key,
            checkpoint_saver=MemorySaver(),
            max_search_results=5,
            max_pages_to_scrape=3,
        )

    @staticmethod
    def create_comprehensive_graph(
        llm: BaseChatModel,
        serper_api_key: Optional[str] = None,
        postgres_saver: Optional[PostgresSaver] = None,
    ) -> WebSearchGraph:
        """Create a comprehensive web search graph with persistence."""
        return WebSearchGraph(
            llm=llm,
            serper_api_key=serper_api_key,
            checkpoint_saver=postgres_saver or MemorySaver(),
            max_search_results=10,
            max_pages_to_scrape=5,
        )

    @staticmethod
    def create_research_graph(
        llm: BaseChatModel,
        serper_api_key: Optional[str] = None,
        prompt_system: Optional[PromptRegistry] = None,
    ) -> WebSearchGraph:
        """Create a research-focused web search graph."""
        return WebSearchGraph(
            llm=llm,
            serper_api_key=serper_api_key,
            prompt_system=prompt_system,
            max_search_results=15,
            max_pages_to_scrape=8,
        )


# Utility functions for integration


async def quick_web_search(
    question: str,
    llm: BaseChatModel,
    serper_api_key: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Quick utility function for simple web searches.

    Args:
        question: Research question
        llm: Language model
        serper_api_key: Serper API key

    Returns:
        Dictionary with research results
    """

    graph = WebSearchGraphFactory.create_basic_graph(llm, serper_api_key)
    result_state = await graph.run_research(question)

    return {
        "research_question": result_state.research_question,
        "research_summary": result_state.research_summary,
        "sources_analyzed": len(result_state.successful_scrapes),
        "total_content_length": result_state.get_total_content_length(),
        "quality_score": result_state.quality_score,
        "status": result_state.current_step,
        "sources_cited": result_state.sources_cited,
        "key_findings": result_state.key_findings,
    }


async def comprehensive_web_research(
    question: str,
    context: Optional[str],
    llm: BaseChatModel,
    serper_api_key: Optional[str] = None,
) -> WebSearchState:
    """
    Comprehensive research utility function.

    Args:
        question: Research question
        context: Additional context
        llm: Language model
        serper_api_key: Serper API key

    Returns:
        Complete WebSearchState with all research data
    """

    graph = WebSearchGraphFactory.create_comprehensive_graph(llm, serper_api_key)
    return await graph.run_research(question, context)


# Example usage patterns


def create_research_config() -> Dict[str, Any]:
    """Create a default research configuration."""
    return {
        "max_search_results": 10,
        "max_pages_to_scrape": 5,
        "research_type": "general",
        "timeout_seconds": 30,
        "enable_analysis": True,
        "enable_synthesis": True,
    }
