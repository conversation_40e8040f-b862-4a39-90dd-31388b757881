"""
LangGraph Studio compatible graph definition for the coaching application.

This module provides a graph that can be visualized and debugged in LangGraph Studio
with proper state management, interrupts, and human-in-the-loop capabilities.
"""

import logging
import os
from typing import Annotated, Any, Dict, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, interrupt
from pydantic import BaseModel, Field

from ...graphs.archived.coaching_graph_with_memory import MemoryEnhancedCoachingGraph
from ...states.state import AgentState

logger = logging.getLogger(__name__)


class StudioConfig(BaseModel):
    """Configuration schema for LangGraph Studio."""

    user_id: str = Field(default="studio_user", description="User identifier")
    mongodb_uri: str = Field(
        default="mongodb://host.docker.internal:27017",
        description="MongoDB URI for Docker",
    )
    thread_id: str = Field(default="studio_thread", description="Thread identifier")
    enable_memory: bool = Field(default=True, description="Enable memory integration")


class StudioState(AgentState):
    """Extended state for LangGraph Studio with additional debugging fields."""

    # Studio-specific fields for debugging
    current_node: Optional[str] = None
    execution_steps: list = Field(default_factory=list)
    debug_info: dict = Field(default_factory=dict)

    # Human-in-the-loop support
    awaiting_human_input: bool = False
    human_input_prompt: Optional[str] = None


async def _experimental_create_studio_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """
    Create a LangGraph Studio compatible coaching graph.

    This function is called by LangGraph Studio to create the graph instance.
    """
    if config is None:
        config = {}

    # Parse configuration
    studio_config = StudioConfig(**config)

    logger.info(f"Creating studio graph with config: {studio_config}")

    # Create the underlying coaching graph
    coaching_graph = MemoryEnhancedCoachingGraph(
        mongodb_uri=studio_config.mongodb_uri, user_id=studio_config.user_id
    )

    # Build graph with proper state management for Studio
    return await build_studio_graph(coaching_graph, studio_config)


async def build_studio_graph(
    coaching_graph: MemoryEnhancedCoachingGraph, config: StudioConfig
) -> CompiledStateGraph:
    """Build the graph with Studio-specific enhancements."""

    # Import all the required components
    from ...agents.aggregation_node_v2 import aggregation_node_v2
    from ...agents.head_coach import (
        clarification_node,
        head_coach_node,
        head_coach_router,
    )
    from ...agents.planning_node import planning_node
    from ...agents.reasoning_node import reasoning_node
    from ...agents.specialized_coaches import (
        cardio_coach_node,
        cycling_coach_node,
        mental_coach_node,
        nutrition_coach_node,
        recovery_coach_node,
        running_coach_node,
        strength_coach_node,
    )

    # Create studio-enhanced node wrappers
    async def studio_reasoning_node(state: StudioState) -> Dict[str, Any]:
        """Reasoning node with studio enhancements."""
        state["current_node"] = "reasoning"
        if "execution_steps" not in state:
            state["execution_steps"] = []
        state["execution_steps"].append("reasoning")

        if config.enable_memory:
            result = await coaching_graph.memory_enhanced_reasoning_node(state)
        else:
            result = await reasoning_node(state)

        result["current_node"] = "reasoning"
        result["debug_info"] = {"node": "reasoning", "timestamp": "now"}
        return result

    async def studio_planning_node(state: StudioState) -> Dict[str, Any]:
        """Planning node with studio enhancements."""
        state["current_node"] = "planning"
        if "execution_steps" not in state:
            state["execution_steps"] = []
        state["execution_steps"].append("planning")

        result = await planning_node(state)
        result["current_node"] = "planning"
        result["debug_info"] = {"node": "planning", "plan": result.get("plan")}
        return result

    async def studio_head_coach_node(state: StudioState) -> Dict[str, Any]:
        """Head coach node with studio enhancements."""
        state["current_node"] = "head_coach"
        if "execution_steps" not in state:
            state["execution_steps"] = []
        state["execution_steps"].append("head_coach")

        result = await head_coach_node(state)
        result["current_node"] = "head_coach"
        result["debug_info"] = {
            "node": "head_coach",
            "routing": result.get("routing_decision"),
        }
        return result

    async def studio_clarification_node(state: StudioState) -> Dict[str, Any]:
        """Clarification node with human-in-the-loop support."""
        state["current_node"] = "clarification"
        if "execution_steps" not in state:
            state["execution_steps"] = []
        state["execution_steps"].append("clarification")

        # Check if we should interrupt for human input
        if state.get("awaiting_human_input", False):
            user_input = interrupt(
                value={
                    "message": "Clarification needed. Please provide additional details.",
                    "current_query": state.get("user_query", ""),
                    "suggestions": [
                        "What are your fitness goals?",
                        "What's your experience level?",
                        "Do you have any limitations?",
                    ],
                }
            )

            if user_input:
                # Update the user query with additional context
                updated_query = (
                    f"{state.get('user_query', '')} Additional context: {user_input}"
                )
                result = {
                    "user_query": updated_query,
                    "clarification_output": f"Thank you for the additional information: {user_input}. Let me help you with that.",
                    "current_node": "clarification",
                    "awaiting_human_input": False,
                }
                return result

        result = await clarification_node(state)
        result["current_node"] = "clarification"
        return result

    async def studio_aggregation_node(state: StudioState) -> Dict[str, Any]:
        """Aggregation node with memory and studio enhancements."""
        state["current_node"] = "aggregation"
        if "execution_steps" not in state:
            state["execution_steps"] = []
        state["execution_steps"].append("aggregation")

        if config.enable_memory:
            result = await coaching_graph.memory_enhanced_aggregation_node(state)
        else:
            result = await aggregation_node_v2(state)

        result["current_node"] = "aggregation"
        result["debug_info"] = {
            "node": "aggregation",
            "specialists_used": state.get("completed_domains", []),
        }
        return result

    # Create specialized coach wrappers
    async def create_specialist_wrapper(specialist_func, name):
        async def wrapper(state: StudioState) -> Dict[str, Any]:
            state["current_node"] = name
            if "execution_steps" not in state:
                state["execution_steps"] = []
            state["execution_steps"].append(name)

            result = await specialist_func(state)
            result["current_node"] = name
            result["debug_info"] = {
                "node": name,
                "response_length": len(str(result.get(f"{name}_response", ""))),
            }
            return result

        return wrapper

    # Create the graph builder
    builder = StateGraph(StudioState)

    # Add all nodes with studio enhancements
    builder.add_node("reasoning", studio_reasoning_node)
    builder.add_node("planning", studio_planning_node)
    builder.add_node("head_coach", studio_head_coach_node)
    builder.add_node("clarification", studio_clarification_node)
    builder.add_node("aggregate_responses", studio_aggregation_node)

    # Add specialist coaches
    builder.add_node(
        "strength_coach",
        await create_specialist_wrapper(strength_coach_node, "strength_coach"),
    )
    builder.add_node(
        "running_coach",
        await create_specialist_wrapper(running_coach_node, "running_coach"),
    )
    builder.add_node(
        "cardio_coach",
        await create_specialist_wrapper(cardio_coach_node, "cardio_coach"),
    )
    builder.add_node(
        "cycling_coach",
        await create_specialist_wrapper(cycling_coach_node, "cycling_coach"),
    )
    builder.add_node(
        "nutrition_coach",
        await create_specialist_wrapper(nutrition_coach_node, "nutrition_coach"),
    )
    builder.add_node(
        "recovery_coach",
        await create_specialist_wrapper(recovery_coach_node, "recovery_coach"),
    )
    builder.add_node(
        "mental_coach",
        await create_specialist_wrapper(mental_coach_node, "mental_coach"),
    )

    # Add a human input node for demonstration
    async def human_input_node(state: StudioState) -> Dict[str, Any]:
        """Node for collecting human input in Studio."""
        state["current_node"] = "human_input"
        if "execution_steps" not in state:
            state["execution_steps"] = []
        state["execution_steps"].append("human_input")

        user_input = interrupt(
            value={
                "message": "Please provide additional input or feedback:",
                "context": state.get("user_query", ""),
                "current_step": len(state["execution_steps"]),
            }
        )

        return {
            "messages": state.get("messages", []) + [HumanMessage(content=user_input)],
            "user_query": f"{state.get('user_query', '')} [Human feedback: {user_input}]",
            "current_node": "human_input",
            "awaiting_human_input": False,
        }

    builder.add_node("human_input", human_input_node)

    # Add edges (same structure as the main graph)
    builder.add_edge("reasoning", "planning")
    builder.add_edge("planning", "head_coach")
    builder.add_edge("strength_coach", "head_coach")
    builder.add_edge("running_coach", "head_coach")
    builder.add_edge("cardio_coach", "head_coach")
    builder.add_edge("cycling_coach", "head_coach")
    builder.add_edge("nutrition_coach", "head_coach")
    builder.add_edge("recovery_coach", "head_coach")
    builder.add_edge("mental_coach", "head_coach")
    builder.add_edge("clarification", "head_coach")
    builder.add_edge("human_input", "head_coach")
    builder.add_edge("aggregate_responses", END)

    # Add conditional edges
    builder.add_conditional_edges("head_coach", head_coach_router)

    # Set entry point
    builder.add_edge(START, "reasoning")

    # Compile with memory if enabled
    if config.enable_memory:
        checkpointer = coaching_graph.checkpointer
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Studio graph compiled successfully")
    return graph


# Example usage function for testing
async def create_test_graph():
    """Create a test graph for development."""
    config = {
        "user_id": "test_user",
        "mongodb_uri": os.getenv("MONGODB_URI", "mongodb://localhost:27017"),
        "thread_id": "test_thread",
        "enable_memory": False,  # Disable for testing without MongoDB
    }

    return await _experimental_create_studio_graph(config)
