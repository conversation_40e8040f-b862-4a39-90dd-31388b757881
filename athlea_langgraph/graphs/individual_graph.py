"""
Individual Coach Graphs

This module creates individual graphs for each coach that reuse the same
domain agents as the optimized coaching graph but with simplified routing.

Instead of duplicating the entire comprehensive graph, we create focused
graphs that:
1. Start with a reasoning node to analyze the user's request
2. Continue to the specified domain agent
3. Have access to the same tools and capabilities
4. Use the same underlying domain agents
5. Follow a simple flow: START → reasoning → domain_coach → END

This allows each coach to work independently while maintaining the same
setup and capabilities as the comprehensive system, with the added benefit
of request analysis through the reasoning node.
"""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, Field

# Import domain agents directly (same as main coaching graph)
from ..agents.cardio_agent import cardio_agent
from ..agents.mental_agent import mental_agent
from ..agents.nutrition_agent import nutrition_agent
from ..agents.recovery_agent import recovery_agent
from ..agents.strength_agent import strength_agent

# Import reasoning node
from ..agents.reasoning_node import reasoning_node

# Import the same state and config as main coaching graph
from ..states.optimized_state import OptimizedCoachingState
from .coaching_graph import ComprehensiveCoachingConfig

logger = logging.getLogger(__name__)

# Available coaches (same as main coaching graph)
AVAILABLE_COACHES = [
    "strength_coach",
    "cardio_coach",
    "cycling_coach",
    "nutrition_coach",
    "recovery_coach",
    "mental_coach",
]

# Domain agents mapping (same as main coaching graph)
DOMAIN_AGENTS = {
    "strength_coach": strength_agent,
    "cardio_coach": cardio_agent,
    "nutrition_coach": nutrition_agent,
    "recovery_coach": recovery_agent,
    "mental_coach": mental_agent,
}

# Global initialization lock
_initialization_lock = asyncio.Lock()
_agents_initialized = False


async def initialize_domain_agents():
    """Initialize domain agents with their tools (same pattern as main coaching graph)."""
    global _agents_initialized

    async with _initialization_lock:
        if _agents_initialized:
            logger.info("✅ INDIVIDUAL_COACH: Domain agents already initialized.")
            return

        logger.info("🚀 INDIVIDUAL_COACH: Initializing domain agents...")

        # Ensure all agents have their tools loaded
        for agent_name, agent in DOMAIN_AGENTS.items():
            if not agent.tools:
                await agent.get_domain_tools()
            logger.info(
                f"✅ INDIVIDUAL_COACH: {agent_name} loaded with {len(agent.tools)} tools"
            )

        _agents_initialized = True
        logger.info("✅ INDIVIDUAL_COACH: Domain agents initialized successfully.")


async def create_individual_coach_graph(
    coach_name: str,
    config: Optional[Union[Dict[str, Any], ComprehensiveCoachingConfig]] = None,
) -> CompiledStateGraph:
    """
    Create an individual coach graph using domain agents directly with reasoning.

    Args:
        coach_name: The specific coach to create a graph for (e.g., "strength_coach")
        config: Optional configuration parameters

    Returns:
        Compiled graph for the individual coach with reasoning node
    """
    if coach_name not in AVAILABLE_COACHES:
        raise ValueError(
            f"Coach '{coach_name}' not available. Choose from: {AVAILABLE_COACHES}"
        )

    logger.info(f"🏗️ INDIVIDUAL_COACH: Creating graph with reasoning for {coach_name}")

    if config is None:
        config = {}

    # Parse configuration (same as main coaching graph)
    if isinstance(config, ComprehensiveCoachingConfig):
        coaching_config = config
    elif isinstance(config, dict):
        coaching_config = ComprehensiveCoachingConfig(**config)
    else:
        coaching_config = ComprehensiveCoachingConfig()

    # Initialize domain agents
    await initialize_domain_agents()

    # Handle cycling_coach (uses cardio_agent for now)
    if coach_name == "cycling_coach":
        if "cycling_coach" not in DOMAIN_AGENTS:
            logger.info("🚴 INDIVIDUAL_COACH: Using cardio_agent for cycling_coach")
            agent_instance = cardio_agent
        else:
            agent_instance = DOMAIN_AGENTS["cycling_coach"]
    else:
        if coach_name not in DOMAIN_AGENTS:
            raise ValueError(f"Domain agent not found for {coach_name}")
        agent_instance = DOMAIN_AGENTS[coach_name]

    # Build the individual coach graph with reasoning
    uncompiled_graph = build_individual_coach_graph_with_reasoning(
        coach_name, agent_instance
    )

    # Compile with memory if enabled
    if coaching_config.enable_memory:
        logger.info(f"🧠 INDIVIDUAL_COACH: Memory enabled for {coach_name}")
        memory = MemorySaver()
        graph = uncompiled_graph.compile(checkpointer=memory)
    else:
        logger.info(f"🧠 INDIVIDUAL_COACH: Memory disabled for {coach_name}")
        graph = uncompiled_graph.compile()

    logger.info(
        f"✅ INDIVIDUAL_COACH: {coach_name} graph with reasoning compiled successfully"
    )
    return graph


def build_individual_coach_graph_with_reasoning(
    coach_name: str, agent_instance
) -> StateGraph:
    """
    Build the individual coach graph structure with reasoning node.

    Enhanced flow: START → reasoning → coach → END
    """
    logger.info(
        f"🏗️ INDIVIDUAL_COACH: Building graph structure with reasoning for {coach_name}"
    )

    async def individual_reasoning_node(
        state: OptimizedCoachingState,
    ) -> Dict[str, Any]:
        """
        Enhanced reasoning node for individual coaches that analyzes the user's request
        in the context of the specific coach domain.
        """
        logger.info(
            f"🧠 INDIVIDUAL_REASONING: [{coach_name}] Starting request analysis"
        )

        # Call the base reasoning node
        reasoning_result = await reasoning_node(state)

        # Add domain-specific context
        domain_context = {
            "current_node": "reasoning",
            "coach_context": coach_name,
            "execution_steps": ["reasoning"],
            "debug_info": {
                "node": "reasoning",
                "coach_domain": coach_name,
                "action": "domain_aware_analysis",
            },
        }

        # Combine reasoning result with domain context
        return {**reasoning_result, **domain_context}

    async def individual_coach_node(state: OptimizedCoachingState) -> Dict[str, Any]:
        """Individual coach node using domain agent directly (same as main coaching graph)."""
        logger.info(f"🏃‍♂️ INDIVIDUAL_COACH: [{coach_name}] Starting execution")

        if isinstance(state, dict):
            user_query = state.get("user_query", "")
            messages = state.get("messages", [])
            user_profile = state.get("user_profile", {})
            execution_steps = state.get("execution_steps", [])
        else:
            user_query = getattr(state, "user_query", "")
            messages = getattr(state, "messages", [])
            user_profile = getattr(state, "user_profile", {})
            execution_steps = getattr(state, "execution_steps", [])

        # Get the latest user message if user_query is empty
        if not user_query and messages:
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

        if not user_query:
            greeting_response = f"Hello! I'm your {coach_name.replace('_', ' ').title()}. I've analyzed your request and I'm ready to help. What specific aspect of {coach_name.replace('_coach', '')} would you like to work on?"
            return {
                "current_node": coach_name,
                "final_response": greeting_response,
                "messages": messages + [AIMessage(content=greeting_response)],
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {
                    "node": coach_name,
                    "action": "greeting_with_reasoning_context",
                },
            }

        try:
            # Create agent state (same format as main coaching graph)
            agent_state = {
                "user_query": user_query,
                "messages": messages,
                "user_profile": user_profile,
            }

            logger.info(
                f"🔄 INDIVIDUAL_COACH: [{coach_name}] Executing with reasoning context"
            )

            # Execute the domain agent (same as main coaching graph)
            result = await agent_instance.process(agent_state)

            final_answer = result.get("response", "I'd be happy to help!")

            # Ensure final response is a string
            if not isinstance(final_answer, str):
                import json

                final_answer = json.dumps(final_answer)

            logger.info(
                f"✅ INDIVIDUAL_COACH: [{coach_name}] Generated response with reasoning context"
            )

            return {
                "current_node": coach_name,
                "final_response": final_answer,
                "messages": messages + [AIMessage(content=final_answer)],
                "coach_responses": {coach_name: final_answer},
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {
                    "node": coach_name,
                    "success": str(result.get("success", True)),
                    "action": "individual_coach_complete_with_reasoning",
                    "reasoning_context": "analyzed",
                },
            }

        except Exception as e:
            logger.error(f"❌ INDIVIDUAL_COACH: [{coach_name}] Error: {e}")
            error_response = "I apologize, but I encountered an error while processing your request. Please try again."
            return {
                "current_node": coach_name,
                "final_response": error_response,
                "messages": messages + [AIMessage(content=error_response)],
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {
                    "node": coach_name,
                    "error": str(e),
                    "action": "error_fallback_with_reasoning",
                },
            }

    # Build the graph - Enhanced flow: START → reasoning → coach → END
    builder = StateGraph(OptimizedCoachingState)

    # Add the reasoning node first
    builder.add_node("reasoning", individual_reasoning_node)

    # Add the coach node
    builder.add_node(coach_name, individual_coach_node)

    # Define enhanced flow with reasoning
    builder.add_edge(START, "reasoning")
    builder.add_edge("reasoning", coach_name)
    builder.add_edge(coach_name, END)

    logger.info(
        f"✅ INDIVIDUAL_COACH: {coach_name} graph structure with reasoning built successfully"
    )
    logger.info(
        f"📊 INDIVIDUAL_COACH: Enhanced flow: START → reasoning → {coach_name} → END"
    )

    return builder


# Factory functions for each coach (for langgraph.json)
async def create_strength_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create strength coach graph with reasoning."""
    return await create_individual_coach_graph("strength_coach", config)


async def create_cardio_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create cardio coach graph with reasoning."""
    return await create_individual_coach_graph("cardio_coach", config)


async def create_cycling_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create cycling coach graph with reasoning."""
    return await create_individual_coach_graph("cycling_coach", config)


async def create_nutrition_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create nutrition coach graph with reasoning."""
    return await create_individual_coach_graph("nutrition_coach", config)


async def create_recovery_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create recovery coach graph with reasoning."""
    return await create_individual_coach_graph("recovery_coach", config)


async def create_mental_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create mental coach graph with reasoning."""
    return await create_individual_coach_graph("mental_coach", config)


# Factory function for LangGraph server (matches expected signature)
def create_individual_coach_graph_factory(config=None):
    """
    Factory function for LangGraph server that creates an individual coach graph.

    This function matches LangGraph's expected signature (RunnableConfig parameter).
    For the individual_graph, we'll default to strength_coach.

    Args:
        config: RunnableConfig object containing configuration

    Returns:
        Compiled graph for the individual coach
    """
    import asyncio
    from langchain_core.runnables.config import RunnableConfig

    # Extract configuration values
    if config and hasattr(config, "configurable"):
        coach_type = config.configurable.get("coach_type", "strength_coach")
        user_id = config.configurable.get("user_id", "studio_user")
        enable_memory = config.configurable.get("enable_memory", False)

        graph_config = {"user_id": user_id, "enable_memory": enable_memory}
    else:
        # Default configuration
        coach_type = "strength_coach"
        graph_config = {"user_id": "studio_user", "enable_memory": False}

    # Create the graph asynchronously
    async def _create_graph():
        return await create_individual_coach_graph(coach_type, graph_config)

    # Run the async function
    try:
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(_create_graph())
    except RuntimeError:
        # If no event loop is running, create a new one
        return asyncio.run(_create_graph())
