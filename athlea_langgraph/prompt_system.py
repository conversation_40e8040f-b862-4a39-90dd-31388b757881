"""
Unified Prompt System for Athlea LangGraph
Handles LangGraph runtime, JSON configs, and LangSmith compatibility
"""

import json
import re
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Union
from pathlib import Path


@dataclass
class PromptMetadata:
    """Metadata for prompt versioning and tracking"""
    name: str
    version: str
    description: str
    author: str
    created_at: str
    updated_at: str
    prompt_type: str
    tags: List[str]
    changelog: List[Dict[str, Any]]
    deprecated: bool = False
    experimental: bool = False
    tested_in_langsmith: bool = False


@dataclass 
class PromptVariables:
    """LLM generation parameters"""
    temperature: float = 0.7
    max_tokens: int = 4000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop_sequences: List[str] = None

    def __post_init__(self):
        if self.stop_sequences is None:
            self.stop_sequences = []


@dataclass
class PromptTemplate:
    """Core prompt template with variable mapping"""
    system: str
    context_template: Optional[str] = None
    user_template: Optional[str] = None
    examples: List[Dict[str, str]] = None
    instructions: Optional[str] = None
    constraints: List[str] = None

    def __post_init__(self):
        if self.examples is None:
            self.examples = []
        if self.constraints is None:
            self.constraints = []


class PromptRegistry:
    """Unified prompt management system"""
    
    # Variable mapping between different systems
    VARIABLE_MAPPINGS = {
        # LangGraph format → LangSmith format
        "langraph_to_langsmith": {
            "{user_profile_info}": "{{userProfileInfo}}",
            "{current_plan_info}": "{{currentPlanInfo}}",
            "{message_history}": "{{messageHistory}}",
            "{latest_user_message_content}": "{{latestUserMessage}}",
            "{user_context}": "{{userContext}}",
            "{conversation_context}": "{{conversationContext}}"
        },
        # LangSmith format → LangGraph format  
        "langsmith_to_langraph": {
            "{{userProfileInfo}}": "{user_profile_info}",
            "{{currentPlanInfo}}": "{current_plan_info}",
            "{{messageHistory}}": "{message_history}",
            "{{latestUserMessage}}": "{latest_user_message_content}",
            "{{userContext}}": "{user_context}",
            "{{conversationContext}}": "{conversation_context}"
        }
    }

    def __init__(self, prompts_dir: str = "athlea_langgraph/prompts"):
        self.prompts_dir = Path(prompts_dir)
        self.prompts: Dict[str, 'UnifiedPrompt'] = {}
        self._load_existing_prompts()

    def _load_existing_prompts(self):
        """Load existing JSON prompts and convert them"""
        coaches_dir = self.prompts_dir / "coaches"
        if coaches_dir.exists():
            for json_file in coaches_dir.glob("*.json"):
                try:
                    prompt = self._load_from_json(json_file)
                    self.prompts[prompt.metadata.name] = prompt
                except Exception as e:
                    print(f"Warning: Could not load {json_file}: {e}")

    def _load_from_json(self, json_path: Path) -> 'UnifiedPrompt':
        """Convert existing JSON format to unified format"""
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convert metadata
        metadata = PromptMetadata(
            name=data["metadata"]["name"],
            version=data["metadata"]["version"],
            description=data["metadata"]["description"],
            author=data["metadata"]["author"],
            created_at=data["metadata"]["created_at"],
            updated_at=data["metadata"]["updated_at"],
            prompt_type=data["metadata"]["prompt_type"],
            tags=data["metadata"]["tags"],
            changelog=data["metadata"]["changelog"],
            deprecated=data["metadata"].get("deprecated", False),
            experimental=data["metadata"].get("experimental", False)
        )

        # Convert prompt template
        prompt_data = data["prompt"]
        template = PromptTemplate(
            system=prompt_data["system"],
            context_template=prompt_data.get("context_template"),
            user_template=prompt_data.get("user_template"),
            examples=prompt_data.get("examples", []),
            instructions=prompt_data.get("instructions"),
            constraints=prompt_data.get("constraints", [])
        )

        # Convert variables
        vars_data = data.get("variables", {})
        variables = PromptVariables(
            temperature=vars_data.get("temperature", 0.7),
            max_tokens=vars_data.get("max_tokens", 4000),
            top_p=vars_data.get("top_p", 1.0),
            frequency_penalty=vars_data.get("frequency_penalty", 0.0),
            presence_penalty=vars_data.get("presence_penalty", 0.0),
            stop_sequences=vars_data.get("stop_sequences", [])
        )

        return UnifiedPrompt(metadata, template, variables)

    def get_prompt(self, name: str) -> 'UnifiedPrompt':
        """Get a prompt by name"""
        if name not in self.prompts:
            raise ValueError(f"Prompt '{name}' not found")
        return self.prompts[name]

    def register_prompt(self, prompt: 'UnifiedPrompt'):
        """Register a new prompt"""
        self.prompts[prompt.metadata.name] = prompt

    def get_langraph_format(self, name: str, **kwargs) -> str:
        """Get prompt formatted for LangGraph (Python .format())"""
        prompt = self.get_prompt(name)
        return prompt.template.system.format(**kwargs)

    def get_langsmith_format(self, name: str) -> str:
        """Get prompt formatted for LangSmith (Mustache templates)"""
        prompt = self.get_prompt(name)
        langsmith_prompt = prompt.template.system
        
        # Convert variables to LangSmith format
        for langraph_var, langsmith_var in self.VARIABLE_MAPPINGS["langraph_to_langsmith"].items():
            langsmith_prompt = langsmith_prompt.replace(langraph_var, langsmith_var)
        
        return langsmith_prompt

    def save_to_json(self, name: str, output_path: Optional[Path] = None):
        """Save prompt back to JSON format (backward compatibility)"""
        prompt = self.get_prompt(name)
        
        if output_path is None:
            output_path = self.prompts_dir / "coaches" / f"{name}.json"

        json_data = {
            "metadata": asdict(prompt.metadata),
            "prompt": asdict(prompt.template),
            "variables": asdict(prompt.variables),
            "validation": {
                "required_context": [],
                "max_length": 10000,
                "min_length": 50,
                "required_fields": [],
                "allowed_variables": []
            }
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

    def export_for_langsmith(self, name: str, output_file: Optional[str] = None) -> str:
        """Export prompt for LangSmith testing"""
        langsmith_prompt = self.get_langsmith_format(name)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(langsmith_prompt)
            print(f"✅ Exported {name} to {output_file} for LangSmith")
        
        return langsmith_prompt

    def import_from_langsmith(self, name: str, langsmith_content: str, version_bump: str = "patch"):
        """Import optimized prompt back from LangSmith"""
        if name not in self.prompts:
            raise ValueError(f"Base prompt '{name}' not found")
        
        original_prompt = self.prompts[name]
        
        # Convert LangSmith variables back to LangGraph format
        langraph_content = langsmith_content
        for langsmith_var, langraph_var in self.VARIABLE_MAPPINGS["langsmith_to_langraph"].items():
            langraph_content = langraph_content.replace(langsmith_var, langraph_var)
        
        # Create new version
        new_version = self._bump_version(original_prompt.metadata.version, version_bump)
        
        # Update metadata
        new_metadata = PromptMetadata(
            name=original_prompt.metadata.name,
            version=new_version,
            description=original_prompt.metadata.description + " (LangSmith optimized)",
            author=original_prompt.metadata.author,
            created_at=original_prompt.metadata.created_at,
            updated_at=datetime.now().isoformat(),
            prompt_type=original_prompt.metadata.prompt_type,
            tags=original_prompt.metadata.tags + ["langsmith_optimized"],
            changelog=original_prompt.metadata.changelog + [{
                "version": new_version,
                "date": datetime.now().isoformat(),
                "changes": "Imported optimized version from LangSmith testing",
                "author": "LangSmith + Human",
                "tested_in_langsmith": True
            }],
            deprecated=False,
            experimental=False,
            tested_in_langsmith=True
        )

        # Create new template with updated content
        new_template = PromptTemplate(
            system=langraph_content,
            context_template=original_prompt.template.context_template,
            user_template=original_prompt.template.user_template,
            examples=original_prompt.template.examples,
            instructions=original_prompt.template.instructions,
            constraints=original_prompt.template.constraints
        )

        # Create updated prompt
        updated_prompt = UnifiedPrompt(new_metadata, new_template, original_prompt.variables)
        self.prompts[name] = updated_prompt
        
        print(f"✅ Imported LangSmith optimizations for {name} (v{new_version})")

    def _bump_version(self, current_version: str, bump_type: str) -> str:
        """Bump version number"""
        try:
            major, minor, patch = map(int, current_version.split('.'))
            
            if bump_type == "major":
                return f"{major + 1}.0.0"
            elif bump_type == "minor":
                return f"{major}.{minor + 1}.0"
            else:  # patch
                return f"{major}.{minor}.{patch + 1}"
        except:
            return f"{current_version}_updated"


@dataclass
class UnifiedPrompt:
    """Complete prompt with metadata, template, and variables"""
    metadata: PromptMetadata
    template: PromptTemplate
    variables: PromptVariables

    def format_for_langraph(self, **kwargs) -> str:
        """Format prompt for LangGraph execution"""
        return self.template.system.format(**kwargs)

    def to_langsmith_format(self) -> str:
        """Convert to LangSmith format"""
        langsmith_prompt = self.template.system
        
        # Convert variables
        mappings = PromptRegistry.VARIABLE_MAPPINGS["langraph_to_langsmith"]
        for langraph_var, langsmith_var in mappings.items():
            langsmith_prompt = langsmith_prompt.replace(langraph_var, langsmith_var)
        
        return langsmith_prompt


# Global registry instance
prompt_registry = PromptRegistry() 