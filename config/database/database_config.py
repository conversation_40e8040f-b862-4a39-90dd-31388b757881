"""
Database Configuration for Different Environments
"""

import os
from typing import Optional


def get_postgres_connection_string() -> Optional[str]:
    """
    Get PostgreSQL connection string based on environment.

    Priority:
    1. POSTGRES_CONNECTION_STRING (direct override)
    2. Azure production database
    3. Local development database
    """

    # Direct override (for production/staging)
    direct_connection = os.getenv("POSTGRES_CONNECTION_STRING")
    if direct_connection:
        return direct_connection

    # Azure production database
    azure_host = os.getenv(
        "AZURE_POSTGRES_HOST", "athlea-langgraph-server.postgres.database.azure.com"
    )
    azure_user = os.getenv("AZURE_POSTGRES_USER", "michwilf")
    azure_password = os.getenv("AZURE_POSTGRES_PASSWORD")
    azure_db = os.getenv("AZURE_POSTGRES_DB", "postgres")

    if azure_password:
        # URL encode the password for special characters
        import urllib.parse

        encoded_password = urllib.parse.quote(azure_password)
        return f"postgresql://{azure_user}:{encoded_password}@{azure_host}:5432/{azure_db}?sslmode=require"

    # Local development fallback
    local_connection = os.getenv("LOCAL_POSTGRES_CONNECTION_STRING")
    if local_connection:
        return local_connection

    # Default local development
    return "postgresql://postgres:postgres@localhost:5432/athlea_coaching"


def get_checkpointer():
    """Get the appropriate checkpointer based on environment."""
    from langgraph.checkpoint.memory import MemorySaver

    try:
        from langgraph.checkpoint.postgres import PostgresSaver

        connection_string = get_postgres_connection_string()

        if connection_string and not connection_string.startswith(
            "postgresql://postgres:postgres@localhost"
        ):
            # Use PostgreSQL for production/Azure
            print(f"🗄️ Setting up PostgreSQL checkpointer...")

            # Test the connection first
            try:
                # Create a temporary checkpointer to test connection
                with PostgresSaver.from_conn_string(
                    connection_string
                ) as temp_checkpointer:
                    # Try to setup tables (this will test the connection)
                    temp_checkpointer.setup()
                    print("✅ PostgreSQL connection and setup successful!")

                # Return the actual checkpointer
                return PostgresSaver.from_conn_string(connection_string)

            except Exception as conn_error:
                print(f"❌ PostgreSQL connection failed: {conn_error}")
                if "timeout" in str(conn_error).lower():
                    print(
                        "💡 This looks like a firewall issue - check Azure networking settings"
                    )
                print("⚠️ Falling back to MemorySaver")
                return MemorySaver()
        else:
            # Use memory for local development
            print("⚠️ Using MemorySaver for local development")
            return MemorySaver()

    except ImportError:
        print("⚠️ PostgreSQL checkpointer not available, using MemorySaver")
        return MemorySaver()
    except Exception as e:
        print(f"⚠️ PostgreSQL setup failed ({e}), using MemorySaver")
        return MemorySaver()


# Environment detection
def is_production() -> bool:
    """Check if running in production environment."""
    return (
        os.getenv("ENVIRONMENT") == "production"
        or os.getenv("AZURE_CLIENT_ID") is not None
    )


def is_development() -> bool:
    """Check if running in development environment."""
    return not is_production()
